/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.EventPriority
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.ItemStack
 */
package com.apexdungeons.listeners;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.mobs.MobSpawnPoint;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

public class MobSpawnToolListener
implements Listener {
    private final ApexDungeons plugin;

    public MobSpawnToolListener(ApexDungeons plugin) {
        this.plugin = plugin;
        plugin.getLogger().info("[MobSpawnToolListener] Initialized mob spawn tool listener");
    }

    @EventHandler(priority=EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        if (item != null && event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            this.plugin.getLogger().info("[MobSpawnToolListener] Player " + player.getName() + " right-clicked with " + String.valueOf(item.getType()));
        }
        if (item == null || event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        if (event.getClickedBlock() == null) {
            return;
        }
        boolean isMobTool = this.plugin.getMobSpawnTool().isMobSpawnTool(item);
        boolean isBossTool = this.plugin.getMobSpawnTool().isBossSpawnTool(item);
        this.plugin.getLogger().info("[MobSpawnToolListener] Tool check - isMobTool: " + isMobTool + ", isBossTool: " + isBossTool);
        if (!isMobTool && !isBossTool) {
            return;
        }
        event.setCancelled(true);
        this.plugin.getLogger().info("[MobSpawnToolListener] Mob spawn tool detected, processing...");
        Location clickedLocation = event.getClickedBlock().getLocation().add(0.0, 1.0, 0.0);
        MobSpawnPoint existing = this.plugin.getMobSpawnManager().getSpawnPoint(clickedLocation);
        if (existing != null) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u26a0 There's already a " + (existing.isBoss() ? "boss" : "mob") + " spawn point here!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Mob: " + existing.getMobName() + ", Radius: " + existing.getRadius());
            this.showSpawnPointParticles(clickedLocation, existing.isBoss());
            return;
        }
        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.mobspawn")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to place spawn points!");
            this.plugin.getLogger().warning("[MobSpawnToolListener] Player " + player.getName() + " lacks permission for mob spawn tools");
            return;
        }
        if (isMobTool) {
            this.handleMobSpawnTool(player, clickedLocation);
        } else {
            this.handleBossSpawnTool(player, clickedLocation);
        }
    }

    private void handleMobSpawnTool(Player player, Location location) {
        this.plugin.getLogger().info("[MobSpawnToolListener] Handling mob spawn tool for " + player.getName());
        String mobName = this.plugin.getMobSpawnData().getPlayerMobSelection(player.getUniqueId());
        this.plugin.getLogger().info("[MobSpawnToolListener] Player mob selection: " + mobName);
        if (mobName == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u274c No mob configured!");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.AQUA) + "/dgn mobspawn set <mob_name>" + String.valueOf(ChatColor.YELLOW) + " first");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Available mobs: " + String.join((CharSequence)", ", this.plugin.getMobAdapter().getAvailableMobs()));
            return;
        }
        double radius = this.plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 5.0);
        this.plugin.getLogger().info("[MobSpawnToolListener] Creating mob spawn point: " + mobName + " at " + String.valueOf(location) + " with radius " + radius);
        try {
            this.plugin.getMobSpawnManager().addMobSpawnPoint(location, mobName, radius);
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Mob spawn point created!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Mob: " + String.valueOf(ChatColor.WHITE) + mobName);
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Radius: " + String.valueOf(ChatColor.WHITE) + radius + " blocks");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Location: " + String.valueOf(ChatColor.WHITE) + location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83e\uddea Testing spawn immediately...");
            boolean testResult = this.plugin.getMobSpawnManager().testMobSpawn(location, mobName, false);
            if (testResult) {
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Test spawn successful!");
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Test spawn failed - check console for details");
            }
            this.showSpawnPointParticles(location, false);
            player.playSound(location, Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 1.0f);
            this.plugin.getLogger().info("[MobSpawnToolListener] Successfully created mob spawn point");
        }
        catch (Exception e) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Error creating spawn point: " + e.getMessage());
            this.plugin.getLogger().severe("[MobSpawnToolListener] Error creating mob spawn point: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void handleBossSpawnTool(Player player, Location location) {
        this.plugin.getLogger().info("[MobSpawnToolListener] Handling boss spawn tool for " + player.getName());
        String bossName = this.plugin.getMobSpawnData().getPlayerBossSelection(player.getUniqueId());
        this.plugin.getLogger().info("[MobSpawnToolListener] Player boss selection: " + bossName);
        if (bossName == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u274c No boss configured!");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.AQUA) + "/dgn bossspawn set <boss_name>" + String.valueOf(ChatColor.YELLOW) + " first");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Available bosses: " + String.join((CharSequence)", ", this.plugin.getMobAdapter().getAvailableBosses()));
            return;
        }
        double radius = this.plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 7.0);
        this.plugin.getLogger().info("[MobSpawnToolListener] Creating boss spawn point: " + bossName + " at " + String.valueOf(location) + " with radius " + radius);
        try {
            this.plugin.getMobSpawnManager().addBossSpawnPoint(location, bossName, radius);
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Boss spawn point created!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Boss: " + String.valueOf(ChatColor.WHITE) + bossName);
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Radius: " + String.valueOf(ChatColor.WHITE) + radius + " blocks");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Location: " + String.valueOf(ChatColor.WHITE) + location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83e\uddea Testing boss spawn immediately...");
            boolean testResult = this.plugin.getMobSpawnManager().testMobSpawn(location, bossName, true);
            if (testResult) {
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Test boss spawn successful!");
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Test boss spawn failed - check console for details");
            }
            this.showSpawnPointParticles(location, true);
            player.playSound(location, Sound.ENTITY_WITHER_SPAWN, 0.5f, 1.0f);
            this.plugin.getLogger().info("[MobSpawnToolListener] Successfully created boss spawn point");
        }
        catch (Exception e) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Error creating boss spawn point: " + e.getMessage());
            this.plugin.getLogger().severe("[MobSpawnToolListener] Error creating boss spawn point: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void showSpawnPointParticles(Location location, boolean isBoss) {
        try {
            Location particleLoc = location.clone().add(0.5, 0.5, 0.5);
            if (isBoss) {
                location.getWorld().spawnParticle(Particle.LARGE_SMOKE, particleLoc, 20, 0.5, 0.5, 0.5, 0.02);
                location.getWorld().spawnParticle(Particle.FLAME, particleLoc, 10, 0.3, 0.3, 0.3, 0.01);
            } else {
                location.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, particleLoc, 15, 0.5, 0.5, 0.5, 0.0);
                location.getWorld().spawnParticle(Particle.ENCHANT, particleLoc, 10, 0.3, 0.3, 0.3, 1.0);
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("[MobSpawnToolListener] Error showing particles: " + e.getMessage());
        }
    }
}

