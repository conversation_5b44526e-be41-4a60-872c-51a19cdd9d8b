/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.EnhancedMainGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class SettingsGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.YELLOW) + "\u2699 Settings & Preferences";
    private static final Map<UUID, PlayerSettings> playerSettings = new HashMap<UUID, PlayerSettings>();

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        SettingsGUI.fillBackground(inv);
        SettingsGUI.createSettingsOptions(inv, player, plugin);
        SettingsGUI.createNavigationButtons(inv);
        player.openInventory(inv);
        SettingsGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.ORANGE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createSettingsOptions(Inventory inv, Player player, ApexDungeons plugin) {
        PlayerSettings settings = SettingsGUI.getPlayerSettings(player);
        ItemStack visualEffects = new ItemStack(settings.visualEffects ? Material.LIME_DYE : Material.GRAY_DYE);
        ItemMeta visualMeta = visualEffects.getItemMeta();
        visualMeta.setDisplayName(String.valueOf(ChatColor.LIGHT_PURPLE) + "\u2728 Visual Effects");
        ArrayList<Object> visualLore = new ArrayList<Object>();
        visualLore.add(String.valueOf(ChatColor.GRAY) + "Toggle particle effects and");
        visualLore.add(String.valueOf(ChatColor.GRAY) + "visual enhancements");
        visualLore.add("");
        visualLore.add(String.valueOf(ChatColor.YELLOW) + "Status: " + (settings.visualEffects ? String.valueOf(ChatColor.GREEN) + "Enabled" : String.valueOf(ChatColor.RED) + "Disabled"));
        visualLore.add("");
        visualLore.add(String.valueOf(ChatColor.GRAY) + "Effects include:");
        visualLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Portal particles");
        visualLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Creation celebrations");
        visualLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Completion fireworks");
        visualLore.add("");
        visualLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to toggle!");
        visualMeta.setLore(visualLore);
        visualEffects.setItemMeta(visualMeta);
        inv.setItem(11, visualEffects);
        ItemStack soundEffects = new ItemStack(settings.soundEffects ? Material.NOTE_BLOCK : Material.BARRIER);
        ItemMeta soundMeta = soundEffects.getItemMeta();
        soundMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udd0a Sound Effects");
        ArrayList<Object> soundLore = new ArrayList<Object>();
        soundLore.add(String.valueOf(ChatColor.GRAY) + "Toggle sound effects and");
        soundLore.add(String.valueOf(ChatColor.GRAY) + "audio notifications");
        soundLore.add("");
        soundLore.add(String.valueOf(ChatColor.YELLOW) + "Status: " + (settings.soundEffects ? String.valueOf(ChatColor.GREEN) + "Enabled" : String.valueOf(ChatColor.RED) + "Disabled"));
        soundLore.add("");
        soundLore.add(String.valueOf(ChatColor.GRAY) + "Sounds include:");
        soundLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Portal travel sounds");
        soundLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Dungeon completion chimes");
        soundLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Ambient dungeon atmosphere");
        soundLore.add("");
        soundLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to toggle!");
        soundMeta.setLore(soundLore);
        soundEffects.setItemMeta(soundMeta);
        inv.setItem(13, soundEffects);
        ItemStack chatNotifications = new ItemStack(settings.chatNotifications ? Material.WRITABLE_BOOK : Material.BOOK);
        ItemMeta chatMeta = chatNotifications.getItemMeta();
        chatMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udcac Chat Notifications");
        ArrayList<Object> chatLore = new ArrayList<Object>();
        chatLore.add(String.valueOf(ChatColor.GRAY) + "Toggle chat messages and");
        chatLore.add(String.valueOf(ChatColor.GRAY) + "status notifications");
        chatLore.add("");
        chatLore.add(String.valueOf(ChatColor.YELLOW) + "Status: " + (settings.chatNotifications ? String.valueOf(ChatColor.GREEN) + "Enabled" : String.valueOf(ChatColor.RED) + "Disabled"));
        chatLore.add("");
        chatLore.add(String.valueOf(ChatColor.GRAY) + "Notifications include:");
        chatLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Dungeon creation updates");
        chatLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Portal travel messages");
        chatLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Achievement unlocks");
        chatLore.add("");
        chatLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to toggle!");
        chatMeta.setLore(chatLore);
        chatNotifications.setItemMeta(chatMeta);
        inv.setItem(15, chatNotifications);
        ItemStack autoTeleport = new ItemStack(settings.autoTeleport ? Material.ENDER_PEARL : Material.ENDER_EYE);
        ItemMeta teleportMeta = autoTeleport.getItemMeta();
        teleportMeta.setDisplayName(String.valueOf(ChatColor.DARK_PURPLE) + "\ud83c\udf00 Auto-Teleport");
        ArrayList<Object> teleportLore = new ArrayList<Object>();
        teleportLore.add(String.valueOf(ChatColor.GRAY) + "Automatically teleport to");
        teleportLore.add(String.valueOf(ChatColor.GRAY) + "dungeons after creation");
        teleportLore.add("");
        teleportLore.add(String.valueOf(ChatColor.YELLOW) + "Status: " + (settings.autoTeleport ? String.valueOf(ChatColor.GREEN) + "Enabled" : String.valueOf(ChatColor.RED) + "Disabled"));
        teleportLore.add("");
        teleportLore.add(String.valueOf(ChatColor.GRAY) + "When enabled, you'll be");
        teleportLore.add(String.valueOf(ChatColor.GRAY) + "automatically transported to");
        teleportLore.add(String.valueOf(ChatColor.GRAY) + "your dungeon once it's ready");
        teleportLore.add("");
        teleportLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to toggle!");
        teleportMeta.setLore(teleportLore);
        autoTeleport.setItemMeta(teleportMeta);
        inv.setItem(29, autoTeleport);
        ItemStack difficulty = new ItemStack(SettingsGUI.getDifficultyMaterial(settings.preferredDifficulty));
        ItemMeta difficultyMeta = difficulty.getItemMeta();
        difficultyMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2694 Preferred Difficulty");
        ArrayList<Object> difficultyLore = new ArrayList<Object>();
        difficultyLore.add(String.valueOf(ChatColor.GRAY) + "Set your preferred dungeon");
        difficultyLore.add(String.valueOf(ChatColor.GRAY) + "difficulty level");
        difficultyLore.add("");
        difficultyLore.add(String.valueOf(ChatColor.YELLOW) + "Current: " + String.valueOf(ChatColor.WHITE) + settings.preferredDifficulty);
        difficultyLore.add("");
        difficultyLore.add(String.valueOf(ChatColor.GRAY) + "Available difficulties:");
        difficultyLore.add(String.valueOf(ChatColor.GREEN) + "\u2022 Easy" + String.valueOf(ChatColor.GRAY) + " - Relaxed exploration");
        difficultyLore.add(String.valueOf(ChatColor.YELLOW) + "\u2022 Medium" + String.valueOf(ChatColor.GRAY) + " - Balanced challenge");
        difficultyLore.add(String.valueOf(ChatColor.GOLD) + "\u2022 Hard" + String.valueOf(ChatColor.GRAY) + " - Serious challenge");
        difficultyLore.add(String.valueOf(ChatColor.RED) + "\u2022 Expert" + String.valueOf(ChatColor.GRAY) + " - Ultimate test");
        difficultyLore.add("");
        difficultyLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to cycle!");
        difficultyMeta.setLore(difficultyLore);
        difficulty.setItemMeta(difficultyMeta);
        inv.setItem(31, difficulty);
        ItemStack theme = new ItemStack(SettingsGUI.getThemeMaterial(settings.preferredTheme));
        ItemMeta themeMeta = theme.getItemMeta();
        themeMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83c\udfa8 Preferred Theme");
        ArrayList<Object> themeLore = new ArrayList<Object>();
        themeLore.add(String.valueOf(ChatColor.GRAY) + "Set your favorite dungeon");
        themeLore.add(String.valueOf(ChatColor.GRAY) + "theme for quick access");
        themeLore.add("");
        themeLore.add(String.valueOf(ChatColor.YELLOW) + "Current: " + String.valueOf(ChatColor.WHITE) + settings.preferredTheme);
        themeLore.add("");
        themeLore.add(String.valueOf(ChatColor.GRAY) + "Available themes:");
        themeLore.add(String.valueOf(ChatColor.GRAY) + "\u2022 Castle" + String.valueOf(ChatColor.GRAY) + " - Medieval fortresses");
        themeLore.add(String.valueOf(ChatColor.DARK_GRAY) + "\u2022 Cave" + String.valueOf(ChatColor.GRAY) + " - Underground systems");
        themeLore.add(String.valueOf(ChatColor.GOLD) + "\u2022 Temple" + String.valueOf(ChatColor.GRAY) + " - Ancient ruins");
        themeLore.add("");
        themeLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to cycle!");
        themeMeta.setLore(themeLore);
        theme.setItemMeta(themeMeta);
        inv.setItem(33, theme);
    }

    private static void createNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2190 Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack reset = new ItemStack(Material.BARRIER);
        ItemMeta resetMeta = reset.getItemMeta();
        resetMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\ud83d\udd04 Reset to Defaults");
        ArrayList<Object> resetLore = new ArrayList<Object>();
        resetLore.add(String.valueOf(ChatColor.GRAY) + "Reset all settings to");
        resetLore.add(String.valueOf(ChatColor.GRAY) + "their default values");
        resetLore.add("");
        resetLore.add(String.valueOf(ChatColor.RED) + "\u26a0 This cannot be undone!");
        resetMeta.setLore(resetLore);
        reset.setItemMeta(resetMeta);
        inv.setItem(49, reset);
        ItemStack save = new ItemStack(Material.EMERALD);
        ItemMeta saveMeta = save.getItemMeta();
        saveMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udcbe Save Settings");
        ArrayList<CallSite> saveLore = new ArrayList<CallSite>();
        saveLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Save your current settings")));
        saveLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "and apply changes")));
        saveMeta.setLore(saveLore);
        save.setItemMeta(saveMeta);
        inv.setItem(53, save);
    }

    private static PlayerSettings getPlayerSettings(Player player) {
        return playerSettings.computeIfAbsent(player.getUniqueId(), k -> new PlayerSettings());
    }

    private static Material getDifficultyMaterial(String difficulty) {
        return switch (difficulty.toLowerCase()) {
            case "easy" -> Material.WOODEN_SWORD;
            case "medium" -> Material.IRON_SWORD;
            case "hard" -> Material.DIAMOND_SWORD;
            case "expert" -> Material.NETHERITE_SWORD;
            default -> Material.IRON_SWORD;
        };
    }

    private static Material getThemeMaterial(String theme) {
        return switch (theme.toLowerCase()) {
            case "castle" -> Material.STONE_BRICKS;
            case "cave" -> Material.COBBLESTONE;
            case "temple" -> Material.SANDSTONE;
            default -> Material.STONE_BRICKS;
        };
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    PlayerSettings settings = SettingsGUI.getPlayerSettings(clicker);
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 11: {
                            settings.visualEffects = !settings.visualEffects;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        }
                        case 13: {
                            settings.soundEffects = !settings.soundEffects;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        }
                        case 15: {
                            settings.chatNotifications = !settings.chatNotifications;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        }
                        case 29: {
                            settings.autoTeleport = !settings.autoTeleport;
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        }
                        case 31: {
                            settings.cycleDifficulty();
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        }
                        case 33: {
                            settings.cycleTheme();
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            break;
                        }
                        case 45: {
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        }
                        case 49: {
                            playerSettings.put(clicker.getUniqueId(), new PlayerSettings());
                            clicker.closeInventory();
                            SettingsGUI.open(clicker, plugin);
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Settings reset to defaults!");
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Settings saved successfully!");
                            EnhancedMainGUI.open(clicker, plugin);
                        }
                    }
                }
            }
        }, (Plugin)plugin);
    }

    public static class PlayerSettings {
        public boolean visualEffects = true;
        public boolean soundEffects = true;
        public boolean chatNotifications = true;
        public boolean autoTeleport = false;
        public String preferredDifficulty = "Medium";
        public String preferredTheme = "Castle";

        public void cycleDifficulty() {
            this.preferredDifficulty = switch (this.preferredDifficulty) {
                case "Easy" -> "Medium";
                case "Medium" -> "Hard";
                case "Hard" -> "Expert";
                case "Expert" -> "Easy";
                default -> "Medium";
            };
        }

        public void cycleTheme() {
            this.preferredTheme = switch (this.preferredTheme) {
                case "Castle" -> "Cave";
                case "Cave" -> "Temple";
                case "Temple" -> "Castle";
                default -> "Castle";
            };
        }
    }
}

