/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.configuration.file.YamlConfiguration
 */
package com.apexdungeons.mobs;

import com.apexdungeons.ApexDungeons;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.configuration.file.YamlConfiguration;

public class MobSpawnData {
    private final ApexDungeons plugin;
    private final Map<UUID, String> playerMobSelections = new HashMap<UUID, String>();
    private final Map<UUID, String> playerBossSelections = new HashMap<UUID, String>();
    private final Map<UUID, Double> playerRadiusSettings = new HashMap<UUID, Double>();

    public MobSpawnData(ApexDungeons plugin) {
        this.plugin = plugin;
        this.loadData();
    }

    public void setPlayerMobSelection(UUID playerId, String mobName) {
        this.playerMobSelections.put(playerId, mobName);
        this.saveData();
    }

    public String getPlayerMobSelection(UUID playerId) {
        return this.playerMobSelections.get(playerId);
    }

    public void setPlayerBossSelection(UUID playerId, String bossName) {
        this.playerBossSelections.put(playerId, bossName);
        this.saveData();
    }

    public String getPlayerBossSelection(UUID playerId) {
        return this.playerBossSelections.get(playerId);
    }

    public void setPlayerRadius(UUID playerId, double radius) {
        this.playerRadiusSettings.put(playerId, radius);
        this.saveData();
    }

    public double getPlayerRadius(UUID playerId, double defaultRadius) {
        return this.playerRadiusSettings.getOrDefault(playerId, defaultRadius);
    }

    public void clearPlayerMobSelection(UUID playerId) {
        this.playerMobSelections.remove(playerId);
        this.saveData();
    }

    public void clearPlayerBossSelection(UUID playerId) {
        this.playerBossSelections.remove(playerId);
        this.saveData();
    }

    private void loadData() {
        UUID playerId;
        File file = new File(this.plugin.getDataFolder(), "mob_spawn_data.yml");
        if (!file.exists()) {
            return;
        }
        YamlConfiguration config = YamlConfiguration.loadConfiguration((File)file);
        if (config.contains("mob_selections")) {
            for (String key : config.getConfigurationSection("mob_selections").getKeys(false)) {
                try {
                    playerId = UUID.fromString(key);
                    String mobName = config.getString("mob_selections." + key);
                    this.playerMobSelections.put(playerId, mobName);
                }
                catch (Exception e) {
                    this.plugin.getLogger().warning("Failed to load mob selection for " + key + ": " + e.getMessage());
                }
            }
        }
        if (config.contains("boss_selections")) {
            for (String key : config.getConfigurationSection("boss_selections").getKeys(false)) {
                try {
                    playerId = UUID.fromString(key);
                    String bossName = config.getString("boss_selections." + key);
                    this.playerBossSelections.put(playerId, bossName);
                }
                catch (Exception e) {
                    this.plugin.getLogger().warning("Failed to load boss selection for " + key + ": " + e.getMessage());
                }
            }
        }
        if (config.contains("radius_settings")) {
            for (String key : config.getConfigurationSection("radius_settings").getKeys(false)) {
                try {
                    playerId = UUID.fromString(key);
                    double radius = config.getDouble("radius_settings." + key);
                    this.playerRadiusSettings.put(playerId, radius);
                }
                catch (Exception e) {
                    this.plugin.getLogger().warning("Failed to load radius setting for " + key + ": " + e.getMessage());
                }
            }
        }
        this.plugin.getLogger().info("Loaded mob spawn data for " + this.playerMobSelections.size() + " mob selections, " + this.playerBossSelections.size() + " boss selections, " + this.playerRadiusSettings.size() + " radius settings");
    }

    private void saveData() {
        File file = new File(this.plugin.getDataFolder(), "mob_spawn_data.yml");
        YamlConfiguration config = new YamlConfiguration();
        for (Map.Entry<UUID, String> entry : this.playerMobSelections.entrySet()) {
            config.set("mob_selections." + entry.getKey().toString(), (Object)entry.getValue());
        }
        for (Map.Entry<UUID, String> entry : this.playerBossSelections.entrySet()) {
            config.set("boss_selections." + entry.getKey().toString(), (Object)entry.getValue());
        }
        for (Map.Entry<UUID, Object> entry : this.playerRadiusSettings.entrySet()) {
            config.set("radius_settings." + entry.getKey().toString(), entry.getValue());
        }
        try {
            config.save(file);
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save mob spawn data: " + e.getMessage());
        }
    }

    public void shutdown() {
        this.saveData();
        this.playerMobSelections.clear();
        this.playerBossSelections.clear();
        this.playerRadiusSettings.clear();
    }
}

