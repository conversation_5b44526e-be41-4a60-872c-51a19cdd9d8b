/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.CustomMobSelectionGUI;
import com.apexdungeons.gui.EnhancedMainGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class BuildingToolsGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_PURPLE) + "\ud83d\udd28 Building Tools - Made by Vexy";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        BuildingToolsGUI.fillBackground(inv);
        BuildingToolsGUI.createSchematicToolsSection(inv, plugin);
        BuildingToolsGUI.createEssentialBlocksSection(inv, plugin);
        BuildingToolsGUI.createMobToolsSection(inv, plugin);
        BuildingToolsGUI.createUtilityToolsSection(inv, plugin);
        BuildingToolsGUI.createNavigationButtons(inv);
        player.openInventory(inv);
        BuildingToolsGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createSchematicToolsSection(Inventory inv, ApexDungeons plugin) {
        ItemStack header = new ItemStack(Material.KNOWLEDGE_BOOK);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udcd0 Schematic Tools");
        ArrayList<Object> headerLore = new ArrayList<Object>();
        headerLore.add(String.valueOf(ChatColor.GRAY) + "Simple and reliable schematic placement");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.GREEN) + "\u2728 Features:");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Direct placement - no complex preview");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Each stick = one schematic");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click to place instantly");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Works with WorldEdit if available");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfaf Click sticks below to get them!");
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(4, header);
        ArrayList<ItemStack> sticks = new ArrayList<ItemStack>();
        try {
            ArrayList<String> names = new ArrayList<String>(plugin.getSchematicManager().getLoadedSchematicNames());
            if (names.isEmpty()) {
                String[] defaults;
                for (String n : defaults = new String[]{"DunStart", "DunEnd", "DunZombie1", "DunSkeleton1", "FireBoss", "MiniBossA", "ProDunRoom1", "ConI"}) {
                    sticks.add(plugin.getSimpleSchematicStick().createSchematicStick(n));
                }
            } else {
                names.sort(String::compareToIgnoreCase);
                for (String n : names) {
                    sticks.add(plugin.getSimpleSchematicStick().createSchematicStick(n));
                }
            }
        }
        catch (Exception ex) {
            plugin.getLogger().warning("[BuildingToolsGUI] Failed to prepare schematic sticks: " + ex.getMessage());
        }
        if (!sticks.isEmpty()) {
            int[] schematicSlots = new int[]{10, 11, 12, 13, 14, 15};
            for (int i = 0; i < Math.min(sticks.size(), schematicSlots.length); ++i) {
                inv.setItem(schematicSlots[i], (ItemStack)sticks.get(i));
            }
            if (sticks.size() > 6) {
                ItemStack moreButton = new ItemStack(Material.CHEST);
                ItemMeta moreMeta = moreButton.getItemMeta();
                moreMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\ud83d\udce6 More Schematics");
                ArrayList<Object> moreLore = new ArrayList<Object>();
                moreLore.add(String.valueOf(ChatColor.GRAY) + "Access all " + sticks.size() + " available schematics");
                moreLore.add("");
                moreLore.add(String.valueOf(ChatColor.GREEN) + "Click to get a pack of sticks!");
                moreMeta.setLore(moreLore);
                moreButton.setItemMeta(moreMeta);
                inv.setItem(16, moreButton);
            }
        } else {
            ItemStack fallback = new ItemStack(Material.STICK);
            ItemMeta fallbackMeta = fallback.getItemMeta();
            fallbackMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u26a0 No Schematics Found");
            ArrayList<CallSite> fallbackLore = new ArrayList<CallSite>();
            fallbackLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "No schematic files found in folder")));
            fallbackLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Add .schem files to /plugins/ApexDungeons/schematics/")));
            fallbackMeta.setLore(fallbackLore);
            fallback.setItemMeta(fallbackMeta);
            inv.setItem(13, fallback);
        }
    }

    private static List<ItemStack> createDefaultSchematicSticks() {
        String[] defaultSchematics;
        ArrayList<ItemStack> sticks = new ArrayList<ItemStack>();
        for (String schematicName : defaultSchematics = new String[]{"DunStart", "DunEnd", "DunZombie1", "DunSkeleton1", "FireBoss", "MiniBossA", "ProDunRoom1", "ConI"}) {
            ItemStack stick = new ItemStack(Material.STICK);
            ItemMeta meta = stick.getItemMeta();
            meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udcd0 " + schematicName);
            ArrayList<Object> lore = new ArrayList<Object>();
            lore.add(String.valueOf(ChatColor.GRAY) + "Schematic placement tool");
            lore.add("");
            lore.add(String.valueOf(ChatColor.YELLOW) + "Right-click: Place " + schematicName);
            lore.add(String.valueOf(ChatColor.GRAY) + "Simple and reliable placement");
            lore.add("");
            lore.add(String.valueOf(ChatColor.GREEN) + "Made by Vexy");
            meta.setLore(lore);
            stick.setItemMeta(meta);
            sticks.add(stick);
        }
        return sticks;
    }

    private static void createEssentialBlocksSection(Inventory inv, ApexDungeons plugin) {
        ItemStack header = new ItemStack(Material.BEACON);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf Essential Dungeon Blocks");
        ArrayList<Object> headerLore = new ArrayList<Object>();
        headerLore.add(String.valueOf(ChatColor.GRAY) + "Core blocks for interactive dungeons");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccb Setup Guide:");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Place Start Block at entrance");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Place End Block at final room");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Players right-click to interact");
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(22, header);
        ItemStack startBlock = plugin.getDungeonBlockManager().createStartBlockItem();
        inv.setItem(23, startBlock);
        ItemStack endBlock = plugin.getDungeonBlockManager().createEndBlockItem();
        inv.setItem(24, endBlock);
    }

    private static void createMobToolsSection(Inventory inv, ApexDungeons plugin) {
        ItemStack header = new ItemStack(Material.SPAWNER);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2694 Mob Spawning Tools");
        ArrayList<Object> headerLore = new ArrayList<Object>();
        headerLore.add(String.valueOf(ChatColor.GRAY) + "Tools for placing mob spawn points");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.YELLOW) + "Available Options:");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Custom Dungeon Mobs (5 unique)");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Traditional mob spawn tools");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Boss spawn capabilities");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Chest spawn system");
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(19, header);
        ItemStack customMobButton = new ItemStack(Material.DRAGON_HEAD);
        ItemMeta customMobMeta = customMobButton.getItemMeta();
        customMobMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\u2694 Custom Dungeon Mobs");
        ArrayList<Object> customMobLore = new ArrayList<Object>();
        customMobLore.add(String.valueOf(ChatColor.GRAY) + "5 unique custom mobs with YAML configs");
        customMobLore.add("");
        customMobLore.add(String.valueOf(ChatColor.GREEN) + "\u2728 Features:");
        customMobLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Visual indicators during building");
        customMobLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Silent spawning during gameplay");
        customMobLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Enhanced attributes & commands");
        customMobLore.add("");
        customMobLore.add(String.valueOf(ChatColor.YELLOW) + "Available: " + String.valueOf(ChatColor.WHITE) + plugin.getCustomMobManager().getCustomMobCount() + " custom mobs");
        customMobLore.add("");
        customMobLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to browse and select!");
        customMobMeta.setLore(customMobLore);
        customMobButton.setItemMeta(customMobMeta);
        inv.setItem(20, customMobButton);
        ItemStack mobSpawnTool = plugin.getMobSpawnTool().createMobSpawnTool();
        inv.setItem(21, mobSpawnTool);
        ItemStack bossSpawnTool = plugin.getMobSpawnTool().createBossSpawnTool();
        inv.setItem(29, bossSpawnTool);
        ItemStack chestSpawnTool = plugin.getChestSpawnTool().createChestSpawnTool();
        inv.setItem(30, chestSpawnTool);
    }

    private static void createUtilityToolsSection(Inventory inv, ApexDungeons plugin) {
        ItemStack header = new ItemStack(Material.BLAZE_ROD);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(String.valueOf(ChatColor.BLUE) + "\ud83d\udd27 Utility Tools");
        ArrayList<Object> headerLore = new ArrayList<Object>();
        headerLore.add(String.valueOf(ChatColor.GRAY) + "Additional tools for dungeon building");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.YELLOW) + "Available Tools:");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Room Connector - Link rooms together");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Selection Wand - Area selection");
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(37, header);
        ItemStack connector = plugin.getRoomConnector().createConnectorTool();
        inv.setItem(38, connector);
        ItemStack wand = new ItemStack(Material.STICK);
        ItemMeta wandMeta = wand.getItemMeta();
        wandMeta.setDisplayName(String.valueOf(ChatColor.BLUE) + "\ud83d\udd27 Selection Wand");
        ArrayList<Object> wandLore = new ArrayList<Object>();
        wandLore.add(String.valueOf(ChatColor.GRAY) + "Basic selection tool for area operations");
        wandLore.add("");
        wandLore.add(String.valueOf(ChatColor.GREEN) + "Usage:");
        wandLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Left-click: Set position 1");
        wandLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click: Set position 2");
        wandLore.add("");
        wandLore.add(String.valueOf(ChatColor.YELLOW) + "Perfect for measuring room sizes!");
        wandMeta.setLore(wandLore);
        wand.setItemMeta(wandMeta);
        inv.setItem(39, wand);
    }

    private static void createNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Back to Main Menu");
        ArrayList<CallSite> backLore = new ArrayList<CallSite>();
        backLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Return to the main")));
        backLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "ApexDungeons interface")));
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udcd6 Building Guide");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.GRAY) + "Complete guide to using");
        helpLore.add(String.valueOf(ChatColor.GRAY) + "all the building tools");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.GREEN) + "Click for detailed instructions!");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(49, help);
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2716 Close Menu");
        ArrayList<CallSite> closeLore = new ArrayList<CallSite>();
        closeLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Close this interface")));
        closeMeta.setLore(closeLore);
        close.setItemMeta(closeMeta);
        inv.setItem(53, close);
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                block23: {
                    if (!e.getView().getTitle().equals(GUI_NAME) || !(e.getWhoClicked() instanceof Player)) break block23;
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player)e.getWhoClicked();
                    switch (slot) {
                        case 10: 
                        case 11: 
                        case 12: 
                        case 13: 
                        case 14: 
                        case 15: {
                            ItemMeta meta;
                            ItemStack clickedItem = e.getCurrentItem();
                            if (clickedItem == null || !clickedItem.hasItemMeta() || !(meta = clickedItem.getItemMeta()).hasDisplayName() || !meta.getDisplayName().contains("\ud83d\udcd0")) break;
                            clicker.closeInventory();
                            ItemStack schematicStick = clickedItem.clone();
                            clicker.getInventory().addItem(new ItemStack[]{schematicStick});
                            String schematicName = meta.getDisplayName().replace(String.valueOf(ChatColor.GOLD) + "\ud83d\udcd0 ", "");
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 You received the " + schematicName + " schematic stick!");
                            clicker.sendMessage(String.valueOf(ChatColor.GRAY) + "Right-click on blocks to place the schematic!");
                            break;
                        }
                        case 16: {
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udce6 Adding a pack of schematic sticks to your inventory...");
                            int given = 0;
                            try {
                                ArrayList<String> names = new ArrayList<String>(plugin.getSchematicManager().getLoadedSchematicNames());
                                names.sort(String::compareToIgnoreCase);
                                for (String name : names) {
                                    if (given < 18) {
                                        ItemStack stick = plugin.getSimpleSchematicStick().createSchematicStick(name);
                                        clicker.getInventory().addItem(new ItemStack[]{stick});
                                        ++given;
                                        continue;
                                    }
                                    break;
                                }
                            }
                            catch (Exception ex) {
                                clicker.sendMessage(String.valueOf(ChatColor.RED) + "Failed to prepare sticks: " + ex.getMessage());
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Added " + given + " schematic sticks.");
                            clicker.sendMessage(String.valueOf(ChatColor.GRAY) + "Run /dgn tools again for more if needed.");
                            break;
                        }
                        case 20: {
                            clicker.closeInventory();
                            CustomMobSelectionGUI.open(clicker, plugin);
                            break;
                        }
                        case 23: {
                            BuildingToolsGUI.giveToolSafely(clicker, plugin.getDungeonBlockManager().createStartBlockItem(), "start_block");
                            break;
                        }
                        case 24: {
                            BuildingToolsGUI.giveToolSafely(clicker, plugin.getDungeonBlockManager().createEndBlockItem(), "end_block");
                            break;
                        }
                        case 21: {
                            if (!BuildingToolsGUI.hasMobSpawnToolInInventory(clicker)) {
                                plugin.getMobSpawnTool().giveMobSpawnTool(clicker);
                                clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Mob spawn tool added to your inventory!");
                                BuildingToolsGUI.showToolGuidance(clicker, "mob_spawn");
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "You already have a mob spawn tool!");
                            break;
                        }
                        case 29: {
                            if (!BuildingToolsGUI.hasBossSpawnToolInInventory(clicker)) {
                                plugin.getMobSpawnTool().giveBossSpawnTool(clicker);
                                clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Boss spawn tool added to your inventory!");
                                BuildingToolsGUI.showToolGuidance(clicker, "boss_spawn");
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "You already have a boss spawn tool!");
                            break;
                        }
                        case 30: {
                            if (!BuildingToolsGUI.hasChestSpawnToolInInventory(clicker)) {
                                plugin.getChestSpawnTool().giveChestSpawnTool(clicker);
                                clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Chest spawn tool added to your inventory!");
                                BuildingToolsGUI.showToolGuidance(clicker, "chest_spawn");
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "You already have a chest spawn tool!");
                            break;
                        }
                        case 38: {
                            BuildingToolsGUI.giveToolSafely(clicker, plugin.getRoomConnector().createConnectorTool(), "connector");
                            break;
                        }
                        case 39: {
                            if (!BuildingToolsGUI.hasWandInInventory(clicker)) {
                                plugin.getWandManager().giveWand(clicker);
                                clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Selection wand added to your inventory!");
                                BuildingToolsGUI.showToolGuidance(clicker, "wand");
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "You already have a selection wand!");
                            break;
                        }
                        case 45: {
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        }
                        case 49: {
                            clicker.closeInventory();
                            BuildingToolsGUI.showComprehensiveHelp(clicker);
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                        }
                    }
                }
            }
        }, (Plugin)pl);
    }

    private static void giveToolSafely(Player player, ItemStack tool, String toolType) {
        if (!BuildingToolsGUI.hasSimilarTool(player, tool)) {
            ItemStack giveItem = tool.clone();
            player.getInventory().addItem(new ItemStack[]{giveItem});
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Tool added to your inventory!");
            BuildingToolsGUI.showToolGuidance(player, toolType);
        } else {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You already have this tool!");
        }
    }

    private static boolean hasMobSpawnToolInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            ItemMeta meta;
            if (item == null || item.getType() != Material.ZOMBIE_HEAD || (meta = item.getItemMeta()) == null || !meta.hasDisplayName() || !meta.getDisplayName().contains("Mob Spawn Tool")) continue;
            return true;
        }
        return false;
    }

    private static boolean hasBossSpawnToolInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            ItemMeta meta;
            if (item == null || item.getType() != Material.WITHER_SKELETON_SKULL || (meta = item.getItemMeta()) == null || !meta.hasDisplayName() || !meta.getDisplayName().contains("Boss Spawn Tool")) continue;
            return true;
        }
        return false;
    }

    private static boolean hasChestSpawnToolInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            ItemMeta meta;
            if (item == null || item.getType() != Material.CHEST || (meta = item.getItemMeta()) == null || !meta.hasDisplayName() || !meta.getDisplayName().contains("Chest Spawn Tool")) continue;
            return true;
        }
        return false;
    }

    private static boolean hasWandInInventory(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            ItemMeta meta;
            if (item == null || !item.hasItemMeta() || !(meta = item.getItemMeta()).hasDisplayName() || !meta.getDisplayName().contains("Wand")) continue;
            return true;
        }
        return false;
    }

    private static boolean hasSimilarTool(Player player, ItemStack tool) {
        if (tool == null || !tool.hasItemMeta()) {
            return false;
        }
        String toolName = tool.getItemMeta().getDisplayName();
        if (toolName == null) {
            return false;
        }
        for (ItemStack item : player.getInventory().getContents()) {
            ItemMeta meta;
            if (item == null || !item.hasItemMeta() || !(meta = item.getItemMeta()).hasDisplayName() || !toolName.equals(meta.getDisplayName())) continue;
            return true;
        }
        return false;
    }

    private static void showToolGuidance(Player player, String toolType) {
        switch (toolType) {
            case "start_block": {
                player.sendMessage("");
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf START BLOCK GUIDANCE:");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Place this in your entrance/start room");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Players right-click to begin challenges");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Only one per dungeon recommended");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Make it visible and accessible!");
                break;
            }
            case "end_block": {
                player.sendMessage("");
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfc6 END BLOCK GUIDANCE:");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Place this in your final/boss room");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Players right-click to complete & get rewards");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Must activate Start Block first");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Place after all challenges are complete!");
                break;
            }
            case "connector": {
                player.sendMessage("");
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd17 ROOM CONNECTOR GUIDANCE:");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Left-click first wall, right-click second wall");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Type 1, 2, or 3 in chat for connection type");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Leave 3-block high spaces for doorways");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Plan your room layout first!");
                break;
            }
            case "mob_spawn": {
                player.sendMessage("");
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udc79 MOB SPAWN TOOL GUIDANCE:");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click blocks to set spawn points");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Use /dgn mobspawn set <mob_name> to configure");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Players trigger spawns by proximity");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Works with MythicMobs if installed!");
                break;
            }
            case "boss_spawn": {
                player.sendMessage("");
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udc80 BOSS SPAWN TOOL GUIDANCE:");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click blocks to set boss spawn points");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Use /dgn bossspawn set <boss_name> to configure");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Players trigger boss spawns by proximity");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Perfect for dungeon boss rooms!");
                break;
            }
            case "chest_spawn": {
                player.sendMessage("");
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udcb0 CHEST SPAWN TOOL GUIDANCE:");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click blocks to set chest spawn points");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Use /dgn chestspawn set <loot_table> to configure");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Players trigger chests by walking near");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Available tables: common, rare, epic, boss_rewards");
                break;
            }
            case "wand": {
                player.sendMessage("");
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd27 SELECTION WAND GUIDANCE:");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Left-click to set position 1");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click to set position 2");
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Use for area selection and measurements");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Great for planning room sizes!");
            }
        }
    }

    private static void showComprehensiveHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "    \ud83d\udd28 COMPLETE BUILDING GUIDE \ud83d\udd28");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfd7\ufe0f STEP-BY-STEP DUNGEON BUILDING:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Plan your dungeon layout on paper first");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Use " + String.valueOf(ChatColor.GOLD) + "Master Builder Wand" + String.valueOf(ChatColor.WHITE) + " for room structures");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Add a " + String.valueOf(ChatColor.GREEN) + "Start Block" + String.valueOf(ChatColor.WHITE) + " in the entrance");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Connect rooms using the " + String.valueOf(ChatColor.YELLOW) + "Room Connector");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "5. " + String.valueOf(ChatColor.WHITE) + "Place " + String.valueOf(ChatColor.RED) + "Mob Spawn Points" + String.valueOf(ChatColor.WHITE) + " for challenges");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "6. " + String.valueOf(ChatColor.WHITE) + "Add an " + String.valueOf(ChatColor.AQUA) + "End Block" + String.valueOf(ChatColor.WHITE) + " in the final room");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "7. " + String.valueOf(ChatColor.WHITE) + "Test your dungeon with " + String.valueOf(ChatColor.YELLOW) + "/dgn start <name>");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u26a1 MASTER BUILDER WAND:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Right-click: " + String.valueOf(ChatColor.WHITE) + "Open schematic selection GUI");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Left-click: " + String.valueOf(ChatColor.WHITE) + "Place selected schematic");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Preview Controls: " + String.valueOf(ChatColor.YELLOW) + "WASD" + String.valueOf(ChatColor.WHITE) + " move, " + String.valueOf(ChatColor.YELLOW) + "R" + String.valueOf(ChatColor.WHITE) + " rotate, " + String.valueOf(ChatColor.YELLOW) + "Enter" + String.valueOf(ChatColor.WHITE) + " confirm");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf ESSENTIAL BLOCKS:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Start Block: " + String.valueOf(ChatColor.WHITE) + "Players right-click to begin dungeon");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 End Block: " + String.valueOf(ChatColor.WHITE) + "Players right-click to complete & get rewards");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "  \u26a0 Players must activate Start Block before End Block works");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2694 MOB TOOLS:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Custom Mobs: " + String.valueOf(ChatColor.WHITE) + "5 unique YAML-configured mobs");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Traditional: " + String.valueOf(ChatColor.WHITE) + "Use commands to configure any mob");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Boss Spawns: " + String.valueOf(ChatColor.WHITE) + "Enhanced mobs for boss rooms");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Chest Spawns: " + String.valueOf(ChatColor.WHITE) + "Loot chests with configurable tables");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd27 UTILITY TOOLS:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Room Connector: " + String.valueOf(ChatColor.WHITE) + "Link rooms with doorways/corridors");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Selection Wand: " + String.valueOf(ChatColor.WHITE) + "Measure areas and plan layouts");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 PRO TIPS:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Use " + String.valueOf(ChatColor.YELLOW) + "/dgn help guide" + String.valueOf(ChatColor.GRAY) + " for complete room system guide");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Leave 3-block high spaces for doorway connections");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Test your dungeon before sharing with players");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Custom mobs show visual indicators during building");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Made by Vexy - Enjoy building amazing dungeons!");
    }
}

