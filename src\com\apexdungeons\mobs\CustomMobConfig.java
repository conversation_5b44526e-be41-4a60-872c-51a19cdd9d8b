/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 *  org.bukkit.configuration.file.YamlConfiguration
 */
package com.apexdungeons.mobs;

import java.io.File;
import java.util.List;
import org.bukkit.Material;
import org.bukkit.configuration.file.YamlConfiguration;

public class CustomMobConfig {
    private final String fileName;
    private final String displayName;
    private final String description;
    private final String category;
    private final String difficulty;
    private final Material icon;
    private final String mobType;
    private final int spawnRadius;
    private final int minCooldown;
    private final int maxCooldown;
    private final int maxConcurrent;
    private final boolean isBoss;
    private final List<String> spawnCommands;
    private final List<String> builderInfo;

    public CustomMobConfig(File file) {
        Material iconMaterial;
        YamlConfiguration config = YamlConfiguration.loadConfiguration((File)file);
        this.fileName = file.getName().replace(".yml", "");
        this.displayName = config.getString("display_name", this.fileName);
        this.description = config.getString("description", "Custom dungeon mob");
        this.category = config.getString("category", "basic");
        this.difficulty = config.getString("difficulty", "normal");
        String iconString = config.getString("icon", "ZOMBIE_HEAD");
        try {
            iconMaterial = Material.valueOf((String)iconString);
        }
        catch (IllegalArgumentException e) {
            iconMaterial = Material.ZOMBIE_HEAD;
        }
        this.icon = iconMaterial;
        this.mobType = config.getString("mob_type", "ZOMBIE");
        this.spawnRadius = config.getInt("spawn_radius", 5);
        this.minCooldown = config.getInt("cooldown.min", 30);
        this.maxCooldown = config.getInt("cooldown.max", 60);
        this.maxConcurrent = config.getInt("max_concurrent", 1);
        this.isBoss = config.getBoolean("is_boss", false);
        this.spawnCommands = config.getStringList("spawn_commands");
        this.builderInfo = config.getStringList("builder_info");
    }

    public String getFileName() {
        return this.fileName;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public String getDescription() {
        return this.description;
    }

    public String getCategory() {
        return this.category;
    }

    public String getDifficulty() {
        return this.difficulty;
    }

    public Material getIcon() {
        return this.icon;
    }

    public String getMobType() {
        return this.mobType;
    }

    public int getSpawnRadius() {
        return this.spawnRadius;
    }

    public int getMinCooldown() {
        return this.minCooldown;
    }

    public int getMaxCooldown() {
        return this.maxCooldown;
    }

    public int getMaxConcurrent() {
        return this.maxConcurrent;
    }

    public boolean isBoss() {
        return this.isBoss;
    }

    public List<String> getSpawnCommands() {
        return this.spawnCommands;
    }

    public List<String> getBuilderInfo() {
        return this.builderInfo;
    }

    public String getDifficultyColor() {
        switch (this.difficulty.toLowerCase()) {
            case "easy": {
                return "\u00a7a";
            }
            case "normal": {
                return "\u00a7e";
            }
            case "hard": {
                return "\u00a7c";
            }
            case "boss": {
                return "\u00a74";
            }
            case "elite": {
                return "\u00a75";
            }
        }
        return "\u00a7f";
    }

    public String getCategoryColor() {
        switch (this.category.toLowerCase()) {
            case "basic": {
                return "\u00a77";
            }
            case "undead": {
                return "\u00a78";
            }
            case "magical": {
                return "\u00a7d";
            }
            case "beast": {
                return "\u00a76";
            }
            case "boss": {
                return "\u00a74";
            }
            case "elite": {
                return "\u00a75";
            }
        }
        return "\u00a7f";
    }
}

