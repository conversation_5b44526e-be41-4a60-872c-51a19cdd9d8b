/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.gui.DungeonDeleteConfirmGUI;
import com.apexdungeons.gui.DungeonManagementGUI;
import java.lang.invoke.CallSite;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.UUID;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class DungeonOptionsGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_GREEN) + "\ud83c\udff0 Dungeon Options";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MMM dd, yyyy HH:mm");

    public static void open(Player player, ApexDungeons plugin, DungeonInstance dungeon) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        DungeonOptionsGUI.fillBackground(inv);
        DungeonOptionsGUI.createDungeonInfo(inv, dungeon);
        DungeonOptionsGUI.createActionButtons(inv, player, plugin, dungeon);
        DungeonOptionsGUI.createNavigationButtons(inv, plugin);
        player.openInventory(inv);
        DungeonOptionsGUI.registerEventListener(plugin, dungeon);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.GREEN_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createDungeonInfo(Inventory inv, DungeonInstance dungeon) {
        ItemStack info = new ItemStack(Material.FILLED_MAP);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udccb " + dungeon.getDisplayName());
        ArrayList<CallSite> infoLore = new ArrayList<CallSite>();
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501")));
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Internal Name: " + String.valueOf(ChatColor.WHITE) + dungeon.getName())));
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Creator: " + String.valueOf(ChatColor.WHITE) + dungeon.getCreator())));
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "World: " + String.valueOf(ChatColor.WHITE) + dungeon.getWorld().getName())));
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Rooms: " + String.valueOf(ChatColor.WHITE) + dungeon.getRoomCount())));
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Created: " + String.valueOf(ChatColor.WHITE) + DATE_FORMAT.format(new Date(dungeon.getCreationTime())))));
        if (dungeon.isGenerating()) {
            infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Status: " + String.valueOf(ChatColor.GOLD) + "\u26a0 Generating...")));
        } else {
            infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Status: " + String.valueOf(ChatColor.GREEN) + "\u2713 Ready")));
        }
        Location origin = dungeon.getOrigin();
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Origin: " + String.valueOf(ChatColor.WHITE) + origin.getBlockX() + ", " + origin.getBlockY() + ", " + origin.getBlockZ())));
        infoLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501")));
        infoMeta.setLore(infoLore);
        info.setItemMeta(infoMeta);
        inv.setItem(13, info);
        ItemStack players = new ItemStack(Material.PLAYER_HEAD);
        ItemMeta playersMeta = players.getItemMeta();
        playersMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udc65 Active Players");
        ArrayList<Object> playersLore = new ArrayList<Object>();
        playersLore.add(String.valueOf(ChatColor.GRAY) + "Players currently in this dungeon");
        playersLore.add("");
        if (dungeon.getPlayers().isEmpty()) {
            playersLore.add(String.valueOf(ChatColor.GRAY) + "No players currently online");
        } else {
            playersLore.add(String.valueOf(ChatColor.YELLOW) + "Online Players:");
            for (UUID playerId : dungeon.getPlayers()) {
                Player onlinePlayer = Bukkit.getPlayer((UUID)playerId);
                if (onlinePlayer == null) continue;
                playersLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + onlinePlayer.getName());
            }
        }
        playersLore.add("");
        playersLore.add(String.valueOf(ChatColor.YELLOW) + "Total: " + String.valueOf(ChatColor.WHITE) + dungeon.getPlayers().size() + " players");
        playersMeta.setLore(playersLore);
        players.setItemMeta(playersMeta);
        inv.setItem(31, players);
    }

    private static void createActionButtons(Inventory inv, Player player, ApexDungeons plugin, DungeonInstance dungeon) {
        boolean isOwner = dungeon.getCreator().equals(player.getName());
        boolean isAdmin = player.hasPermission("apexdungeons.admin");
        ItemStack teleport = new ItemStack(Material.ENDER_PEARL);
        ItemMeta teleportMeta = teleport.getItemMeta();
        teleportMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83c\udf00 Teleport to Dungeon");
        ArrayList<Object> teleportLore = new ArrayList<Object>();
        teleportLore.add(String.valueOf(ChatColor.GRAY) + "Travel to this dungeon instantly");
        if (dungeon.isGenerating()) {
            teleportLore.add("");
            teleportLore.add(String.valueOf(ChatColor.RED) + "\u26a0 Dungeon is still generating!");
            teleportLore.add(String.valueOf(ChatColor.GRAY) + "Please wait for completion");
        } else {
            teleportLore.add("");
            teleportLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to teleport!");
        }
        teleportMeta.setLore(teleportLore);
        teleport.setItemMeta(teleportMeta);
        inv.setItem(19, teleport);
        ItemStack portals = new ItemStack(Material.NETHER_PORTAL);
        ItemMeta portalsMeta = portals.getItemMeta();
        portalsMeta.setDisplayName(String.valueOf(ChatColor.LIGHT_PURPLE) + "\ud83c\udf0c Portal Management");
        ArrayList<Object> portalsLore = new ArrayList<Object>();
        portalsLore.add(String.valueOf(ChatColor.GRAY) + "Manage portals for this dungeon");
        portalsLore.add("");
        portalsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 View existing portals");
        portalsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Create new portals");
        portalsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Remove old portals");
        portalsLore.add("");
        portalsLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to manage!");
        portalsMeta.setLore(portalsLore);
        portals.setItemMeta(portalsMeta);
        inv.setItem(21, portals);
        if (isOwner || isAdmin) {
            ItemStack playerMgmt = new ItemStack(Material.IRON_SWORD);
            ItemMeta playerMeta = playerMgmt.getItemMeta();
            playerMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2694 Player Management");
            ArrayList<Object> playerLore = new ArrayList<Object>();
            playerLore.add(String.valueOf(ChatColor.GRAY) + "Manage players in this dungeon");
            playerLore.add("");
            playerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Kick players");
            playerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Ban/unban players");
            playerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Set permissions");
            playerLore.add("");
            playerLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to manage!");
            playerMeta.setLore(playerLore);
            playerMgmt.setItemMeta(playerMeta);
            inv.setItem(23, playerMgmt);
        }
        if (isOwner || isAdmin) {
            ItemStack settings = new ItemStack(Material.REDSTONE);
            ItemMeta settingsMeta = settings.getItemMeta();
            settingsMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\u2699 Dungeon Settings");
            ArrayList<Object> settingsLore = new ArrayList<Object>();
            settingsLore.add(String.valueOf(ChatColor.GRAY) + "Configure dungeon properties");
            settingsLore.add("");
            settingsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Rename dungeon");
            settingsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Change permissions");
            settingsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Modify spawn point");
            settingsLore.add("");
            settingsLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to configure!");
            settingsMeta.setLore(settingsLore);
            settings.setItemMeta(settingsMeta);
            inv.setItem(25, settings);
        }
        ItemStack stats = new ItemStack(Material.CLOCK);
        ItemMeta statsMeta = stats.getItemMeta();
        statsMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udcca Dungeon Statistics");
        ArrayList<Object> statsLore = new ArrayList<Object>();
        statsLore.add(String.valueOf(ChatColor.GRAY) + "View detailed dungeon statistics");
        statsLore.add("");
        statsLore.add(String.valueOf(ChatColor.YELLOW) + "Statistics include:");
        statsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Total visitors");
        statsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Average session time");
        statsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Most active times");
        statsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Performance metrics");
        statsLore.add("");
        statsLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to view!");
        statsMeta.setLore(statsLore);
        stats.setItemMeta(statsMeta);
        inv.setItem(37, stats);
        if (isOwner || isAdmin) {
            ItemStack backup = new ItemStack(Material.CHEST);
            ItemMeta backupMeta = backup.getItemMeta();
            backupMeta.setDisplayName(String.valueOf(ChatColor.BLUE) + "\ud83d\udcbe Backup & Export");
            ArrayList<Object> backupLore = new ArrayList<Object>();
            backupLore.add(String.valueOf(ChatColor.GRAY) + "Create backups and exports");
            backupLore.add("");
            backupLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Save dungeon schematic");
            backupLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Export as template");
            backupLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Create world backup");
            backupLore.add("");
            backupLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to backup!");
            backupMeta.setLore(backupLore);
            backup.setItemMeta(backupMeta);
            inv.setItem(39, backup);
        }
        if (isOwner || isAdmin) {
            ItemStack delete = new ItemStack(Material.BARRIER);
            ItemMeta deleteMeta = delete.getItemMeta();
            deleteMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\ud83d\uddd1 Delete Dungeon");
            ArrayList<Object> deleteLore = new ArrayList<Object>();
            deleteLore.add(String.valueOf(ChatColor.GRAY) + "Permanently delete this dungeon");
            deleteLore.add("");
            deleteLore.add(String.valueOf(ChatColor.RED) + "\u26a0 WARNING: This action cannot be undone!");
            deleteLore.add(String.valueOf(ChatColor.RED) + "\u26a0 All data will be permanently lost!");
            deleteLore.add("");
            deleteLore.add(String.valueOf(ChatColor.YELLOW) + "This will:");
            deleteLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Remove the dungeon world");
            deleteLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Delete all portals");
            deleteLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Kick all players");
            deleteLore.add("");
            deleteLore.add(String.valueOf(ChatColor.RED) + "\u25b6 Click to delete!");
            deleteMeta.setLore(deleteLore);
            delete.setItemMeta(deleteMeta);
            inv.setItem(41, delete);
        }
    }

    private static void createNavigationButtons(Inventory inv, ApexDungeons plugin) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2190 Back to Dungeon List");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack refresh = new ItemStack(Material.LIME_DYE);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udd04 Refresh Information");
        ArrayList<CallSite> refreshLore = new ArrayList<CallSite>();
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Update dungeon information")));
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "with the latest data")));
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(53, refresh);
    }

    private static void registerEventListener(final ApexDungeons plugin, final DungeonInstance dungeon) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    boolean isOwner = dungeon.getCreator().equals(clicker.getName());
                    boolean isAdmin = clicker.hasPermission("apexdungeons.admin");
                    switch (slot) {
                        case 19: {
                            if (dungeon.isGenerating()) {
                                clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon is still generating. Please wait...");
                                return;
                            }
                            Location spawnLoc = plugin.getWorldManager().getDungeonSpawnLocation(dungeon.getName());
                            if (spawnLoc != null) {
                                clicker.closeInventory();
                                plugin.getEffectsManager().playDungeonEntryEffects(clicker, dungeon);
                                clicker.teleport(spawnLoc);
                                dungeon.addPlayer(clicker);
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.RED) + "Failed to find dungeon spawn location!");
                            break;
                        }
                        case 21: {
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Portal management coming soon!");
                            break;
                        }
                        case 23: {
                            if (!isOwner && !isAdmin) break;
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Player management coming soon!");
                            break;
                        }
                        case 25: {
                            if (!isOwner && !isAdmin) break;
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon settings coming soon!");
                            break;
                        }
                        case 37: {
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon statistics coming soon!");
                            break;
                        }
                        case 39: {
                            if (!isOwner && !isAdmin) break;
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Backup functionality coming soon!");
                            break;
                        }
                        case 41: {
                            if (!isOwner && !isAdmin) break;
                            clicker.closeInventory();
                            DungeonDeleteConfirmGUI.open(clicker, plugin, dungeon);
                            break;
                        }
                        case 45: {
                            clicker.closeInventory();
                            DungeonManagementGUI.open(clicker, plugin);
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                            DungeonOptionsGUI.open(clicker, plugin, dungeon);
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Information refreshed!");
                        }
                    }
                }
            }
        }, (Plugin)plugin);
    }
}

