/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.party;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import org.bukkit.entity.Player;

public class Party {
    private final UUID partyId = UUID.randomUUID();
    private final Player leader;
    private final Set<Player> members;
    private final long createdTime;
    private final int maxSize;

    public Party(Player leader) {
        this.leader = leader;
        this.members = new LinkedHashSet<Player>();
        this.members.add(leader);
        this.createdTime = System.currentTimeMillis();
        this.maxSize = 4;
    }

    public boolean addMember(Player player) {
        if (this.members.size() >= this.maxSize) {
            return false;
        }
        if (this.members.contains(player)) {
            return false;
        }
        return this.members.add(player);
    }

    public boolean removeMember(Player player) {
        if (player.equals((Object)this.leader)) {
            return false;
        }
        return this.members.remove(player);
    }

    public boolean hasMember(Player player) {
        return this.members.contains(player);
    }

    public boolean isLeader(Player player) {
        return player.equals((Object)this.leader);
    }

    public List<Player> getMembers() {
        return new ArrayList<Player>(this.members);
    }

    public List<Player> getMembersExcludingLeader() {
        ArrayList<Player> result = new ArrayList<Player>(this.members);
        result.remove(this.leader);
        return result;
    }

    public boolean isFull() {
        return this.members.size() >= this.maxSize;
    }

    public boolean isEmpty() {
        return this.members.size() <= 1;
    }

    public int getSize() {
        return this.members.size();
    }

    public int getAvailableSlots() {
        return this.maxSize - this.members.size();
    }

    public void disband() {
        this.members.clear();
    }

    public boolean transferLeadership(Player newLeader) {
        if (!this.members.contains(newLeader) || newLeader.equals((Object)this.leader)) {
            return false;
        }
        this.members.remove(this.leader);
        this.members.remove(newLeader);
        LinkedHashSet<Player> newMembers = new LinkedHashSet<Player>();
        newMembers.add(newLeader);
        newMembers.add(this.leader);
        newMembers.addAll(this.members);
        this.members.clear();
        this.members.addAll(newMembers);
        return true;
    }

    public String getFormattedInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Party (").append(this.members.size()).append("/").append(this.maxSize).append("):\n");
        for (Player member : this.members) {
            if (member.equals((Object)this.leader)) {
                info.append("\ud83d\udc51 ").append(member.getName()).append(" (Leader)\n");
                continue;
            }
            info.append("\ud83d\udc64 ").append(member.getName()).append("\n");
        }
        return info.toString();
    }

    public UUID getPartyId() {
        return this.partyId;
    }

    public Player getLeader() {
        return this.leader;
    }

    public long getCreatedTime() {
        return this.createdTime;
    }

    public int getMaxSize() {
        return this.maxSize;
    }

    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || this.getClass() != obj.getClass()) {
            return false;
        }
        Party party = (Party)obj;
        return Objects.equals(this.partyId, party.partyId);
    }

    public int hashCode() {
        return Objects.hash(this.partyId);
    }

    public String toString() {
        return "Party{partyId=" + String.valueOf(this.partyId) + ", leader=" + this.leader.getName() + ", size=" + this.members.size() + ", maxSize=" + this.maxSize + "}";
    }
}

