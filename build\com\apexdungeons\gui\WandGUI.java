/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class WandGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_GRAY) + "Select Room";

    public static void open(Player player, final ApexDungeons plugin) {
        final List<String> roomNames = plugin.getDungeonManager().listRoomNames();
        int rows = Math.max(1, (int)Math.ceil((double)roomNames.size() / 9.0));
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)(rows * 9), (String)GUI_NAME);
        for (int i = 0; i < roomNames.size(); ++i) {
            String room = roomNames.get(i);
            ItemStack paper = new ItemStack(Material.PAPER);
            ItemMeta meta = paper.getItemMeta();
            meta.setDisplayName(String.valueOf(ChatColor.GREEN) + room);
            meta.setLore(List.of(String.valueOf(ChatColor.GRAY) + "Place this room"));
            paper.setItemMeta(meta);
            inv.setItem(i, paper);
        }
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot >= 0 && slot < roomNames.size()) {
                        String selected = (String)roomNames.get(slot);
                        e.getWhoClicked().closeInventory();
                        plugin.getDungeonManager().placeRoomAt((Player)e.getWhoClicked(), selected);
                    }
                }
            }
        }, (Plugin)pl);
        player.openInventory(inv);
    }
}

