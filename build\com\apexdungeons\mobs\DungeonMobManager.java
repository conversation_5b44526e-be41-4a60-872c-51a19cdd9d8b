/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 */
package com.apexdungeons.mobs;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.mobs.DungeonMobConfig;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.bukkit.Material;

public class DungeonMobManager {
    private final ApexDungeons plugin;
    private final File dungeonMobsFolder;
    private final Map<String, DungeonMobConfig> loadedMobs = new HashMap<String, DungeonMobConfig>();
    private final Map<String, List<DungeonMobConfig>> mobsByCategory = new HashMap<String, List<DungeonMobConfig>>();

    public DungeonMobManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.dungeonMobsFolder = new File(plugin.getDataFolder(), "dungeon_mobs");
        if (!this.dungeonMobsFolder.exists()) {
            this.dungeonMobsFolder.mkdirs();
            plugin.getLogger().info("Created dungeon_mobs folder: " + this.dungeonMobsFolder.getPath());
            this.createExampleMobConfigs();
        }
        this.loadAllMobConfigs();
    }

    public void loadAllMobConfigs() {
        this.loadedMobs.clear();
        this.mobsByCategory.clear();
        this.plugin.getLogger().info("Loading mob configurations from: " + this.dungeonMobsFolder.getAbsolutePath());
        File[] files = this.dungeonMobsFolder.listFiles((dir, name) -> name.toLowerCase().endsWith(".yml") || name.toLowerCase().endsWith(".yaml"));
        if (files == null || files.length == 0) {
            this.plugin.getLogger().info("No mob configuration files found in " + this.dungeonMobsFolder.getPath());
            this.plugin.getLogger().info("Creating example mob configurations...");
            this.createExampleMobConfigs();
            return;
        }
        this.plugin.getLogger().info("Found " + files.length + " mob configuration files, loading...");
        int loaded = 0;
        int failed = 0;
        Arrays.sort(files, (a, b) -> a.getName().compareToIgnoreCase(b.getName()));
        for (File file : files) {
            try {
                this.plugin.getLogger().info("Loading mob config: " + file.getName());
                DungeonMobConfig mobConfig = DungeonMobConfig.loadFromFile(file);
                if (mobConfig != null) {
                    this.loadedMobs.put(mobConfig.getId(), mobConfig);
                    this.mobsByCategory.computeIfAbsent(mobConfig.getCategory(), k -> new ArrayList()).add(mobConfig);
                    ++loaded;
                    this.plugin.getLogger().info("\u2713 Loaded mob: " + mobConfig.getDisplayName() + " [" + mobConfig.getCategory() + "/" + mobConfig.getDifficulty() + "]");
                    continue;
                }
                ++failed;
                this.plugin.getLogger().warning("\u2717 Failed to load mob config: " + file.getName() + " (returned null)");
            }
            catch (Exception e) {
                ++failed;
                this.plugin.getLogger().warning("\u2717 Failed to load mob config: " + file.getName() + " - " + e.getMessage());
                if (!this.plugin.getConfig().getBoolean("debug", false)) continue;
                e.printStackTrace();
            }
        }
        this.plugin.getLogger().info("Mob configuration loading complete: " + loaded + " loaded, " + failed + " failed");
        if (loaded > 0) {
            this.plugin.getLogger().info("Available categories: " + String.join((CharSequence)", ", this.mobsByCategory.keySet()));
            this.plugin.getLogger().info("Total mobs: " + this.loadedMobs.size());
        }
    }

    private void createExampleMobConfigs() {
        this.createMobConfig("zombie_basic", "Basic Zombie", "A simple undead mob", "undead", "easy", Material.ZOMBIE_HEAD, "ZOMBIE", Arrays.asList("summon zombie %x% %y% %z% {CustomName:'\"Dungeon Zombie\"'}"));
        this.createMobConfig("skeleton_archer", "Skeleton Archer", "Ranged undead attacker", "undead", "normal", Material.SKELETON_SKULL, "SKELETON", Arrays.asList("summon skeleton %x% %y% %z% {CustomName:'\"Skeleton Archer\"', HandItems:[{id:bow,Count:1},{}]}"));
        this.createMobConfig("spider_venomous", "Venomous Spider", "Poisonous cave spider", "beast", "normal", Material.SPIDER_EYE, "CAVE_SPIDER", Arrays.asList("summon cave_spider %x% %y% %z% {CustomName:'\"Venomous Spider\"', ActiveEffects:[{Id:19,Amplifier:0,Duration:999999}]}"));
        this.createMobConfig("golem_guardian", "Stone Guardian", "Powerful dungeon guardian", "magical", "hard", Material.IRON_BLOCK, "IRON_GOLEM", Arrays.asList("summon iron_golem %x% %y% %z% {CustomName:'\"Stone Guardian\"', Health:100.0f, Attributes:[{Name:generic.max_health,Base:100.0}]}"));
        this.createMobConfig("lich_boss", "Dungeon Lich", "Powerful undead boss", "boss", "boss", Material.WITHER_SKELETON_SKULL, "WITHER_SKELETON", Arrays.asList("summon wither_skeleton %x% %y% %z% {CustomName:'\"Dungeon Lich\"', Health:200.0f, Attributes:[{Name:generic.max_health,Base:200.0},{Name:generic.attack_damage,Base:12.0}], HandItems:[{id:netherite_sword,Count:1,tag:{Enchantments:[{id:sharpness,lvl:3}]}},{}], ArmorItems:[{},{},{},{id:netherite_helmet,Count:1}]}"));
    }

    private void createMobConfig(String id, String displayName, String description, String category, String difficulty, Material icon, String mobType, List<String> spawnCommands) {
        File configFile = new File(this.dungeonMobsFolder, id + ".yml");
        if (configFile.exists()) {
            return;
        }
        try (FileWriter writer = new FileWriter(configFile);){
            writer.write("# " + displayName + " Configuration\n");
            writer.write("display_name: \"" + displayName + "\"\n");
            writer.write("description: \"" + description + "\"\n");
            writer.write("category: \"" + category + "\"\n");
            writer.write("difficulty: \"" + difficulty + "\"\n");
            writer.write("icon: \"" + icon.name() + "\"\n\n");
            writer.write("# Mob spawning configuration\n");
            writer.write("mob_type: \"" + mobType + "\"\n");
            writer.write("spawn_radius: 6\n");
            writer.write("cooldown:\n");
            writer.write("  min: " + (difficulty.equals("boss") ? 60 : 30) + "\n");
            writer.write("  max: " + (difficulty.equals("boss") ? 120 : 60) + "\n");
            writer.write("max_concurrent: " + (difficulty.equals("boss") ? 1 : 2) + "\n");
            writer.write("is_boss: " + difficulty.equals("boss") + "\n\n");
            writer.write("# Commands to execute when spawning\n");
            writer.write("spawn_commands:\n");
            for (String command : spawnCommands) {
                writer.write("  - \"" + command + "\"\n");
            }
            writer.write("\n# Information shown to builders\n");
            writer.write("builder_info:\n");
            writer.write("  - \"" + description + "\"\n");
            writer.write("  - \"Difficulty: " + difficulty + "\"\n");
            writer.write("  - \"Category: " + category + "\"\n");
            this.plugin.getLogger().info("Created example mob config: " + configFile.getName());
        }
        catch (IOException e) {
            this.plugin.getLogger().warning("Failed to create mob config " + id + ": " + e.getMessage());
        }
    }

    public Map<String, DungeonMobConfig> getLoadedMobs() {
        return new HashMap<String, DungeonMobConfig>(this.loadedMobs);
    }

    public Map<String, List<DungeonMobConfig>> getMobsByCategory() {
        return new HashMap<String, List<DungeonMobConfig>>(this.mobsByCategory);
    }

    public DungeonMobConfig getMobConfig(String id) {
        return this.loadedMobs.get(id);
    }

    public List<String> getCategories() {
        return new ArrayList<String>(this.mobsByCategory.keySet());
    }

    public List<DungeonMobConfig> getMobsInCategory(String category) {
        return this.mobsByCategory.getOrDefault(category, new ArrayList());
    }

    public List<DungeonMobConfig> getBossMobs() {
        return this.loadedMobs.values().stream().filter(DungeonMobConfig::isBoss).collect(Collectors.toList());
    }

    public List<DungeonMobConfig> getMobsByDifficulty(String difficulty) {
        return this.loadedMobs.values().stream().filter(mob -> mob.getDifficulty().equalsIgnoreCase(difficulty)).collect(Collectors.toList());
    }

    public void reloadMobConfigs() {
        this.loadAllMobConfigs();
    }
}

