/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 *  org.jetbrains.annotations.NotNull
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.ActiveGUI;
import com.apexdungeons.gui.HelpGUI;
import com.apexdungeons.gui.PresetGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;

public class MainGUI
implements Listener {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_PURPLE) + "\ud83c\udff0 Soaps Dungeons";
    private static final int SIZE = 27;

    public static void open(@NotNull Player player, final @NotNull ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)27, (String)GUI_NAME);
        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta cm = create.getItemMeta();
        cm.setDisplayName(String.valueOf(ChatColor.GREEN) + "Create Dungeon");
        ArrayList<CallSite> lore = new ArrayList<CallSite>();
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Generate a new dungeon using a preset")));
        cm.setLore(lore);
        create.setItemMeta(cm);
        inv.setItem(11, create);
        ItemStack active = new ItemStack(Material.ENDER_EYE);
        ItemMeta am = active.getItemMeta();
        am.setDisplayName(String.valueOf(ChatColor.AQUA) + "Active Dungeons");
        am.setLore(List.of(String.valueOf(ChatColor.GRAY) + "Teleport to an existing dungeon"));
        active.setItemMeta(am);
        inv.setItem(13, active);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta hm = help.getItemMeta();
        hm.setDisplayName(String.valueOf(ChatColor.YELLOW) + "Help");
        hm.setLore(List.of(String.valueOf(ChatColor.GRAY) + "Learn how to use ApexDungeons"));
        help.setItemMeta(hm);
        inv.setItem(15, help);
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot == 11) {
                        e.getWhoClicked().closeInventory();
                        PresetGUI.open((Player)e.getWhoClicked(), plugin);
                    } else if (slot == 13) {
                        e.getWhoClicked().closeInventory();
                        ActiveGUI.open((Player)e.getWhoClicked(), plugin);
                    } else if (slot == 15) {
                        e.getWhoClicked().closeInventory();
                        HelpGUI.open((Player)e.getWhoClicked());
                    }
                }
            }
        }, (Plugin)pl);
        player.openInventory(inv);
    }
}

