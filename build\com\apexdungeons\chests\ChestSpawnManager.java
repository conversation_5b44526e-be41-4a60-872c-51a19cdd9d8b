/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.block.Block
 *  org.bukkit.block.Chest
 *  org.bukkit.configuration.file.YamlConfiguration
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.player.PlayerMoveEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.chests;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.chests.ChestLootTable;
import com.apexdungeons.chests.ChestSpawnPoint;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.Chest;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.plugin.Plugin;

public class ChestSpawnManager
implements Listener {
    private final ApexDungeons plugin;
    private final Map<String, ChestSpawnPoint> chestSpawnPoints = new ConcurrentHashMap<String, ChestSpawnPoint>();
    private final Map<String, Long> lastSpawnTimes = new ConcurrentHashMap<String, Long>();
    private final Set<String> activeChests = ConcurrentHashMap.newKeySet();
    private double defaultRadius = 3.0;
    private long spawnCooldown = 60000L;

    public ChestSpawnManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.loadChestSpawnPoints();
        Bukkit.getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public void addChestSpawnPoint(Location location, String lootTable, double radius) {
        String key = this.locationToKey(location);
        ChestSpawnPoint spawnPoint = new ChestSpawnPoint(location, lootTable, radius);
        this.chestSpawnPoints.put(key, spawnPoint);
        this.saveChestSpawnPoints();
        this.plugin.getLogger().info("Added chest spawn point: " + lootTable + " at " + this.locationToString(location));
    }

    public boolean removeChestSpawnPoint(Location location) {
        String key = this.locationToKey(location);
        ChestSpawnPoint removed = this.chestSpawnPoints.remove(key);
        if (removed != null) {
            this.saveChestSpawnPoints();
            this.plugin.getLogger().info("Removed chest spawn point at " + this.locationToString(location));
            return true;
        }
        return false;
    }

    public ChestSpawnPoint getChestSpawnPoint(Location location) {
        String key = this.locationToKey(location);
        return this.chestSpawnPoints.get(key);
    }

    public Collection<ChestSpawnPoint> getAllChestSpawnPoints() {
        return new ArrayList<ChestSpawnPoint>(this.chestSpawnPoints.values());
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        Location to = event.getTo();
        if (to == null) {
            return;
        }
        Location from = event.getFrom();
        if (from.getBlockX() == to.getBlockX() && from.getBlockY() == to.getBlockY() && from.getBlockZ() == to.getBlockZ()) {
            return;
        }
        for (ChestSpawnPoint spawnPoint : this.chestSpawnPoints.values()) {
            double distance;
            if (!spawnPoint.getLocation().getWorld().equals((Object)to.getWorld()) || !((distance = spawnPoint.getLocation().distance(to)) <= spawnPoint.getRadius())) continue;
            this.triggerChestSpawn(spawnPoint, player);
        }
    }

    private void triggerChestSpawn(ChestSpawnPoint spawnPoint, Player triggeringPlayer) {
        String key = this.locationToKey(spawnPoint.getLocation());
        Long lastSpawn = this.lastSpawnTimes.get(key);
        if (lastSpawn != null && System.currentTimeMillis() - lastSpawn < this.spawnCooldown) {
            return;
        }
        if (this.activeChests.contains(key)) {
            return;
        }
        Location chestLoc = spawnPoint.getLocation();
        if (chestLoc.getBlock().getType() == Material.CHEST) {
            return;
        }
        this.activeChests.add(key);
        this.lastSpawnTimes.put(key, System.currentTimeMillis());
        this.spawnChest(spawnPoint, triggeringPlayer);
        this.activeChests.remove(key);
    }

    private void spawnChest(ChestSpawnPoint spawnPoint, Player triggeringPlayer) {
        Location chestLoc = spawnPoint.getLocation().clone();
        String lootTable = spawnPoint.getLootTable();
        chestLoc = this.findSafeChestLocation(chestLoc);
        Block chestBlock = chestLoc.getBlock();
        chestBlock.setType(Material.CHEST);
        if (chestBlock.getState() instanceof Chest) {
            Chest chest = (Chest)chestBlock.getState();
            this.fillChestWithLoot(chest.getInventory(), lootTable);
            this.plugin.getLogger().info("Spawned chest with loot table '" + lootTable + "' at " + this.locationToString(chestLoc) + " (triggered by " + triggeringPlayer.getName() + ")");
        }
    }

    private Location findSafeChestLocation(Location location) {
        Location safe = location.clone();
        if (safe.getBlock().getType().isAir() && !safe.clone().add(0.0, -1.0, 0.0).getBlock().getType().isAir()) {
            return safe;
        }
        for (int y = -2; y <= 3; ++y) {
            Location test = safe.clone().add(0.0, (double)y, 0.0);
            if (!test.getBlock().getType().isAir() || test.clone().add(0.0, -1.0, 0.0).getBlock().getType().isAir()) continue;
            return test;
        }
        return safe;
    }

    private void fillChestWithLoot(Inventory chestInventory, String lootTable) {
        ChestLootTable lootTableData = this.plugin.getChestLootManager().getLootTable(lootTable);
        if (lootTableData == null) {
            lootTableData = this.plugin.getChestLootManager().getDefaultLootTable();
        }
        if (lootTableData != null) {
            List<ItemStack> loot = lootTableData.generateLoot();
            Random random = new Random();
            block0: for (ItemStack item : loot) {
                int slot = random.nextInt(chestInventory.getSize());
                for (int i = 0; i < chestInventory.getSize(); ++i) {
                    int checkSlot = (slot + i) % chestInventory.getSize();
                    if (chestInventory.getItem(checkSlot) != null) continue;
                    chestInventory.setItem(checkSlot, item);
                    continue block0;
                }
            }
        }
    }

    private String locationToKey(Location location) {
        return location.getWorld().getName() + ":" + location.getBlockX() + ":" + location.getBlockY() + ":" + location.getBlockZ();
    }

    private String locationToString(Location location) {
        return String.format("%s(%d,%d,%d)", location.getWorld().getName(), location.getBlockX(), location.getBlockY(), location.getBlockZ());
    }

    private void loadChestSpawnPoints() {
        File file = new File(this.plugin.getDataFolder(), "chest_spawns.yml");
        if (!file.exists()) {
            return;
        }
        YamlConfiguration config = YamlConfiguration.loadConfiguration((File)file);
        for (String key : config.getKeys(false)) {
            try {
                String worldName = config.getString(key + ".world");
                int x = config.getInt(key + ".x");
                int y = config.getInt(key + ".y");
                int z = config.getInt(key + ".z");
                String lootTable = config.getString(key + ".loot_table");
                double radius = config.getDouble(key + ".radius", this.defaultRadius);
                Location location = new Location(Bukkit.getWorld((String)worldName), (double)x, (double)y, (double)z);
                ChestSpawnPoint spawnPoint = new ChestSpawnPoint(location, lootTable, radius);
                this.chestSpawnPoints.put(key, spawnPoint);
            }
            catch (Exception e) {
                this.plugin.getLogger().warning("Failed to load chest spawn point " + key + ": " + e.getMessage());
            }
        }
        this.plugin.getLogger().info("Loaded " + this.chestSpawnPoints.size() + " chest spawn points");
    }

    private void saveChestSpawnPoints() {
        File file = new File(this.plugin.getDataFolder(), "chest_spawns.yml");
        YamlConfiguration config = new YamlConfiguration();
        for (Map.Entry<String, ChestSpawnPoint> entry : this.chestSpawnPoints.entrySet()) {
            String key = entry.getKey();
            ChestSpawnPoint point = entry.getValue();
            Location loc = point.getLocation();
            config.set(key + ".world", (Object)loc.getWorld().getName());
            config.set(key + ".x", (Object)loc.getBlockX());
            config.set(key + ".y", (Object)loc.getBlockY());
            config.set(key + ".z", (Object)loc.getBlockZ());
            config.set(key + ".loot_table", (Object)point.getLootTable());
            config.set(key + ".radius", (Object)point.getRadius());
        }
        try {
            config.save(file);
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save chest spawn points: " + e.getMessage());
        }
    }

    public void shutdown() {
        this.saveChestSpawnPoints();
        this.chestSpawnPoints.clear();
        this.lastSpawnTimes.clear();
        this.activeChests.clear();
    }
}

