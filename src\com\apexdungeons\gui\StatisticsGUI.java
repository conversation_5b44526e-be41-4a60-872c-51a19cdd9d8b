/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.EnhancedMainGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class StatisticsGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.GOLD) + "\ud83d\udcca Your Statistics";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        StatisticsGUI.fillBackground(inv);
        StatisticsGUI.createStatisticsDisplay(inv, player, plugin);
        StatisticsGUI.createNavigationButtons(inv);
        player.openInventory(inv);
        StatisticsGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.YELLOW_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createStatisticsDisplay(Inventory inv, Player player, ApexDungeons plugin) {
        ItemStack general = new ItemStack(Material.PLAYER_HEAD);
        ItemMeta generalMeta = general.getItemMeta();
        generalMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udc64 General Statistics");
        ArrayList<Object> generalLore = new ArrayList<Object>();
        generalLore.add(String.valueOf(ChatColor.GRAY) + "Your overall dungeon activity");
        generalLore.add("");
        generalLore.add(String.valueOf(ChatColor.YELLOW) + "Player: " + String.valueOf(ChatColor.WHITE) + player.getName());
        generalLore.add(String.valueOf(ChatColor.YELLOW) + "Dungeons Created: " + String.valueOf(ChatColor.WHITE) + "0");
        generalLore.add(String.valueOf(ChatColor.YELLOW) + "Dungeons Completed: " + String.valueOf(ChatColor.WHITE) + "0");
        generalLore.add(String.valueOf(ChatColor.YELLOW) + "Total Playtime: " + String.valueOf(ChatColor.WHITE) + "0h 0m");
        generalLore.add(String.valueOf(ChatColor.YELLOW) + "First Dungeon: " + String.valueOf(ChatColor.WHITE) + "Never");
        generalLore.add(String.valueOf(ChatColor.YELLOW) + "Last Activity: " + String.valueOf(ChatColor.WHITE) + "Now");
        generalMeta.setLore(generalLore);
        general.setItemMeta(generalMeta);
        inv.setItem(11, general);
        ItemStack themes = new ItemStack(Material.PAINTING);
        ItemMeta themesMeta = themes.getItemMeta();
        themesMeta.setDisplayName(String.valueOf(ChatColor.LIGHT_PURPLE) + "\ud83c\udfa8 Theme Preferences");
        ArrayList<Object> themesLore = new ArrayList<Object>();
        themesLore.add(String.valueOf(ChatColor.GRAY) + "Your favorite dungeon themes");
        themesLore.add("");
        themesLore.add(String.valueOf(ChatColor.YELLOW) + "Castle Dungeons: " + String.valueOf(ChatColor.WHITE) + "0 created");
        themesLore.add(String.valueOf(ChatColor.YELLOW) + "Cave Systems: " + String.valueOf(ChatColor.WHITE) + "0 created");
        themesLore.add(String.valueOf(ChatColor.YELLOW) + "Ancient Temples: " + String.valueOf(ChatColor.WHITE) + "0 created");
        themesLore.add("");
        themesLore.add(String.valueOf(ChatColor.YELLOW) + "Favorite Theme: " + String.valueOf(ChatColor.WHITE) + "None yet");
        themesLore.add(String.valueOf(ChatColor.YELLOW) + "Most Challenging: " + String.valueOf(ChatColor.WHITE) + "None yet");
        themesMeta.setLore(themesLore);
        themes.setItemMeta(themesMeta);
        inv.setItem(13, themes);
        ItemStack achievements = new ItemStack(Material.GOLDEN_APPLE);
        ItemMeta achievementsMeta = achievements.getItemMeta();
        achievementsMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83c\udfc6 Achievements");
        ArrayList<Object> achievementsLore = new ArrayList<Object>();
        achievementsLore.add(String.valueOf(ChatColor.GRAY) + "Your dungeon achievements");
        achievementsLore.add("");
        achievementsLore.add(String.valueOf(ChatColor.GREEN) + "\u2713 First Steps" + String.valueOf(ChatColor.GRAY) + " - Create your first dungeon");
        achievementsLore.add(String.valueOf(ChatColor.GRAY) + "\u2717 Master Builder" + String.valueOf(ChatColor.GRAY) + " - Create 10 dungeons");
        achievementsLore.add(String.valueOf(ChatColor.GRAY) + "\u2717 Theme Explorer" + String.valueOf(ChatColor.GRAY) + " - Try all themes");
        achievementsLore.add(String.valueOf(ChatColor.GRAY) + "\u2717 Speed Runner" + String.valueOf(ChatColor.GRAY) + " - Complete in under 10 minutes");
        achievementsLore.add(String.valueOf(ChatColor.GRAY) + "\u2717 Architect" + String.valueOf(ChatColor.GRAY) + " - Create a massive dungeon");
        achievementsLore.add("");
        achievementsLore.add(String.valueOf(ChatColor.YELLOW) + "Progress: " + String.valueOf(ChatColor.WHITE) + "0/15 achievements");
        achievementsMeta.setLore(achievementsLore);
        achievements.setItemMeta(achievementsMeta);
        inv.setItem(15, achievements);
        ItemStack recent = new ItemStack(Material.CLOCK);
        ItemMeta recentMeta = recent.getItemMeta();
        recentMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u23f0 Recent Activity");
        ArrayList<Object> recentLore = new ArrayList<Object>();
        recentLore.add(String.valueOf(ChatColor.GRAY) + "Your latest dungeon activities");
        recentLore.add("");
        recentLore.add(String.valueOf(ChatColor.GRAY) + "No recent activity");
        recentLore.add(String.valueOf(ChatColor.GRAY) + "Create your first dungeon to");
        recentLore.add(String.valueOf(ChatColor.GRAY) + "start tracking your progress!");
        recentMeta.setLore(recentLore);
        recent.setItemMeta(recentMeta);
        inv.setItem(29, recent);
        ItemStack server = new ItemStack(Material.BEACON);
        ItemMeta serverMeta = server.getItemMeta();
        serverMeta.setDisplayName(String.valueOf(ChatColor.BLUE) + "\ud83c\udf10 Server Statistics");
        ArrayList<Object> serverLore = new ArrayList<Object>();
        serverLore.add(String.valueOf(ChatColor.GRAY) + "Global server dungeon stats");
        serverLore.add("");
        int totalDungeons = plugin.getDungeonManager().getDungeons().size();
        serverLore.add(String.valueOf(ChatColor.YELLOW) + "Active Dungeons: " + String.valueOf(ChatColor.WHITE) + totalDungeons);
        serverLore.add(String.valueOf(ChatColor.YELLOW) + "Total Created: " + String.valueOf(ChatColor.WHITE) + "0");
        serverLore.add(String.valueOf(ChatColor.YELLOW) + "Most Popular Theme: " + String.valueOf(ChatColor.WHITE) + "Castle");
        serverLore.add(String.valueOf(ChatColor.YELLOW) + "Average Completion: " + String.valueOf(ChatColor.WHITE) + "25 minutes");
        serverLore.add("");
        serverLore.add(String.valueOf(ChatColor.GRAY) + "Be part of the community!");
        serverMeta.setLore(serverLore);
        server.setItemMeta(serverMeta);
        inv.setItem(31, server);
        ItemStack leaderboard = new ItemStack(Material.DIAMOND);
        ItemMeta leaderboardMeta = leaderboard.getItemMeta();
        leaderboardMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83e\udd47 Leaderboards");
        ArrayList<Object> leaderboardLore = new ArrayList<Object>();
        leaderboardLore.add(String.valueOf(ChatColor.GRAY) + "Top dungeon creators");
        leaderboardLore.add("");
        leaderboardLore.add(String.valueOf(ChatColor.GOLD) + "1. " + String.valueOf(ChatColor.WHITE) + "Player1" + String.valueOf(ChatColor.GRAY) + " - 25 dungeons");
        leaderboardLore.add(String.valueOf(ChatColor.GRAY) + "2. " + String.valueOf(ChatColor.WHITE) + "Player2" + String.valueOf(ChatColor.GRAY) + " - 18 dungeons");
        leaderboardLore.add(String.valueOf(ChatColor.GOLD) + "3. " + String.valueOf(ChatColor.WHITE) + "Player3" + String.valueOf(ChatColor.GRAY) + " - 15 dungeons");
        leaderboardLore.add("");
        leaderboardLore.add(String.valueOf(ChatColor.YELLOW) + "Your Rank: " + String.valueOf(ChatColor.WHITE) + "Unranked");
        leaderboardLore.add(String.valueOf(ChatColor.GRAY) + "Create dungeons to climb the ranks!");
        leaderboardMeta.setLore(leaderboardLore);
        leaderboard.setItemMeta(leaderboardMeta);
        inv.setItem(33, leaderboard);
    }

    private static void createNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2190 Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack refresh = new ItemStack(Material.LIME_DYE);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udd04 Refresh Statistics");
        ArrayList<CallSite> refreshLore = new ArrayList<CallSite>();
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Update your statistics")));
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "with the latest data")));
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(53, refresh);
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 45: {
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                            StatisticsGUI.open(clicker, plugin);
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Statistics refreshed!");
                        }
                    }
                }
            }
        }, (Plugin)plugin);
    }
}

