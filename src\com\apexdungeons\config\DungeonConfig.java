/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.Location
 *  org.bukkit.World
 *  org.bukkit.configuration.file.FileConfiguration
 *  org.bukkit.configuration.file.YamlConfiguration
 */
package com.apexdungeons.config;

import com.apexdungeons.ApexDungeons;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

public class DungeonConfig {
    private final ApexDungeons plugin;
    private final Map<String, FileConfiguration> dungeonConfigs = new HashMap<String, FileConfiguration>();
    private final File configDir;

    public DungeonConfig(ApexDungeons plugin) {
        this.plugin = plugin;
        this.configDir = new File(plugin.getDataFolder(), "dungeons");
        if (!this.configDir.exists()) {
            this.configDir.mkdirs();
        }
    }

    public FileConfiguration getDungeonConfig(String dungeonName) {
        if (!this.dungeonConfigs.containsKey(dungeonName)) {
            File configFile = new File(this.configDir, dungeonName + ".yml");
            YamlConfiguration config = YamlConfiguration.loadConfiguration((File)configFile);
            if (!configFile.exists()) {
                this.setDefaults((FileConfiguration)config, dungeonName);
                this.saveDungeonConfig(dungeonName, (FileConfiguration)config);
            }
            this.dungeonConfigs.put(dungeonName, (FileConfiguration)config);
        }
        return this.dungeonConfigs.get(dungeonName);
    }

    public void saveDungeonConfig(String dungeonName, FileConfiguration config) {
        try {
            File configFile = new File(this.configDir, dungeonName + ".yml");
            config.save(configFile);
            this.dungeonConfigs.put(dungeonName, config);
            this.plugin.getLogger().info("Saved configuration for dungeon: " + dungeonName);
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save configuration for dungeon " + dungeonName + ": " + e.getMessage());
        }
    }

    private void setDefaults(FileConfiguration config, String dungeonName) {
        config.set("dungeon.name", (Object)dungeonName);
        config.set("dungeon.created", (Object)System.currentTimeMillis());
        config.set("spawn.use_custom", (Object)false);
        config.set("spawn.world", (Object)"");
        config.set("spawn.x", (Object)0.0);
        config.set("spawn.y", (Object)64.0);
        config.set("spawn.z", (Object)0.0);
        config.set("spawn.yaw", (Object)Float.valueOf(0.0f));
        config.set("spawn.pitch", (Object)Float.valueOf(0.0f));
        config.set("exit.use_custom", (Object)false);
        config.set("exit.world", (Object)"");
        config.set("exit.x", (Object)0.0);
        config.set("exit.y", (Object)64.0);
        config.set("exit.z", (Object)0.0);
        config.set("exit.yaw", (Object)Float.valueOf(0.0f));
        config.set("exit.pitch", (Object)Float.valueOf(0.0f));
        config.set("settings.max_players", (Object)4);
        config.set("settings.time_limit", (Object)0);
        config.set("settings.difficulty", (Object)"normal");
    }

    public void setCustomSpawnLocation(String dungeonName, Location location) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        config.set("spawn.use_custom", (Object)true);
        config.set("spawn.world", (Object)location.getWorld().getName());
        config.set("spawn.x", (Object)location.getX());
        config.set("spawn.y", (Object)location.getY());
        config.set("spawn.z", (Object)location.getZ());
        config.set("spawn.yaw", (Object)Float.valueOf(location.getYaw()));
        config.set("spawn.pitch", (Object)Float.valueOf(location.getPitch()));
        this.saveDungeonConfig(dungeonName, config);
    }

    public void setCustomExitLocation(String dungeonName, Location location) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        config.set("exit.use_custom", (Object)true);
        config.set("exit.world", (Object)location.getWorld().getName());
        config.set("exit.x", (Object)location.getX());
        config.set("exit.y", (Object)location.getY());
        config.set("exit.z", (Object)location.getZ());
        config.set("exit.yaw", (Object)Float.valueOf(location.getYaw()));
        config.set("exit.pitch", (Object)Float.valueOf(location.getPitch()));
        this.saveDungeonConfig(dungeonName, config);
    }

    public Location getCustomSpawnLocation(String dungeonName) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        if (!config.getBoolean("spawn.use_custom", false)) {
            return null;
        }
        String worldName = config.getString("spawn.world");
        World world = Bukkit.getWorld((String)worldName);
        if (world == null) {
            this.plugin.getLogger().warning("World not found for custom spawn: " + worldName);
            return null;
        }
        double x = config.getDouble("spawn.x");
        double y = config.getDouble("spawn.y");
        double z = config.getDouble("spawn.z");
        float yaw = (float)config.getDouble("spawn.yaw");
        float pitch = (float)config.getDouble("spawn.pitch");
        return new Location(world, x, y, z, yaw, pitch);
    }

    public Location getCustomExitLocation(String dungeonName) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        if (!config.getBoolean("exit.use_custom", false)) {
            return null;
        }
        String worldName = config.getString("exit.world");
        World world = Bukkit.getWorld((String)worldName);
        if (world == null) {
            this.plugin.getLogger().warning("World not found for custom exit: " + worldName);
            return null;
        }
        double x = config.getDouble("exit.x");
        double y = config.getDouble("exit.y");
        double z = config.getDouble("exit.z");
        float yaw = (float)config.getDouble("exit.yaw");
        float pitch = (float)config.getDouble("exit.pitch");
        return new Location(world, x, y, z, yaw, pitch);
    }

    public void removeCustomSpawnLocation(String dungeonName) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        config.set("spawn.use_custom", (Object)false);
        this.saveDungeonConfig(dungeonName, config);
    }

    public void removeCustomExitLocation(String dungeonName) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        config.set("exit.use_custom", (Object)false);
        this.saveDungeonConfig(dungeonName, config);
    }

    public boolean hasCustomSpawnLocation(String dungeonName) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        return config.getBoolean("spawn.use_custom", false);
    }

    public boolean hasCustomExitLocation(String dungeonName) {
        FileConfiguration config = this.getDungeonConfig(dungeonName);
        return config.getBoolean("exit.use_custom", false);
    }

    public Map<String, FileConfiguration> getAllDungeonConfigs() {
        HashMap<String, FileConfiguration> configs = new HashMap<String, FileConfiguration>();
        File[] files = this.configDir.listFiles((dir, name) -> name.endsWith(".yml"));
        if (files != null) {
            for (File file : files) {
                String dungeonName = file.getName().replace(".yml", "");
                configs.put(dungeonName, this.getDungeonConfig(dungeonName));
            }
        }
        return configs;
    }

    public void deleteDungeonConfig(String dungeonName) {
        File configFile = new File(this.configDir, dungeonName + ".yml");
        if (configFile.exists()) {
            configFile.delete();
        }
        this.dungeonConfigs.remove(dungeonName);
    }
}

