/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.EquipmentSlot
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.wand;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.WandGUI;
import java.util.List;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.EquipmentSlot;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class WandManager
implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey wandKey;

    public WandManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.wandKey = new NamespacedKey((Plugin)plugin, "wand");
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    private ItemStack createWandItem() {
        ItemStack wand = new ItemStack(Material.BRUSH);
        ItemMeta meta = wand.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "Dungeon Architect Wand");
        meta.setLore(List.of(String.valueOf(ChatColor.GRAY) + "Right click: Preview & place room", String.valueOf(ChatColor.GRAY) + "Left click: Rotate 90 deg", String.valueOf(ChatColor.GRAY) + "Sneak + scroll: Change height", String.valueOf(ChatColor.GRAY) + "Sneak + right click: Room selector"));
        meta.getPersistentDataContainer().set(this.wandKey, PersistentDataType.INTEGER, (Object)1);
        wand.setItemMeta(meta);
        return wand;
    }

    public void giveWand(Player player) {
        ItemStack wand = this.createWandItem();
        player.getInventory().addItem(new ItemStack[]{wand}).forEach((slot, item) -> player.getWorld().dropItem(player.getLocation(), item));
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "You have been given the Dungeon Architect Wand.");
    }

    private boolean isWand(ItemStack stack) {
        if (stack == null || !stack.hasItemMeta()) {
            return false;
        }
        ItemMeta meta = stack.getItemMeta();
        return meta.getPersistentDataContainer().has(this.wandKey, PersistentDataType.INTEGER);
    }

    @EventHandler
    public void onInteract(PlayerInteractEvent event) {
        if (event.getHand() != EquipmentSlot.HAND) {
            return;
        }
        if (!this.isWand(event.getItem())) {
            return;
        }
        event.setCancelled(true);
        Player player = event.getPlayer();
        switch (event.getAction()) {
            case RIGHT_CLICK_AIR: 
            case RIGHT_CLICK_BLOCK: {
                WandGUI.open(player, this.plugin);
                break;
            }
            case LEFT_CLICK_AIR: 
            case LEFT_CLICK_BLOCK: {
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Rotation controls not implemented yet.");
                break;
            }
        }
    }
}

