/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 *  org.bukkit.configuration.file.YamlConfiguration
 */
package com.apexdungeons.mobs;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.Material;
import org.bukkit.configuration.file.YamlConfiguration;

public class DungeonMobConfig {
    private final String id;
    private final String displayName;
    private final String description;
    private final String category;
    private final String difficulty;
    private final Material iconMaterial;
    private final String mobType;
    private final int spawnRadius;
    private final int cooldownMin;
    private final int cooldownMax;
    private final int maxConcurrent;
    private final List<String> spawnCommands;
    private final List<String> builderInfo;
    private final boolean isBoss;

    public DungeonMobConfig(String id, String displayName, String description, String category, String difficulty, Material iconMaterial, String mobType, int spawnRadius, int cooldownMin, int cooldownMax, int maxConcurrent, List<String> spawnCommands, List<String> builderInfo, boolean isBoss) {
        this.id = id;
        this.displayName = displayName;
        this.description = description;
        this.category = category;
        this.difficulty = difficulty;
        this.iconMaterial = iconMaterial;
        this.mobType = mobType;
        this.spawnRadius = spawnRadius;
        this.cooldownMin = cooldownMin;
        this.cooldownMax = cooldownMax;
        this.maxConcurrent = maxConcurrent;
        this.spawnCommands = spawnCommands;
        this.builderInfo = builderInfo;
        this.isBoss = isBoss;
    }

    public static DungeonMobConfig loadFromFile(File file) {
        try {
            YamlConfiguration config = YamlConfiguration.loadConfiguration((File)file);
            String id = file.getName().replace(".yml", "");
            String displayName = config.getString("display_name", id);
            String description = config.getString("description", "A dungeon mob");
            String category = config.getString("category", "basic");
            String difficulty = config.getString("difficulty", "normal");
            Material iconMaterial = Material.ZOMBIE_HEAD;
            String iconStr = config.getString("icon", "ZOMBIE_HEAD");
            try {
                iconMaterial = Material.valueOf((String)iconStr.toUpperCase());
            }
            catch (IllegalArgumentException illegalArgumentException) {
                // empty catch block
            }
            String mobType = config.getString("mob_type", "ZOMBIE");
            int spawnRadius = config.getInt("spawn_radius", 6);
            int cooldownMin = config.getInt("cooldown.min", 30);
            int cooldownMax = config.getInt("cooldown.max", 60);
            int maxConcurrent = config.getInt("max_concurrent", 2);
            boolean isBoss = config.getBoolean("is_boss", false);
            List<String> spawnCommands = new ArrayList();
            if (config.isList("spawn_commands")) {
                spawnCommands = config.getStringList("spawn_commands");
            } else if (config.isString("spawn_commands")) {
                spawnCommands.add(config.getString("spawn_commands"));
            }
            List<String> builderInfo = new ArrayList();
            if (config.isList("builder_info")) {
                builderInfo = config.getStringList("builder_info");
            } else if (config.isString("builder_info")) {
                builderInfo.add(config.getString("builder_info"));
            }
            return new DungeonMobConfig(id, displayName, description, category, difficulty, iconMaterial, mobType, spawnRadius, cooldownMin, cooldownMax, maxConcurrent, spawnCommands, builderInfo, isBoss);
        }
        catch (Exception e) {
            System.err.println("Failed to load mob config from " + file.getName() + ": " + e.getMessage());
            return null;
        }
    }

    public String getId() {
        return this.id;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public String getDescription() {
        return this.description;
    }

    public String getCategory() {
        return this.category;
    }

    public String getDifficulty() {
        return this.difficulty;
    }

    public Material getIconMaterial() {
        return this.iconMaterial;
    }

    public String getMobType() {
        return this.mobType;
    }

    public int getSpawnRadius() {
        return this.spawnRadius;
    }

    public int getCooldownMin() {
        return this.cooldownMin;
    }

    public int getCooldownMax() {
        return this.cooldownMax;
    }

    public int getMaxConcurrent() {
        return this.maxConcurrent;
    }

    public List<String> getSpawnCommands() {
        return this.spawnCommands;
    }

    public List<String> getBuilderInfo() {
        return this.builderInfo;
    }

    public boolean isBoss() {
        return this.isBoss;
    }

    public String getDifficultyColor() {
        switch (this.difficulty.toLowerCase()) {
            case "easy": {
                return "\u00a7a";
            }
            case "normal": {
                return "\u00a7e";
            }
            case "hard": {
                return "\u00a7c";
            }
            case "boss": {
                return "\u00a74";
            }
            case "elite": {
                return "\u00a75";
            }
        }
        return "\u00a77";
    }

    public String getCategoryColor() {
        switch (this.category.toLowerCase()) {
            case "basic": {
                return "\u00a77";
            }
            case "undead": {
                return "\u00a78";
            }
            case "magical": {
                return "\u00a7d";
            }
            case "beast": {
                return "\u00a76";
            }
            case "boss": {
                return "\u00a74";
            }
            case "elite": {
                return "\u00a75";
            }
        }
        return "\u00a7f";
    }

    public static String createSampleConfig() {
        return "# Dungeon Mob Configuration\n# This file defines a custom mob for dungeon spawning\n\ndisplay_name: \"Dungeon Zombie\"\ndescription: \"A basic undead mob for dungeon encounters\"\ncategory: \"undead\"  # Categories: basic, undead, magical, beast, boss, elite\ndifficulty: \"normal\"  # Difficulties: easy, normal, hard, boss, elite\nicon: \"ZOMBIE_HEAD\"  # Material for GUI display\n\n# Mob spawning configuration\nmob_type: \"ZOMBIE\"\nspawn_radius: 6\ncooldown:\n  min: 30\n  max: 60\nmax_concurrent: 2\nis_boss: false\n\n# Commands to execute when spawning (use %x%, %y%, %z% for coordinates)\nspawn_commands:\n  - \"summon zombie %x% %y% %z% {CustomName:'\\\"Dungeon Zombie\\\"'}\"\n\n# Information shown to builders (not players)\nbuilder_info:\n  - \"Basic undead mob\"\n  - \"Spawns every 30-60 seconds\"\n  - \"Max 2 concurrent mobs\"\n";
    }
}

