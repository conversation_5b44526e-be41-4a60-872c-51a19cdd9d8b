/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.block.BlockBreakEvent
 *  org.bukkit.event.block.BlockPlaceEvent
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.blocks;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class DungeonBlockManager
implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey startBlockKey;
    private final NamespacedKey endBlockKey;
    private final NamespacedKey dungeonNameKey;
    private final Map<UUID, DungeonSession> activeSessions = new HashMap<UUID, DungeonSession>();

    public DungeonBlockManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.startBlockKey = new NamespacedKey((Plugin)plugin, "dungeon_start_block");
        this.endBlockKey = new NamespacedKey((Plugin)plugin, "dungeon_end_block");
        this.dungeonNameKey = new NamespacedKey((Plugin)plugin, "dungeon_name");
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public ItemStack createStartBlockItem() {
        ItemStack item = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(String.valueOf(ChatColor.GREEN) + "Dungeon Start Block");
            meta.setLore(Arrays.asList(String.valueOf(ChatColor.GRAY) + "Place this block in a dungeon to create", String.valueOf(ChatColor.GRAY) + "a start point for dungeon challenges.", String.valueOf(ChatColor.YELLOW) + "Right-click to begin the challenge!"));
            meta.getPersistentDataContainer().set(this.startBlockKey, PersistentDataType.BYTE, (Object)1);
            item.setItemMeta(meta);
        }
        return item;
    }

    public ItemStack createEndBlockItem() {
        ItemStack item = new ItemStack(Material.DIAMOND_BLOCK);
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(String.valueOf(ChatColor.AQUA) + "Dungeon End Block");
            meta.setLore(Arrays.asList(String.valueOf(ChatColor.GRAY) + "Place this block in a dungeon to create", String.valueOf(ChatColor.GRAY) + "a completion point for dungeon challenges.", String.valueOf(ChatColor.YELLOW) + "Right-click to complete the challenge!"));
            meta.getPersistentDataContainer().set(this.endBlockKey, PersistentDataType.BYTE, (Object)1);
            item.setItemMeta(meta);
        }
        return item;
    }

    public void markAsStartBlock(Block block, String dungeonName) {
        block.setType(Material.EMERALD_BLOCK);
        Location loc = block.getLocation();
        this.plugin.getLogger().info("Marked block at " + this.locationToString(loc) + " as start block for dungeon: " + dungeonName);
    }

    public void markAsEndBlock(Block block, String dungeonName) {
        block.setType(Material.DIAMOND_BLOCK);
        Location loc = block.getLocation();
        this.plugin.getLogger().info("Marked block at " + this.locationToString(loc) + " as end block for dungeon: " + dungeonName);
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        Block block = event.getClickedBlock();
        if (block == null) {
            return;
        }
        Player player = event.getPlayer();
        if (!this.plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            return;
        }
        if (block.getType() == Material.EMERALD_BLOCK) {
            this.handleStartBlockInteraction(player, block);
            event.setCancelled(true);
        } else if (block.getType() == Material.DIAMOND_BLOCK) {
            this.handleEndBlockInteraction(player, block);
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Block block = event.getBlock();
        Player player = event.getPlayer();
        if (!this.plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            return;
        }
        String dungeonName = this.plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld());
        if (dungeonName == null) {
            return;
        }
        if (!this.canPlayerBuild(player, dungeonName)) {
            event.setCancelled(true);
            player.sendMessage(String.valueOf(ChatColor.RED) + "You cannot break blocks in this dungeon!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "This dungeon is in playable mode. Only the owner can edit it.");
            return;
        }
        if (block.getType() == Material.EMERALD_BLOCK || block.getType() == Material.DIAMOND_BLOCK) {
            event.setDropItems(false);
            if (this.hasActiveSession(player)) {
                this.clearSession(player);
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Your dungeon challenge has been cancelled due to block removal.");
            }
            String blockType = block.getType() == Material.EMERALD_BLOCK ? "Start Block" : "End Block";
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Dungeon " + blockType + " removed (no items dropped).");
            this.plugin.getLogger().info("Player " + player.getName() + " broke a dungeon " + blockType.toLowerCase() + " at " + this.locationToString(block.getLocation()));
        }
    }

    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        if (!this.plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            return;
        }
        String dungeonName = this.plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld());
        if (dungeonName == null) {
            return;
        }
        if (!this.canPlayerBuild(player, dungeonName)) {
            event.setCancelled(true);
            player.sendMessage(String.valueOf(ChatColor.RED) + "You cannot place blocks in this dungeon!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "This dungeon is in playable mode. Only the owner can edit it.");
        }
    }

    private void handleStartBlockInteraction(Player player, Block block) {
        UUID playerId = player.getUniqueId();
        if (this.activeSessions.containsKey(playerId)) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You already have an active dungeon challenge!");
            return;
        }
        String dungeonName = this.plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld());
        if (dungeonName == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Could not determine dungeon name!");
            return;
        }
        DungeonSession session = new DungeonSession(dungeonName, System.currentTimeMillis());
        this.activeSessions.put(playerId, session);
        Location loc = block.getLocation();
        player.playSound(loc, Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);
        loc.getWorld().spawnParticle(Particle.ENCHANT, loc.add(0.5, 1.0, 0.5), 30, 0.5, 0.5, 0.5, 0.1);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2726 Dungeon Challenge Started!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Find and activate the " + String.valueOf(ChatColor.AQUA) + "End Block" + String.valueOf(ChatColor.GRAY) + " to complete the challenge.");
        this.plugin.getLogger().info("Player " + player.getName() + " started dungeon challenge in: " + dungeonName);
    }

    private void handleEndBlockInteraction(Player player, Block block) {
        UUID playerId = player.getUniqueId();
        DungeonSession session = this.activeSessions.get(playerId);
        if (session == null) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You must activate a " + String.valueOf(ChatColor.GREEN) + "Start Block" + String.valueOf(ChatColor.YELLOW) + " first!");
            return;
        }
        long completionTime = System.currentTimeMillis() - session.getStartTime();
        long seconds = completionTime / 1000L;
        long minutes = seconds / 60L;
        seconds %= 60L;
        this.activeSessions.remove(playerId);
        Location loc = block.getLocation();
        player.playSound(loc, Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
        player.playSound(loc, Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
        loc.getWorld().spawnParticle(Particle.FIREWORK, loc.add(0.5, 1.0, 0.5), 50, 1.0, 1.0, 1.0, 0.1);
        loc.getWorld().spawnParticle(Particle.END_ROD, loc, 20, 0.5, 0.5, 0.5, 0.1);
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2726 " + String.valueOf(ChatColor.GREEN) + "Dungeon Challenge Completed!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Time: " + String.valueOf(ChatColor.YELLOW) + String.format("%d:%02d", minutes, seconds));
        this.giveCompletionRewards(player, session);
        this.handleDungeonExit(player, session.getDungeonName());
        this.plugin.getLogger().info("Player " + player.getName() + " completed dungeon challenge in " + session.getDungeonName() + " (Time: " + String.format("%d:%02d", minutes, seconds) + ")");
    }

    private void handleDungeonExit(Player player, String dungeonName) {
        Location customExit = this.plugin.getDungeonConfig().getCustomExitLocation(dungeonName);
        if (customExit != null) {
            player.teleport(customExit);
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "Teleported to custom exit location!");
            this.plugin.getLogger().info("Player " + player.getName() + " teleported to custom exit for dungeon: " + dungeonName);
        } else {
            Location originalLocation = this.plugin.getPlayerLocationManager().getPlayerReturnLocation(player);
            if (originalLocation != null) {
                player.teleport(originalLocation);
                this.plugin.getPlayerLocationManager().clearPlayerLocation(player);
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Returned to your original location!");
            } else {
                Location mainSpawn = ((World)this.plugin.getServer().getWorlds().get(0)).getSpawnLocation();
                player.teleport(mainSpawn);
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Returned to main world spawn.");
            }
        }
    }

    private void giveCompletionRewards(Player player, DungeonSession session) {
        player.giveExp(100);
        ItemStack reward1 = new ItemStack(Material.DIAMOND, 2);
        ItemStack reward2 = new ItemStack(Material.EMERALD, 5);
        ItemStack reward3 = new ItemStack(Material.GOLDEN_APPLE, 1);
        player.getInventory().addItem(new ItemStack[]{reward1, reward2, reward3});
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Rewards: " + String.valueOf(ChatColor.YELLOW) + "2 Diamonds, 5 Emeralds, 1 Golden Apple, 100 XP");
    }

    public DungeonSession getActiveSession(Player player) {
        return this.activeSessions.get(player.getUniqueId());
    }

    public boolean hasActiveSession(Player player) {
        return this.activeSessions.containsKey(player.getUniqueId());
    }

    public void clearSession(Player player) {
        this.activeSessions.remove(player.getUniqueId());
    }

    public Map<UUID, DungeonSession> getActiveSessions() {
        return new HashMap<UUID, DungeonSession>(this.activeSessions);
    }

    private boolean canPlayerBuild(Player player, String dungeonName) {
        if (player.hasPermission("apexdungeons.admin")) {
            return true;
        }
        DungeonInstance dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            return false;
        }
        if (player.getName().equals(dungeon.getCreator())) {
            return true;
        }
        boolean hasActiveSessions = this.activeSessions.values().stream().anyMatch(session -> session.getDungeonName().equals(dungeonName));
        if (hasActiveSessions) {
            return false;
        }
        return player.hasPermission("apexdungeons.build") || player.getName().equals(dungeon.getCreator());
    }

    public void shutdown() {
        this.activeSessions.clear();
        this.plugin.getLogger().info("DungeonBlockManager shutdown complete.");
    }

    private String locationToString(Location loc) {
        return String.format("%s %.1f,%.1f,%.1f", loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }

    public static class DungeonSession {
        private final String dungeonName;
        private final long startTime;

        public DungeonSession(String dungeonName, long startTime) {
            this.dungeonName = dungeonName;
            this.startTime = startTime;
        }

        public String getDungeonName() {
            return this.dungeonName;
        }

        public long getStartTime() {
            return this.startTime;
        }
    }
}

