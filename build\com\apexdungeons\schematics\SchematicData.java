/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 */
package com.apexdungeons.schematics;

import org.bukkit.Material;

public class SchematicData {
    private final String name;
    private final int width;
    private final int height;
    private final int depth;
    private final Material[][][] blocks;

    public SchematicData(String name, int width, int height, int depth, Material[][][] blocks) {
        this.name = name;
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.blocks = blocks;
    }

    public String getName() {
        return this.name;
    }

    public int getWidth() {
        return this.width;
    }

    public int getHeight() {
        return this.height;
    }

    public int getDepth() {
        return this.depth;
    }

    public Material[][][] getBlocks() {
        return this.blocks;
    }

    public Material getBlockAt(int x, int y, int z) {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height || z < 0 || z >= this.depth) {
            return Material.AIR;
        }
        return this.blocks[y][z][x];
    }

    public void setBlockAt(int x, int y, int z, Material material) {
        if (x >= 0 && x < this.width && y >= 0 && y < this.height && z >= 0 && z < this.depth) {
            this.blocks[y][z][x] = material;
        }
    }

    public int getTotalBlocks() {
        return this.width * this.height * this.depth;
    }

    public int getSolidBlocks() {
        int count = 0;
        for (int y = 0; y < this.height; ++y) {
            for (int z = 0; z < this.depth; ++z) {
                for (int x = 0; x < this.width; ++x) {
                    if (this.blocks[y][z][x] == Material.AIR) continue;
                    ++count;
                }
            }
        }
        return count;
    }

    public SchematicData copy() {
        Material[][][] newBlocks = new Material[this.height][this.depth][this.width];
        for (int y = 0; y < this.height; ++y) {
            for (int z = 0; z < this.depth; ++z) {
                for (int x = 0; x < this.width; ++x) {
                    newBlocks[y][z][x] = this.blocks[y][z][x];
                }
            }
        }
        return new SchematicData(this.name + "_copy", this.width, this.height, this.depth, newBlocks);
    }

    public SchematicData rotateClockwise() {
        Material[][][] rotatedBlocks = new Material[this.height][this.width][this.depth];
        for (int y = 0; y < this.height; ++y) {
            for (int z = 0; z < this.depth; ++z) {
                for (int x = 0; x < this.width; ++x) {
                    rotatedBlocks[y][x][this.depth - 1 - z] = this.blocks[y][z][x];
                }
            }
        }
        return new SchematicData(this.name + "_rotated", this.depth, this.height, this.width, rotatedBlocks);
    }

    public SchematicData mirrorX() {
        Material[][][] mirroredBlocks = new Material[this.height][this.depth][this.width];
        for (int y = 0; y < this.height; ++y) {
            for (int z = 0; z < this.depth; ++z) {
                for (int x = 0; x < this.width; ++x) {
                    mirroredBlocks[y][z][this.width - 1 - x] = this.blocks[y][z][x];
                }
            }
        }
        return new SchematicData(this.name + "_mirrored", this.width, this.height, this.depth, mirroredBlocks);
    }

    public SchematicData mirrorZ() {
        Material[][][] mirroredBlocks = new Material[this.height][this.depth][this.width];
        for (int y = 0; y < this.height; ++y) {
            for (int z = 0; z < this.depth; ++z) {
                for (int x = 0; x < this.width; ++x) {
                    mirroredBlocks[y][this.depth - 1 - z][x] = this.blocks[y][z][x];
                }
            }
        }
        return new SchematicData(this.name + "_mirrored_z", this.width, this.height, this.depth, mirroredBlocks);
    }

    public String toString() {
        return String.format("SchematicData{name='%s', dimensions=%dx%dx%d, solidBlocks=%d}", this.name, this.width, this.height, this.depth, this.getSolidBlocks());
    }
}

