/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.gui.EnhancedMainGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class AdminGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_PURPLE) + "\u2699\ufe0f Soaps Dungeons Admin";

    public static void open(Player player, final ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        AdminGUI.fillBackground(inv);
        AdminGUI.createAdminButtons(inv, plugin);
        AdminGUI.createDungeonToolButtons(inv, plugin);
        AdminGUI.createNavigationButtons(inv);
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player)e.getWhoClicked();
                    switch (slot) {
                        case 10: {
                            plugin.getDungeonManager().purgeDungeons();
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "All dungeons purged.");
                            break;
                        }
                        case 12: {
                            DungeonInstance inst = plugin.getDungeonManager().getDungeonByPlayer(clicker);
                            if (inst != null) {
                                inst.spawnBoss();
                                clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Boss spawned in dungeon " + inst.getName());
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.RED) + "You are not inside a dungeon.");
                            break;
                        }
                        case 14: {
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Reloading schematics...");
                            plugin.getSchematicManager().reloadSchematics();
                            int count = plugin.getSchematicManager().getLoadedSchematicNames().size();
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Schematics reloaded! " + count + " available.");
                            break;
                        }
                        case 16: {
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Creating test superflat world...");
                            plugin.getWorldManager().createDungeonWorld("test").thenAccept(world -> {
                                if (world != null) {
                                    Location spawnLoc = new Location(world, 0.0, 64.0, 0.0);
                                    clicker.teleport(spawnLoc);
                                    clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Test world created! You've been teleported to: " + world.getName());
                                    plugin.getPlayerLocationManager().storePlayerLocation(clicker, "test");
                                } else {
                                    clicker.sendMessage(String.valueOf(ChatColor.RED) + "Failed to create test world!");
                                }
                            });
                            break;
                        }
                        case 20: 
                        case 22: 
                        case 24: 
                        case 31: 
                        case 32: 
                        case 33: 
                        case 34: 
                        case 40: {
                            ItemStack item = e.getCurrentItem();
                            if (item == null || item.getType() == Material.AIR) break;
                            if (!AdminGUI.hasSimilarTool(clicker, item)) {
                                clicker.getInventory().addItem(new ItemStack[]{item.clone()});
                                clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Tool added to your inventory!");
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "You already have this tool!");
                            break;
                        }
                        case 45: {
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                        }
                    }
                }
            }
        }, (Plugin)pl);
        player.openInventory(inv);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createAdminButtons(Inventory inv, ApexDungeons plugin) {
        ItemStack purge = new ItemStack(Material.BARRIER);
        ItemMeta pm = purge.getItemMeta();
        pm.setDisplayName(String.valueOf(ChatColor.RED) + "Purge Dungeons");
        ArrayList<Object> purgeLore = new ArrayList<Object>();
        purgeLore.add(String.valueOf(ChatColor.GRAY) + "Remove all active dungeons");
        purgeLore.add(String.valueOf(ChatColor.GRAY) + "and clean up world files");
        purgeLore.add("");
        purgeLore.add(String.valueOf(ChatColor.RED) + "\u26a0 This action cannot be undone!");
        pm.setLore(purgeLore);
        purge.setItemMeta(pm);
        inv.setItem(10, purge);
        ItemStack boss = new ItemStack(Material.NETHER_STAR);
        ItemMeta bm = boss.getItemMeta();
        bm.setDisplayName(String.valueOf(ChatColor.GOLD) + "Spawn Boss");
        ArrayList<Object> bossLore = new ArrayList<Object>();
        bossLore.add(String.valueOf(ChatColor.GRAY) + "Spawn the boss in your");
        bossLore.add(String.valueOf(ChatColor.GRAY) + "current dungeon location");
        bossLore.add("");
        bossLore.add(String.valueOf(ChatColor.YELLOW) + "Must be inside a dungeon!");
        bm.setLore(bossLore);
        boss.setItemMeta(bm);
        inv.setItem(12, boss);
        ItemStack reload = new ItemStack(Material.KNOWLEDGE_BOOK);
        ItemMeta rm = reload.getItemMeta();
        rm.setDisplayName(String.valueOf(ChatColor.AQUA) + "Reload Schematics");
        ArrayList<Object> reloadLore = new ArrayList<Object>();
        reloadLore.add(String.valueOf(ChatColor.GRAY) + "Reload all schematic files");
        reloadLore.add(String.valueOf(ChatColor.GRAY) + "from the schematics folder");
        reloadLore.add("");
        reloadLore.add(String.valueOf(ChatColor.GREEN) + "Use after adding new files!");
        rm.setLore(reloadLore);
        reload.setItemMeta(rm);
        inv.setItem(14, reload);
        ItemStack testWorld = new ItemStack(Material.GRASS_BLOCK);
        ItemMeta tm = testWorld.getItemMeta();
        tm.setDisplayName(String.valueOf(ChatColor.GREEN) + "Test Superflat World");
        ArrayList<Object> testLore = new ArrayList<Object>();
        testLore.add(String.valueOf(ChatColor.GRAY) + "Create a test superflat world");
        testLore.add(String.valueOf(ChatColor.GRAY) + "to verify world generation");
        testLore.add("");
        testLore.add(String.valueOf(ChatColor.YELLOW) + "Teleports you to test world");
        tm.setLore(testLore);
        testWorld.setItemMeta(tm);
        inv.setItem(16, testWorld);
    }

    private static void createDungeonToolButtons(Inventory inv, ApexDungeons plugin) {
        ItemStack startBlock = plugin.getDungeonBlockManager().createStartBlockItem();
        inv.setItem(20, startBlock);
        ItemStack endBlock = plugin.getDungeonBlockManager().createEndBlockItem();
        inv.setItem(22, endBlock);
        ItemStack connector = plugin.getRoomConnector().createConnectorTool();
        inv.setItem(24, connector);
        ItemStack schematicHeader = new ItemStack(Material.WRITTEN_BOOK);
        ItemMeta headerMeta = schematicHeader.getItemMeta();
        headerMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udccb Schematic Tools");
        ArrayList<Object> headerLore = new ArrayList<Object>();
        headerLore.add(String.valueOf(ChatColor.GRAY) + "Available schematic placement tools:");
        headerLore.add("");
        for (String schematicName : plugin.getSchematicManager().getLoadedSchematicNames()) {
            headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + schematicName);
        }
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.YELLOW) + "Click individual tools below!");
        headerMeta.setLore(headerLore);
        schematicHeader.setItemMeta(headerMeta);
        inv.setItem(30, schematicHeader);
        ArrayList<String> schematicNames = new ArrayList<String>(plugin.getSchematicManager().getLoadedSchematicNames());
        int[] schematicSlots = new int[]{31, 32, 33, 34, 40};
        for (int i = 0; i < Math.min(schematicNames.size(), schematicSlots.length); ++i) {
            String schematicName = (String)schematicNames.get(i);
            ItemStack tool = plugin.getSchematicTool().createSchematicTool(schematicName);
            if (tool == null) continue;
            inv.setItem(schematicSlots[i], tool);
        }
    }

    private static void createNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Back to Main Menu");
        ArrayList<CallSite> backLore = new ArrayList<CallSite>();
        backLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Return to the main")));
        backLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Soaps Dungeons interface")));
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2716 Close Menu");
        ArrayList<CallSite> closeLore = new ArrayList<CallSite>();
        closeLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Close this interface")));
        closeMeta.setLore(closeLore);
        close.setItemMeta(closeMeta);
        inv.setItem(53, close);
    }

    private static boolean hasSimilarTool(Player player, ItemStack tool) {
        if (tool == null || !tool.hasItemMeta()) {
            return false;
        }
        String toolName = tool.getItemMeta().getDisplayName();
        if (toolName == null) {
            return false;
        }
        for (ItemStack item : player.getInventory().getContents()) {
            ItemMeta meta;
            if (item == null || !item.hasItemMeta() || !(meta = item.getItemMeta()).hasDisplayName() || !toolName.equals(meta.getDisplayName())) continue;
            return true;
        }
        return false;
    }
}

