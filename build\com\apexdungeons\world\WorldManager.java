/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.GameRule
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.World
 *  org.bukkit.World$Environment
 *  org.bukkit.WorldCreator
 *  org.bukkit.WorldType
 *  org.bukkit.entity.Player
 *  org.bukkit.generator.WorldInfo
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.world;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import java.io.File;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.GameRule;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.WorldType;
import org.bukkit.entity.Player;
import org.bukkit.generator.WorldInfo;
import org.bukkit.plugin.Plugin;

public class WorldManager {
    private final ApexDungeons plugin;
    private final Map<String, World> dungeonWorlds = new HashMap<String, World>();
    private final Map<String, String> dungeonToWorld = new HashMap<String, String>();
    private final Set<String> worldsBeingCreated = new HashSet<String>();
    private final Map<String, CompletableFuture<World>> pendingWorldCreations = new HashMap<String, CompletableFuture<World>>();

    public WorldManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public CompletableFuture<World> createDungeonWorld(String dungeonName) {
        this.plugin.getLogger().info("[WorldManager] createDungeonWorld called: dungeonName=" + dungeonName + ", primaryThread=" + Bukkit.isPrimaryThread());
        CompletableFuture<World> future = new CompletableFuture<World>();
        Object object = this.pendingWorldCreations;
        synchronized (object) {
            if (this.pendingWorldCreations.containsKey(dungeonName)) {
                this.plugin.getLogger().warning("[WorldManager] Reusing pending world creation future for dungeon: " + dungeonName);
                return this.pendingWorldCreations.get(dungeonName);
            }
            this.pendingWorldCreations.put(dungeonName, future);
        }
        object = this.worldsBeingCreated;
        synchronized (object) {
            if (this.worldsBeingCreated.contains(dungeonName)) {
                this.plugin.getLogger().warning("[WorldManager] World creation already in progress for dungeon: " + dungeonName);
                Map<String, CompletableFuture<World>> map = this.pendingWorldCreations;
                synchronized (map) {
                    this.pendingWorldCreations.remove(dungeonName);
                }
                future.complete(null);
                return future;
            }
            this.worldsBeingCreated.add(dungeonName);
        }
        this.cleanupExistingWorldFiles(dungeonName);
        Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
            this.plugin.getLogger().info("[WorldManager] Starting world creation on main thread for: " + dungeonName);
            try {
                String worldName;
                int attempts = 0;
                while (Bukkit.getWorld((String)(worldName = "dungeon_" + dungeonName + "_" + System.currentTimeMillis() + (String)(attempts > 0 ? "_" + attempts : ""))) != null && ++attempts < 10) {
                }
                if (Bukkit.getWorld((String)worldName) != null) {
                    this.plugin.getLogger().severe("[WorldManager] Could not generate unique world name after 10 attempts for dungeon: " + dungeonName);
                    this.cleanupAndComplete(dungeonName, future, null);
                    return;
                }
                this.plugin.getLogger().info("[WorldManager] Creating dungeon world: " + worldName);
                World world = this.createWorldWithFallbacks(worldName);
                if (world == null) {
                    this.plugin.getLogger().severe("[WorldManager] CRITICAL: All world creation methods failed for dungeon: " + dungeonName);
                    this.logWorldCreationFailure(dungeonName, worldName);
                    this.cleanupAndComplete(dungeonName, future, null);
                    return;
                }
                if (!this.verifyWorldCreation(world, dungeonName)) {
                    this.cleanupAndComplete(dungeonName, future, null);
                    return;
                }
                this.configureWorldQuick(world);
                if (!this.storeMappingsWithVerification(dungeonName, worldName, world)) {
                    this.cleanupAndComplete(dungeonName, future, null);
                    return;
                }
                this.plugin.getLogger().info("[WorldManager] \u2713 Successfully created and verified isolated world '" + worldName + "' for dungeon '" + dungeonName + "'");
                this.cleanupAndComplete(dungeonName, future, world);
            }
            catch (Exception e) {
                this.plugin.getLogger().log(Level.SEVERE, "[WorldManager] Error creating world for dungeon " + dungeonName, e);
                this.cleanupAndComplete(dungeonName, future, null);
            }
        });
        return future;
    }

    private World createWorldWithFallbacks(String worldName) {
        WorldCreator creator;
        World world = null;
        try {
            this.plugin.getLogger().info("[WorldManager] Attempting ultra-simple world creation...");
            creator = new WorldCreator(worldName);
            creator.environment(World.Environment.NORMAL);
            creator.generateStructures(false);
            creator.type(WorldType.FLAT);
            world = creator.createWorld();
            if (world != null) {
                this.plugin.getLogger().info("[WorldManager] \u2713 World created successfully with ultra-simple method");
                return world;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("[WorldManager] Ultra-simple method failed: " + e.getMessage());
        }
        try {
            this.plugin.getLogger().info("[WorldManager] Attempting world creation with modern superflat format...");
            creator = new WorldCreator(worldName);
            creator.environment(World.Environment.NORMAL);
            creator.generateStructures(false);
            creator.type(WorldType.FLAT);
            creator.generatorSettings("3;minecraft:bedrock,60*minecraft:stone,2*minecraft:dirt,minecraft:grass_block;1;");
            world = creator.createWorld();
            if (world != null) {
                this.plugin.getLogger().info("[WorldManager] \u2713 World created successfully with modern format");
                return world;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("[WorldManager] Modern format failed: " + e.getMessage());
        }
        try {
            this.plugin.getLogger().info("[WorldManager] Attempting world creation with legacy superflat format...");
            creator = new WorldCreator(worldName);
            creator.environment(World.Environment.NORMAL);
            creator.generateStructures(false);
            creator.type(WorldType.FLAT);
            creator.generatorSettings("2;7,60x1,3x3,2;1;");
            world = creator.createWorld();
            if (world != null) {
                this.plugin.getLogger().info("[WorldManager] \u2713 World created successfully with legacy format");
                return world;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("[WorldManager] Legacy format failed: " + e.getMessage());
        }
        try {
            this.plugin.getLogger().info("[WorldManager] Attempting world creation with simple flat world...");
            creator = new WorldCreator(worldName);
            creator.environment(World.Environment.NORMAL);
            creator.generateStructures(false);
            creator.type(WorldType.FLAT);
            world = creator.createWorld();
            if (world != null) {
                this.plugin.getLogger().info("[WorldManager] \u2713 World created successfully with simple flat format");
                return world;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("[WorldManager] Simple flat failed: " + e.getMessage());
        }
        try {
            this.plugin.getLogger().info("[WorldManager] Attempting world creation with normal world type...");
            creator = new WorldCreator(worldName);
            creator.environment(World.Environment.NORMAL);
            creator.generateStructures(false);
            world = creator.createWorld();
            if (world != null) {
                this.plugin.getLogger().info("[WorldManager] \u2713 World created successfully with normal world type");
                return world;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[WorldManager] Normal world creation failed: " + e.getMessage());
        }
        try {
            this.plugin.getLogger().info("[WorldManager] Attempting ultra-simple world creation (last resort)...");
            creator = new WorldCreator(worldName);
            world = creator.createWorld();
            if (world != null) {
                this.plugin.getLogger().info("[WorldManager] \u2713 World created successfully with ultra-simple method (last resort)");
                return world;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[WorldManager] All world creation methods failed: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    private boolean verifyWorldCreation(World world, String dungeonName) {
        this.plugin.getLogger().info("[WorldManager] Verifying world creation: " + world.getName());
        this.plugin.getLogger().info("[WorldManager] World generator: " + String.valueOf(world.getGenerator()));
        this.plugin.getLogger().info("[WorldManager] World environment: " + String.valueOf(world.getEnvironment()));
        World verifyWorld = Bukkit.getWorld((String)world.getName());
        if (verifyWorld == null) {
            this.plugin.getLogger().severe("[WorldManager] CRITICAL: World created but not accessible through Bukkit!");
            this.plugin.getLogger().severe("[WorldManager] World name: " + world.getName());
            return false;
        }
        this.plugin.getLogger().info("[WorldManager] \u2713 World verification passed: " + verifyWorld.getName());
        return true;
    }

    private boolean storeMappingsWithVerification(String dungeonName, String worldName, World world) {
        this.dungeonWorlds.put(worldName, world);
        this.dungeonToWorld.put(dungeonName, worldName);
        World testRetrieve = this.getDungeonWorld(dungeonName);
        if (testRetrieve == null) {
            this.plugin.getLogger().severe("[WorldManager] CRITICAL: World mapping verification failed!");
            this.plugin.getLogger().severe("[WorldManager] Dungeon: " + dungeonName + ", World: " + worldName);
            this.dungeonWorlds.remove(worldName);
            this.dungeonToWorld.remove(dungeonName);
            return false;
        }
        this.plugin.getLogger().info("[WorldManager] \u2713 World mapping verified successfully");
        return true;
    }

    private void logWorldCreationFailure(String dungeonName, String worldName) {
        this.plugin.getLogger().severe("[WorldManager] This may be due to:");
        this.plugin.getLogger().severe("[WorldManager] 1. Insufficient server permissions");
        this.plugin.getLogger().severe("[WorldManager] 2. Disk space issues");
        this.plugin.getLogger().severe("[WorldManager] 3. Bukkit/Spigot version compatibility");
        this.plugin.getLogger().severe("[WorldManager] 4. World name conflicts");
        this.plugin.getLogger().severe("[WorldManager] 5. Server configuration issues");
        this.plugin.getLogger().severe("[WorldManager] 6. Plugin conflicts affecting world creation");
        this.plugin.getLogger().info("[WorldManager] Attempted world name: " + worldName);
        this.plugin.getLogger().info("[WorldManager] Server version: " + Bukkit.getVersion());
        this.plugin.getLogger().info("[WorldManager] Available worlds: " + String.valueOf(Bukkit.getWorlds().stream().map(WorldInfo::getName).toList()));
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    private void cleanupAndComplete(String dungeonName, CompletableFuture<World> future, World result) {
        Object object = this.worldsBeingCreated;
        synchronized (object) {
            this.worldsBeingCreated.remove(dungeonName);
        }
        object = this.pendingWorldCreations;
        synchronized (object) {
            this.pendingWorldCreations.remove(dungeonName);
        }
        future.complete(result);
    }

    private void cleanupExistingWorldFiles(String dungeonName) {
        try {
            File worldContainer = Bukkit.getWorldContainer();
            File[] existingDirs = worldContainer.listFiles((dir, name) -> name.startsWith("dungeon_" + dungeonName + "_"));
            if (existingDirs != null && existingDirs.length > 0) {
                this.plugin.getLogger().info("Found " + existingDirs.length + " existing world directories for dungeon: " + dungeonName);
                for (File dir2 : existingDirs) {
                    this.plugin.getLogger().info("Cleaning up old world directory: " + dir2.getName());
                    this.deleteWorldFolder(dir2);
                }
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("Error during world cleanup: " + e.getMessage());
        }
    }

    private void configureWorldQuick(World world) {
        world.setSpawnFlags(false, false);
        world.setGameRule(GameRule.DO_DAYLIGHT_CYCLE, (Object)false);
        world.setGameRule(GameRule.DO_WEATHER_CYCLE, (Object)false);
        world.setGameRule(GameRule.DO_MOB_SPAWNING, (Object)false);
        world.setGameRule(GameRule.KEEP_INVENTORY, (Object)true);
        world.setGameRule(GameRule.ANNOUNCE_ADVANCEMENTS, (Object)false);
        world.setGameRule(GameRule.DO_FIRE_TICK, (Object)false);
        world.setGameRule(GameRule.MOB_GRIEFING, (Object)false);
        world.setGameRule(GameRule.DO_TRADER_SPAWNING, (Object)false);
        world.setGameRule(GameRule.DO_PATROL_SPAWNING, (Object)false);
        world.setGameRule(GameRule.DO_WARDEN_SPAWNING, (Object)false);
        this.plugin.getLogger().info("World configured with anti-structure protection: " + world.getName());
        world.setTime(18000L);
        world.setStorm(false);
        world.setThundering(false);
        world.setSpawnLocation(0, 64, 0);
        this.plugin.getLogger().info("World configured quickly: " + world.getName());
    }

    public World getDungeonWorld(String dungeonName) {
        String worldName = this.dungeonToWorld.get(dungeonName);
        if (worldName != null) {
            World world = this.dungeonWorlds.get(worldName);
            if (world == null && (world = Bukkit.getWorld((String)worldName)) != null) {
                this.dungeonWorlds.put(worldName, world);
                this.plugin.getLogger().info("Re-cached world: " + worldName);
            }
            return world;
        }
        this.plugin.getLogger().warning("No world mapping found for dungeon: " + dungeonName);
        this.plugin.getLogger().info("Available dungeon mappings: " + String.valueOf(this.dungeonToWorld.keySet()));
        return null;
    }

    public CompletableFuture<Boolean> deleteDungeonWorld(String dungeonName) {
        return CompletableFuture.supplyAsync(() -> {
            String worldName = this.dungeonToWorld.get(dungeonName);
            if (worldName == null) {
                return false;
            }
            World world = this.dungeonWorlds.get(worldName);
            if (world == null) {
                return false;
            }
            try {
                Location mainWorldSpawn = ((World)Bukkit.getWorlds().get(0)).getSpawnLocation();
                for (Player player : world.getPlayers()) {
                    player.teleport(mainWorldSpawn);
                    player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You have been teleported out of a dungeon world that is being deleted.");
                }
                boolean unloaded = Bukkit.unloadWorld((World)world, (boolean)false);
                if (!unloaded) {
                    this.plugin.getLogger().warning("Failed to unload world: " + worldName);
                    return false;
                }
                File worldFolder = new File(Bukkit.getWorldContainer(), worldName);
                if (worldFolder.exists()) {
                    this.deleteWorldFolder(worldFolder);
                }
                this.dungeonWorlds.remove(worldName);
                this.dungeonToWorld.remove(dungeonName);
                this.plugin.getLogger().info("Deleted dungeon world: " + worldName);
                return true;
            }
            catch (Exception e) {
                this.plugin.getLogger().log(Level.SEVERE, "Error deleting world for dungeon " + dungeonName, e);
                return false;
            }
        });
    }

    private void deleteWorldFolder(File folder) {
        File[] files;
        if (folder.isDirectory() && (files = folder.listFiles()) != null) {
            for (File file : files) {
                this.deleteWorldFolder(file);
            }
        }
        folder.delete();
    }

    public Location getDungeonSpawnLocation(String dungeonName) {
        this.plugin.getLogger().info("[WorldManager] getDungeonSpawnLocation called for: " + dungeonName);
        Location customSpawn = this.plugin.getDungeonConfig().getCustomSpawnLocation(dungeonName);
        if (customSpawn != null) {
            this.plugin.getLogger().info("[WorldManager] Using custom spawn location for " + dungeonName + ": " + customSpawn.getBlockX() + "," + customSpawn.getBlockY() + "," + customSpawn.getBlockZ());
            return customSpawn;
        }
        World world = this.getDungeonWorld(dungeonName);
        this.plugin.getLogger().info("[WorldManager] Retrieved world for " + dungeonName + ": " + (world != null ? world.getName() : "null"));
        if (world != null) {
            DungeonInstance dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
            this.plugin.getLogger().info("[WorldManager] Retrieved dungeon instance for " + dungeonName + ": " + (dungeon != null ? "found" : "null"));
            if (dungeon != null && dungeon.getOrigin() != null) {
                Location origin = dungeon.getOrigin().clone();
                Location surfaceLocation = this.findSafeSpawnLocation(world, origin.getBlockX(), origin.getBlockZ());
                this.plugin.getLogger().info("Found spawn location for " + dungeonName + " at origin: " + surfaceLocation.getBlockX() + "," + surfaceLocation.getBlockY() + "," + surfaceLocation.getBlockZ());
                return surfaceLocation;
            }
            Location surfaceLocation = this.findSafeSpawnLocation(world, 0, 0);
            this.plugin.getLogger().info("Using fallback spawn location for dungeon '" + dungeonName + "': " + String.valueOf(surfaceLocation));
            return surfaceLocation;
        }
        this.plugin.getLogger().warning("Could not find spawn location for dungeon: " + dungeonName);
        this.plugin.getLogger().info("Available dungeons: " + String.valueOf(this.dungeonToWorld.keySet()));
        return null;
    }

    private Location findSafeSpawnLocation(World world, int x, int z) {
        world.getChunkAt(x >> 4, z >> 4);
        Location loc = new Location(world, (double)x + 0.5, 64.0, (double)z + 0.5);
        if (world.getBlockAt(x, 63, z).getType().isSolid() && world.getBlockAt(x, 64, z).getType().isAir() && world.getBlockAt(x, 65, z).getType().isAir()) {
            return loc;
        }
        world.getBlockAt(x, 63, z).setType(Material.GRASS_BLOCK);
        world.getBlockAt(x, 64, z).setType(Material.AIR);
        world.getBlockAt(x, 65, z).setType(Material.AIR);
        return loc;
    }

    public boolean isDungeonWorld(World world) {
        return this.dungeonWorlds.containsValue(world);
    }

    public String getDungeonNameFromWorld(World world) {
        for (Map.Entry<String, String> entry : this.dungeonToWorld.entrySet()) {
            if (this.dungeonWorlds.get(entry.getValue()) != world) continue;
            return entry.getKey();
        }
        return null;
    }

    public void registerDungeonWorld(String dungeonName, World world) {
        this.dungeonToWorld.put(dungeonName, world.getName());
        this.dungeonWorlds.put(world.getName(), world);
        this.plugin.getLogger().info("Registered dungeon world mapping: " + dungeonName + " -> " + world.getName());
    }

    public Map<String, String> getDungeonToWorldMappings() {
        return new HashMap<String, String>(this.dungeonToWorld);
    }

    public void shutdown() {
        this.plugin.getLogger().info("Cleaning up " + this.dungeonWorlds.size() + " dungeon worlds...");
        for (String dungeonName : new HashMap<String, String>(this.dungeonToWorld).keySet()) {
            this.deleteDungeonWorld(dungeonName).join();
        }
        this.dungeonWorlds.clear();
        this.dungeonToWorld.clear();
    }
}

