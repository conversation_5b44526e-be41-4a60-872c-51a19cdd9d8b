/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.World
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.instance;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.bukkit.World;
import org.bukkit.entity.Player;

public class DungeonInstanceData {
    private final String instanceId;
    private final String templateName;
    private final World instanceWorld;
    private final List<Player> team;
    private final long createdTime;
    private long startTime;
    private boolean started;
    private boolean completed;

    public DungeonInstanceData(String instanceId, String templateName, World instanceWorld, List<Player> team, long createdTime) {
        this.instanceId = instanceId;
        this.templateName = templateName;
        this.instanceWorld = instanceWorld;
        this.team = new ArrayList<Player>(team);
        this.createdTime = createdTime;
        this.started = false;
        this.completed = false;
    }

    public void start() {
        if (!this.started) {
            this.started = true;
            this.startTime = System.currentTimeMillis();
        }
    }

    public void complete() {
        this.completed = true;
    }

    public void addPlayer(Player player) {
        if (!this.team.contains(player)) {
            this.team.add(player);
        }
    }

    public void removePlayer(Player player) {
        this.team.remove(player);
    }

    public boolean hasPlayer(Player player) {
        return this.team.contains(player);
    }

    public boolean hasPlayer(UUID playerId) {
        return this.team.stream().anyMatch(p -> p.getUniqueId().equals(playerId));
    }

    public long getElapsedTime() {
        if (!this.started) {
            return 0L;
        }
        return System.currentTimeMillis() - this.startTime;
    }

    public String getFormattedElapsedTime() {
        long elapsed = this.getElapsedTime();
        long minutes = elapsed / 60000L;
        long seconds = elapsed % 60000L / 1000L;
        return String.format("%d:%02d", minutes, seconds);
    }

    public boolean isEmpty() {
        return this.team.isEmpty();
    }

    public int getTeamSize() {
        return this.team.size();
    }

    public String getInstanceId() {
        return this.instanceId;
    }

    public String getTemplateName() {
        return this.templateName;
    }

    public World getInstanceWorld() {
        return this.instanceWorld;
    }

    public List<Player> getTeam() {
        return new ArrayList<Player>(this.team);
    }

    public long getCreatedTime() {
        return this.createdTime;
    }

    public long getStartTime() {
        return this.startTime;
    }

    public boolean isStarted() {
        return this.started;
    }

    public boolean isCompleted() {
        return this.completed;
    }

    public String toString() {
        return "DungeonInstanceData{instanceId='" + this.instanceId + "', templateName='" + this.templateName + "', teamSize=" + this.team.size() + ", started=" + this.started + ", completed=" + this.completed + "}";
    }
}

