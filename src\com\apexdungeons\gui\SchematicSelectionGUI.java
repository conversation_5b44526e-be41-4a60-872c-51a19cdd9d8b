/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.ClickType
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class SchematicSelectionGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.GOLD) + "Master Builder Wand " + String.valueOf(ChatColor.GRAY) + "- Select Schematic";
    private static final Set<String> favoriteSchematicsByPlayer = new HashSet<String>();
    private static int currentPage = 0;
    private static String searchFilter = "";

    public static void open(Player player, ApexDungeons plugin) {
        SchematicSelectionGUI.open(player, plugin, 0, "");
    }

    public static void open(Player player, ApexDungeons plugin, int page, String filter) {
        currentPage = page;
        searchFilter = filter;
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        SchematicSelectionGUI.fillBackground(inv);
        SchematicSelectionGUI.populateSchematics(inv, plugin, player, page, filter);
        SchematicSelectionGUI.addUtilityButtons(inv, plugin, player, page, filter);
        player.openInventory(inv);
        SchematicSelectionGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void populateSchematics(Inventory inv, ApexDungeons plugin, Player player, int page, String filter) {
        Set<String> allSchematicNames = plugin.getSchematicManager().getLoadedSchematicNames();
        ArrayList<String> filteredNames = new ArrayList<String>();
        for (String name : allSchematicNames) {
            if (!filter.isEmpty() && !name.toLowerCase().contains(filter.toLowerCase())) continue;
            filteredNames.add(name);
        }
        filteredNames.sort((a, b) -> {
            boolean aFav = SchematicSelectionGUI.isFavorite(player, a);
            boolean bFav = SchematicSelectionGUI.isFavorite(player, b);
            if (aFav && !bFav) {
                return -1;
            }
            if (!aFav && bFav) {
                return 1;
            }
            return a.compareToIgnoreCase((String)b);
        });
        int[] schematicSlots = new int[]{10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34, 37, 38, 39, 40, 41, 42, 43};
        int startIndex = page * schematicSlots.length;
        for (int i = 0; i < schematicSlots.length && startIndex + i < filteredNames.size(); ++i) {
            String schematicName = (String)filteredNames.get(startIndex + i);
            ItemStack schematicItem = SchematicSelectionGUI.createSchematicItem(schematicName, plugin, player);
            inv.setItem(schematicSlots[i], schematicItem);
        }
    }

    private static ItemStack createSchematicItem(String schematicName, ApexDungeons plugin, Player player) {
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        Material material = SchematicSelectionGUI.getSchematicMaterial(schematicName);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        boolean isFavorite = SchematicSelectionGUI.isFavorite(player, schematicName);
        String displayName = String.valueOf(isFavorite ? String.valueOf(ChatColor.GOLD) + "\u2b50 " : ChatColor.AQUA) + schematicName;
        meta.setDisplayName(displayName);
        ArrayList<Object> lore = new ArrayList<Object>();
        if (schematic != null) {
            lore.add(String.valueOf(ChatColor.GRAY) + "Dimensions: " + String.valueOf(ChatColor.WHITE) + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
            lore.add(String.valueOf(ChatColor.GRAY) + "Blocks: " + String.valueOf(ChatColor.WHITE) + schematic.getWidth() * schematic.getHeight() * schematic.getDepth());
        }
        lore.add("");
        if (isFavorite) {
            lore.add(String.valueOf(ChatColor.GOLD) + "\u2b50 Favorited");
        }
        lore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Left-click to select");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\u25b6 Right-click to " + (isFavorite ? "unfavorite" : "favorite"));
        lore.add(String.valueOf(ChatColor.BLUE) + "\u25b6 Shift+click for instant preview");
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private static Material getSchematicMaterial(String name) {
        String lowerName = name.toLowerCase();
        if (lowerName.contains("house") || lowerName.contains("home")) {
            return Material.BRICK;
        }
        if (lowerName.contains("castle") || lowerName.contains("fort")) {
            return Material.STONE_BRICKS;
        }
        if (lowerName.contains("tower")) {
            return Material.COBBLESTONE;
        }
        if (lowerName.contains("bridge")) {
            return Material.OAK_PLANKS;
        }
        if (lowerName.contains("ship") || lowerName.contains("boat")) {
            return Material.DARK_OAK_PLANKS;
        }
        if (lowerName.contains("tree")) {
            return Material.OAK_LOG;
        }
        if (lowerName.contains("farm")) {
            return Material.HAY_BLOCK;
        }
        if (lowerName.contains("mine") || lowerName.contains("cave")) {
            return Material.COAL_ORE;
        }
        if (lowerName.contains("temple") || lowerName.contains("church")) {
            return Material.SANDSTONE;
        }
        if (lowerName.contains("modern")) {
            return Material.QUARTZ_BLOCK;
        }
        return Material.STRUCTURE_BLOCK;
    }

    private static void addUtilityButtons(Inventory inv, ApexDungeons plugin, Player player, int page, String filter) {
        ItemStack refresh = new ItemStack(Material.EMERALD);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udd04 Refresh Schematics");
        ArrayList<CallSite> refreshLore = new ArrayList<CallSite>();
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Reload all schematics from folder")));
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Total: " + String.valueOf(ChatColor.WHITE) + plugin.getSchematicManager().getLoadedSchematicNames().size() + " schematics")));
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(45, refresh);
        ItemStack search = new ItemStack(Material.COMPASS);
        ItemMeta searchMeta = search.getItemMeta();
        searchMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\ud83d\udd0d Search Filter");
        ArrayList<CallSite> searchLore = new ArrayList<CallSite>();
        if (!filter.isEmpty()) {
            searchLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Current filter: " + String.valueOf(ChatColor.WHITE) + filter)));
            searchLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Click to clear filter")));
        } else {
            searchLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "No filter active")));
            searchLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Type in chat to search")));
        }
        searchMeta.setLore(searchLore);
        search.setItemMeta(searchMeta);
        inv.setItem(46, search);
        ItemStack favorites = new ItemStack(Material.NETHER_STAR);
        ItemMeta favMeta = favorites.getItemMeta();
        favMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\u2b50 Show Favorites Only");
        ArrayList<CallSite> favLore = new ArrayList<CallSite>();
        favLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "View only your favorite schematics")));
        favMeta.setLore(favLore);
        favorites.setItemMeta(favMeta);
        inv.setItem(47, favorites);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\u2753 Help & Controls");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "Master Builder Wand Guide:");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Left-click: Select schematic");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click: Toggle favorite");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Shift+click: Instant preview");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.GREEN) + "Made by Vexy");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
        Set<String> allNames = plugin.getSchematicManager().getLoadedSchematicNames();
        int totalPages = (int)Math.ceil((double)allNames.size() / 28.0);
        if (page > 0) {
            ItemStack prev = new ItemStack(Material.ARROW);
            ItemMeta prevMeta = prev.getItemMeta();
            prevMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Previous Page");
            prev.setItemMeta(prevMeta);
            inv.setItem(48, prev);
        }
        if (page < totalPages - 1) {
            ItemStack next = new ItemStack(Material.ARROW);
            ItemMeta nextMeta = next.getItemMeta();
            nextMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "Next Page \u2192");
            next.setItemMeta(nextMeta);
            inv.setItem(50, next);
        }
        ItemStack pageInfo = new ItemStack(Material.PAPER);
        ItemMeta pageMeta = pageInfo.getItemMeta();
        pageMeta.setDisplayName(String.valueOf(ChatColor.WHITE) + "Page " + (page + 1) + " of " + Math.max(1, totalPages));
        pageInfo.setItemMeta(pageMeta);
        inv.setItem(49, pageInfo);
    }

    private static boolean isFavorite(Player player, String schematicName) {
        return favoriteSchematicsByPlayer.contains(String.valueOf(player.getUniqueId()) + ":" + schematicName);
    }

    private static void toggleFavorite(Player player, String schematicName) {
        String key = String.valueOf(player.getUniqueId()) + ":" + schematicName;
        if (favoriteSchematicsByPlayer.contains(key)) {
            favoriteSchematicsByPlayer.remove(key);
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Removed " + String.valueOf(ChatColor.AQUA) + schematicName + String.valueOf(ChatColor.YELLOW) + " from favorites");
        } else {
            favoriteSchematicsByPlayer.add(key);
            player.sendMessage(String.valueOf(ChatColor.GOLD) + "Added " + String.valueOf(ChatColor.AQUA) + schematicName + String.valueOf(ChatColor.GOLD) + " to favorites \u2b50");
        }
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (!e.getView().getTitle().equals(GUI_NAME) || !(e.getWhoClicked() instanceof Player)) {
                    return;
                }
                e.setCancelled(true);
                Player player = (Player)e.getWhoClicked();
                int slot = e.getRawSlot();
                ItemStack clicked = e.getCurrentItem();
                if (clicked == null || !clicked.hasItemMeta()) {
                    return;
                }
                if (slot >= 10 && slot <= 43 && !SchematicSelectionGUI.isBorderSlot(slot)) {
                    String schematicName = SchematicSelectionGUI.extractSchematicName(clicked.getItemMeta().getDisplayName());
                    if (e.getClick() == ClickType.RIGHT) {
                        SchematicSelectionGUI.toggleFavorite(player, schematicName);
                        SchematicSelectionGUI.open(player, plugin, currentPage, searchFilter);
                    } else if (e.getClick() == ClickType.SHIFT_LEFT) {
                        player.closeInventory();
                        plugin.getMasterBuilderWand().setSelectedSchematic(player, schematicName);
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Selected and ready for preview: " + String.valueOf(ChatColor.AQUA) + schematicName);
                    } else {
                        player.closeInventory();
                        plugin.getMasterBuilderWand().setSelectedSchematic(player, schematicName);
                    }
                }
                switch (slot) {
                    case 45: {
                        plugin.getSchematicManager().loadSchematics();
                        SchematicSelectionGUI.open(player, plugin, currentPage, searchFilter);
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Schematics refreshed!");
                        break;
                    }
                    case 46: {
                        player.closeInventory();
                        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Type a search term in chat (or 'cancel' to abort):");
                        break;
                    }
                    case 47: {
                        break;
                    }
                    case 48: {
                        if (currentPage <= 0) break;
                        SchematicSelectionGUI.open(player, plugin, currentPage - 1, searchFilter);
                        break;
                    }
                    case 50: {
                        SchematicSelectionGUI.open(player, plugin, currentPage + 1, searchFilter);
                    }
                }
            }
        }, (Plugin)plugin);
    }

    private static boolean isBorderSlot(int slot) {
        int[] borderSlots;
        for (int border : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            if (slot != border) continue;
            return true;
        }
        return false;
    }

    private static String extractSchematicName(String displayName) {
        return ChatColor.stripColor((String)displayName).replace("\u2b50 ", "");
    }
}

