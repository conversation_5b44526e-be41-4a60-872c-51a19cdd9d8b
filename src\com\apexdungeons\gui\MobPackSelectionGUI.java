/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.BuildingToolsGUI;
import com.apexdungeons.mobs.DungeonMobConfig;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class MobPackSelectionGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_RED) + "Dungeon Mob Selection " + String.valueOf(ChatColor.GRAY) + "- Made by Vexy";
    private static String currentCategory = "all";
    private static int currentPage = 0;

    public static void open(Player player, ApexDungeons plugin) {
        MobPackSelectionGUI.open(player, plugin, "all", 0);
    }

    public static void open(Player player, ApexDungeons plugin, String category, int page) {
        currentCategory = category;
        currentPage = page;
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        MobPackSelectionGUI.fillBackground(inv);
        MobPackSelectionGUI.createCategoryButtons(inv, plugin, category);
        MobPackSelectionGUI.populateMobs(inv, plugin, player, category, page);
        MobPackSelectionGUI.addUtilityButtons(inv, plugin, player, category, page);
        player.openInventory(inv);
        MobPackSelectionGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.RED_STAINED_GLASS_PANE);
        ItemMeta meta = background.getItemMeta();
        meta.setDisplayName(" ");
        background.setItemMeta(meta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createCategoryButtons(Inventory inv, ApexDungeons plugin, String selectedCategory) {
        ItemStack allButton = new ItemStack(selectedCategory.equals("all") ? Material.LIME_STAINED_GLASS_PANE : Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta allMeta = allButton.getItemMeta();
        allMeta.setDisplayName(String.valueOf(ChatColor.WHITE) + "All Mobs");
        ArrayList<CallSite> allLore = new ArrayList<CallSite>();
        allLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Show all available mobs")));
        allLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Total: " + String.valueOf(ChatColor.WHITE) + plugin.getDungeonMobManager().getLoadedMobs().size() + " mobs")));
        allMeta.setLore(allLore);
        allButton.setItemMeta(allMeta);
        inv.setItem(1, allButton);
        List<String> categories = plugin.getDungeonMobManager().getCategories();
        int[] categorySlots = new int[]{2, 3, 4, 5, 6, 7};
        for (int i = 0; i < Math.min(categories.size(), categorySlots.length); ++i) {
            String category = categories.get(i);
            boolean isSelected = category.equals(selectedCategory);
            ItemStack categoryButton = new ItemStack(isSelected ? Material.LIME_STAINED_GLASS_PANE : Material.GRAY_STAINED_GLASS_PANE);
            ItemMeta categoryMeta = categoryButton.getItemMeta();
            categoryMeta.setDisplayName(String.valueOf(ChatColor.WHITE) + MobPackSelectionGUI.formatCategoryName(category));
            ArrayList<CallSite> categoryLore = new ArrayList<CallSite>();
            categoryLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Filter by " + category + " mobs")));
            int mobCount = plugin.getDungeonMobManager().getMobsInCategory(category).size();
            categoryLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Count: " + String.valueOf(ChatColor.WHITE) + mobCount + " mobs")));
            categoryMeta.setLore(categoryLore);
            categoryButton.setItemMeta(categoryMeta);
            inv.setItem(categorySlots[i], categoryButton);
        }
    }

    private static void populateMobs(Inventory inv, ApexDungeons plugin, Player player, String category, int page) {
        List<DungeonMobConfig> mobs = category.equals("all") ? new ArrayList<DungeonMobConfig>(plugin.getDungeonMobManager().getLoadedMobs().values()) : plugin.getDungeonMobManager().getMobsInCategory(category);
        mobs.sort((a, b) -> {
            if (a.isBoss() && !b.isBoss()) {
                return -1;
            }
            if (!a.isBoss() && b.isBoss()) {
                return 1;
            }
            int diffCompare = MobPackSelectionGUI.getDifficultyOrder(a.getDifficulty()) - MobPackSelectionGUI.getDifficultyOrder(b.getDifficulty());
            if (diffCompare != 0) {
                return diffCompare;
            }
            return a.getDisplayName().compareToIgnoreCase(b.getDisplayName());
        });
        int[] mobSlots = new int[]{9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44};
        int startIndex = page * mobSlots.length;
        for (int i = 0; i < mobSlots.length && startIndex + i < mobs.size(); ++i) {
            DungeonMobConfig mob = mobs.get(startIndex + i);
            ItemStack mobItem = MobPackSelectionGUI.createMobItem(mob);
            inv.setItem(mobSlots[i], mobItem);
        }
    }

    private static ItemStack createMobItem(DungeonMobConfig mob) {
        ItemStack item = new ItemStack(mob.getIconMaterial());
        ItemMeta meta = item.getItemMeta();
        String displayName = mob.getDifficultyColor() + mob.getDisplayName();
        if (mob.isBoss()) {
            displayName = String.valueOf(ChatColor.DARK_RED) + "\ud83d\udc51 " + displayName;
        }
        meta.setDisplayName(displayName);
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + mob.getDescription());
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "Category: " + mob.getCategoryColor() + MobPackSelectionGUI.formatCategoryName(mob.getCategory()));
        lore.add(String.valueOf(ChatColor.YELLOW) + "Difficulty: " + mob.getDifficultyColor() + MobPackSelectionGUI.formatDifficultyName(mob.getDifficulty()));
        lore.add(String.valueOf(ChatColor.YELLOW) + "Mob Type: " + String.valueOf(ChatColor.WHITE) + mob.getMobType());
        lore.add("");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2699 Spawn Settings:");
        lore.add(String.valueOf(ChatColor.GRAY) + "\u2022 Radius: " + String.valueOf(ChatColor.WHITE) + mob.getSpawnRadius() + " blocks");
        lore.add(String.valueOf(ChatColor.GRAY) + "\u2022 Cooldown: " + String.valueOf(ChatColor.WHITE) + mob.getCooldownMin() + "-" + mob.getCooldownMax() + "s");
        lore.add(String.valueOf(ChatColor.GRAY) + "\u2022 Max Concurrent: " + String.valueOf(ChatColor.WHITE) + mob.getMaxConcurrent());
        if (!mob.getBuilderInfo().isEmpty()) {
            lore.add("");
            lore.add(String.valueOf(ChatColor.GOLD) + "\ud83d\udccb Builder Notes:");
            for (String info : mob.getBuilderInfo()) {
                lore.add(String.valueOf(ChatColor.GRAY) + "\u2022 " + String.valueOf(ChatColor.WHITE) + info);
            }
        }
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to select this mob!");
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private static void addUtilityButtons(Inventory inv, ApexDungeons plugin, Player player, String category, int page) {
        ItemStack refresh = new ItemStack(Material.EMERALD);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udd04 Refresh Mob Configs");
        ArrayList<CallSite> refreshLore = new ArrayList<CallSite>();
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Reload all mob configurations")));
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Total: " + String.valueOf(ChatColor.WHITE) + plugin.getDungeonMobManager().getLoadedMobs().size() + " mobs")));
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(45, refresh);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\u2753 Mob Spawner Help");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "How to use:");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Select a mob from this GUI");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Click where you want to place spawn point");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Mob will spawn automatically for players");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "Builder Mode:");
        helpLore.add(String.valueOf(ChatColor.GRAY) + "\u2022 You see spawn point indicators");
        helpLore.add(String.valueOf(ChatColor.GRAY) + "\u2022 Players in gameplay mode don't see them");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.GREEN) + "Made by Vexy");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Back to Building Tools");
        back.setItemMeta(backMeta);
        inv.setItem(46, back);
        List<DungeonMobConfig> allMobs = category.equals("all") ? new ArrayList<DungeonMobConfig>(plugin.getDungeonMobManager().getLoadedMobs().values()) : plugin.getDungeonMobManager().getMobsInCategory(category);
        int totalPages = (int)Math.ceil((double)allMobs.size() / 36.0);
        if (page > 0) {
            ItemStack prev = new ItemStack(Material.ARROW);
            ItemMeta prevMeta = prev.getItemMeta();
            prevMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Previous Page");
            prev.setItemMeta(prevMeta);
            inv.setItem(48, prev);
        }
        if (page < totalPages - 1) {
            ItemStack next = new ItemStack(Material.ARROW);
            ItemMeta nextMeta = next.getItemMeta();
            nextMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "Next Page \u2192");
            next.setItemMeta(nextMeta);
            inv.setItem(50, next);
        }
        if (totalPages > 1) {
            ItemStack pageInfo = new ItemStack(Material.PAPER);
            ItemMeta pageMeta = pageInfo.getItemMeta();
            pageMeta.setDisplayName(String.valueOf(ChatColor.WHITE) + "Page " + (page + 1) + " of " + totalPages);
            pageInfo.setItemMeta(pageMeta);
            inv.setItem(49, pageInfo);
        }
    }

    private static String formatCategoryName(String category) {
        return category.substring(0, 1).toUpperCase() + category.substring(1).toLowerCase();
    }

    private static String formatDifficultyName(String difficulty) {
        return difficulty.substring(0, 1).toUpperCase() + difficulty.substring(1).toLowerCase();
    }

    private static int getDifficultyOrder(String difficulty) {
        switch (difficulty.toLowerCase()) {
            case "easy": {
                return 1;
            }
            case "normal": {
                return 2;
            }
            case "hard": {
                return 3;
            }
            case "elite": {
                return 4;
            }
            case "boss": {
                return 5;
            }
        }
        return 0;
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (!e.getView().getTitle().equals(GUI_NAME) || !(e.getWhoClicked() instanceof Player)) {
                    return;
                }
                e.setCancelled(true);
                Player player = (Player)e.getWhoClicked();
                int slot = e.getRawSlot();
                ItemStack clicked = e.getCurrentItem();
                if (clicked == null || !clicked.hasItemMeta()) {
                    return;
                }
                if (slot >= 1 && slot <= 7) {
                    String newCategory = "all";
                    if (slot == 1) {
                        newCategory = "all";
                    } else {
                        int categoryIndex = slot - 2;
                        List<String> categories = plugin.getDungeonMobManager().getCategories();
                        if (categoryIndex < categories.size()) {
                            newCategory = categories.get(categoryIndex);
                        }
                    }
                    MobPackSelectionGUI.open(player, plugin, newCategory, 0);
                    return;
                }
                if (slot >= 9 && slot <= 44) {
                    String mobName = MobPackSelectionGUI.extractMobName(clicked.getItemMeta().getDisplayName());
                    DungeonMobConfig selectedMob = MobPackSelectionGUI.findMobByDisplayName(plugin, mobName);
                    if (selectedMob != null) {
                        player.closeInventory();
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Selected mob: " + String.valueOf(ChatColor.AQUA) + selectedMob.getDisplayName());
                        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Right-click where you want to place the spawn point!");
                    }
                    return;
                }
                switch (slot) {
                    case 45: {
                        plugin.getDungeonMobManager().reloadMobConfigs();
                        MobPackSelectionGUI.open(player, plugin, currentCategory, currentPage);
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Mob configurations refreshed!");
                        break;
                    }
                    case 46: {
                        player.closeInventory();
                        BuildingToolsGUI.open(player, plugin);
                        break;
                    }
                    case 48: {
                        if (currentPage <= 0) break;
                        MobPackSelectionGUI.open(player, plugin, currentCategory, currentPage - 1);
                        break;
                    }
                    case 50: {
                        MobPackSelectionGUI.open(player, plugin, currentCategory, currentPage + 1);
                    }
                }
            }
        }, (Plugin)plugin);
    }

    private static String extractMobName(String displayName) {
        return ChatColor.stripColor((String)displayName).replace("\ud83d\udc51 ", "");
    }

    private static DungeonMobConfig findMobByDisplayName(ApexDungeons plugin, String displayName) {
        return plugin.getDungeonMobManager().getLoadedMobs().values().stream().filter(mob -> mob.getDisplayName().equals(displayName)).findFirst().orElse(null);
    }
}

