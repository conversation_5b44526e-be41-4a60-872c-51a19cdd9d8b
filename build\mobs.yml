# ------------------------------------------------------------------------------
# Mob spawn pools for ApexDungeons - MythicMobs Integration
# This file defines which MythicMobs can spawn in dungeons. Uses MythicMobs
# mob names directly. Pools are lists of mob identifiers with spawn weights.
# ------------------------------------------------------------------------------

# MythicMobs integration settings
mythic_integration:
  enabled: true
  use_mythic_commands: true
  sync_with_server: true

pools:
  # Default pool - uses MythicMobs if available, fallback to vanilla
  default:
    - type: MM:SkeletonKnight
      weight: 30
      level: 1-3
    - type: MM:ZombieBrute
      weight: 25
      level: 1-2
    - type: VANILLA:SKELETON
      weight: 20
    - type: VANILLA:ZOMBIE
      weight: 15
    - type: MM:SpiderQueen
      weight: 10
      level: 2-4

  # Boss room pool
  boss_room:
    - type: MM:DungeonBoss
      weight: 50
      level: 5-8
    - type: MM:EliteKnight
      weight: 30
      level: 4-6
    - type: VANILLA:WITHER
      weight: 20

  # Mini boss pool
  mini_boss:
    - type: MM:MiniBoss
      weight: 40
      level: 3-5
    - type: MM:ChampionOrc
      weight: 35
      level: 2-4
    - type: VANILLA:IRON_GOLEM
      weight: 25