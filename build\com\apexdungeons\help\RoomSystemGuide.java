/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.help;

import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

public class RoomSystemGuide {
    public static void showMainGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "    \ud83c\udff0 APEX DUNGEONS ROOM SYSTEM \ud83c\udff0");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udccb QUICK START GUIDE:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Create a dungeon world: " + String.valueOf(ChatColor.YELLOW) + "/dgn create <name>");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Get building tools: " + String.valueOf(ChatColor.YELLOW) + "/dgn tools");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Build your start room with entrance");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Connect rooms using the Room Connector");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "5. " + String.valueOf(ChatColor.WHITE) + "Place End Block in final room");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udcd6 DETAILED GUIDES:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.YELLOW) + "/dgn help rooms" + String.valueOf(ChatColor.WHITE) + " - Room types and design");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.YELLOW) + "/dgn help connections" + String.valueOf(ChatColor.WHITE) + " - Connecting rooms");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.YELLOW) + "/dgn help blocks" + String.valueOf(ChatColor.WHITE) + " - Start/End blocks");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.YELLOW) + "/dgn help schematics" + String.valueOf(ChatColor.WHITE) + " - Using schematics");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Tip: Use " + String.valueOf(ChatColor.YELLOW) + "/dgn tools" + String.valueOf(ChatColor.GRAY) + " to access all building tools!");
    }

    public static void showRoomGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "        \ud83c\udfe0 ROOM TYPES & DESIGN \ud83c\udfe0");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udeaa START ROOM:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 The first room players enter");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Must contain a " + String.valueOf(ChatColor.GREEN) + "Start Block" + String.valueOf(ChatColor.WHITE) + " (Emerald Block)");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Should be welcoming and clearly show the path forward");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Recommended size: 7x7 or larger");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfc6 END ROOM:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 The final room where players complete the dungeon");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Must contain an " + String.valueOf(ChatColor.AQUA) + "End Block" + String.valueOf(ChatColor.WHITE) + " (Diamond Block)");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Often contains the main challenge or boss area");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Should feel rewarding and climactic");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd17 CONNECTING ROOMS:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Intermediate rooms between start and end");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Can contain puzzles, challenges, or story elements");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Use " + String.valueOf(ChatColor.YELLOW) + "Room Connector" + String.valueOf(ChatColor.WHITE) + " tool to link them");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Minimum 5x5 size recommended");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 Design Tips:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Leave 3-block high doorways for connections");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Use consistent themes and materials");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Add lighting to guide players");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Test your dungeon by playing through it!");
    }

    public static void showConnectionGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "       \ud83d\udd17 ROOM CONNECTION SYSTEM \ud83d\udd17");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd27 USING THE ROOM CONNECTOR:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Get the tool: " + String.valueOf(ChatColor.YELLOW) + "/dgn tools " + String.valueOf(ChatColor.WHITE) + "\u2192 Room Connector");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Left-click on the first room's wall");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Right-click on the second room's wall");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Type in chat: " + String.valueOf(ChatColor.YELLOW) + "1" + String.valueOf(ChatColor.WHITE) + ", " + String.valueOf(ChatColor.YELLOW) + "2" + String.valueOf(ChatColor.WHITE) + ", or " + String.valueOf(ChatColor.YELLOW) + "3" + String.valueOf(ChatColor.WHITE) + " for connection type");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udeaa CONNECTION TYPES:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Type 1 - Simple Doorway:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Creates a 3x3 opening between rooms");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Best for adjacent rooms");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Clean and simple connection");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Type 2 - Decorated Doorway:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 3x3 opening with stone brick frame");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Adds architectural detail");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Good for important connections");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Type 3 - Corridor Passage:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Creates a hallway between distant rooms");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Automatically builds the connecting path");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Use for rooms that are far apart");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 Connection Tips:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Plan your room layout before connecting");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Leave space for doorways when building");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Test connections by walking through them");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Use Type 3 for creative corridor designs");
    }

    public static void showBlocksGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "      \ud83c\udfaf START & END BLOCKS GUIDE \ud83c\udfaf");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udc8e START BLOCK (Emerald Block):");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Marks the beginning of dungeon challenges");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Players right-click to start their timer");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Place in your entrance/start room");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Only one per dungeon recommended");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "How to use:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "1. Get from " + String.valueOf(ChatColor.YELLOW) + "/dgn tools" + String.valueOf(ChatColor.GRAY) + " \u2192 Dungeon Blocks");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "2. Place in a prominent, accessible location");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "3. Players right-click to begin challenge");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udc8e END BLOCK (Diamond Block):");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Marks the completion point of the dungeon");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Players right-click to finish and get rewards");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Place in your final/boss room");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Only one per dungeon recommended");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "How to use:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "1. Get from " + String.valueOf(ChatColor.YELLOW) + "/dgn tools" + String.valueOf(ChatColor.GRAY) + " \u2192 Dungeon Blocks");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "2. Place in the final room after all challenges");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "3. Players right-click to complete and get rewards");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u26a0 IMPORTANT NOTES:");
        player.sendMessage(String.valueOf(ChatColor.RED) + "\u2022 Players must activate Start Block before End Block");
        player.sendMessage(String.valueOf(ChatColor.RED) + "\u2022 Breaking these blocks cancels active challenges");
        player.sendMessage(String.valueOf(ChatColor.RED) + "\u2022 Blocks don't drop items when broken (by design)");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2022 Rewards: 2 Diamonds, 5 Emeralds, 1 Golden Apple, 100 XP");
    }

    public static void showSchematicsGuide(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "        \ud83d\udcd0 SCHEMATICS SYSTEM \ud83d\udcd0");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udcc1 ADDING SCHEMATIC FILES:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Place " + String.valueOf(ChatColor.YELLOW) + ".schem" + String.valueOf(ChatColor.WHITE) + ", " + String.valueOf(ChatColor.YELLOW) + ".schematic" + String.valueOf(ChatColor.WHITE) + ", or " + String.valueOf(ChatColor.YELLOW) + ".nbt" + String.valueOf(ChatColor.WHITE) + " files in:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "  " + String.valueOf(ChatColor.AQUA) + "plugins/ApexDungeons/schematics/");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Use " + String.valueOf(ChatColor.YELLOW) + "/dgn reloadschematics" + String.valueOf(ChatColor.WHITE) + " to load new files");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Files appear as tools in " + String.valueOf(ChatColor.YELLOW) + "/dgn tools");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfae USING SCHEMATIC TOOLS:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Preview Mode:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Right-click with tool to show 3D outline");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Use " + String.valueOf(ChatColor.YELLOW) + "W/A/S/D" + String.valueOf(ChatColor.WHITE) + " keys to move preview");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Press " + String.valueOf(ChatColor.YELLOW) + "R" + String.valueOf(ChatColor.WHITE) + " to rotate 90 degrees");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Press " + String.valueOf(ChatColor.YELLOW) + "Enter" + String.valueOf(ChatColor.WHITE) + " to confirm placement");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Shift+Right-click to cancel preview");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Direct Placement:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Left-click to place immediately (no preview)");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Faster for experienced builders");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfd7\ufe0f BUILDING WITH SCHEMATICS:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Use schematics for room templates");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Combine multiple schematics for complex designs");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Modify placed structures as needed");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Add Start/End blocks after placing structures");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 Pro Tips:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Preview before placing to avoid mistakes");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Rotate schematics to fit your layout");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Use smaller schematics for room details");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Create your own schematics with WorldEdit!");
    }
}

