/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.ItemStack
 */
package com.apexdungeons.listeners;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.chests.ChestSpawnPoint;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

public class ChestSpawnToolListener
implements Listener {
    private final ApexDungeons plugin;

    public ChestSpawnToolListener(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        if (item == null || event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }
        if (!this.plugin.getChestSpawnTool().isChestSpawnTool(item)) {
            return;
        }
        event.setCancelled(true);
        Location clickedLocation = event.getClickedBlock().getLocation().add(0.0, 1.0, 0.0);
        ChestSpawnPoint existing = this.plugin.getChestSpawnManager().getChestSpawnPoint(clickedLocation);
        if (existing != null) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u26a0 There's already a chest spawn point here!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Loot Table: " + existing.getLootTable() + ", Radius: " + existing.getRadius());
            this.showChestSpawnParticles(clickedLocation, false);
            return;
        }
        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.chestspawn")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to place chest spawn points!");
            return;
        }
        this.handleChestSpawnTool(player, clickedLocation);
    }

    private void handleChestSpawnTool(Player player, Location location) {
        String lootTable = this.plugin.getChestSpawnData().getPlayerLootTableSelection(player.getUniqueId());
        if (lootTable == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u274c No loot table configured!");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.AQUA) + "/dgn chestspawn set <loot_table>" + String.valueOf(ChatColor.YELLOW) + " first");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Available tables: " + String.join((CharSequence)", ", this.plugin.getChestLootManager().getLootTableNames()));
            return;
        }
        double radius = this.plugin.getChestSpawnData().getPlayerRadius(player.getUniqueId(), 3.0);
        this.plugin.getChestSpawnManager().addChestSpawnPoint(location, lootTable, radius);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Chest spawn point created!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Loot Table: " + String.valueOf(ChatColor.WHITE) + lootTable);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Radius: " + String.valueOf(ChatColor.WHITE) + radius + " blocks");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Location: " + String.valueOf(ChatColor.WHITE) + location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        this.showChestSpawnParticles(location, true);
        player.playSound(location, Sound.BLOCK_CHEST_OPEN, 1.0f, 1.0f);
    }

    private void showChestSpawnParticles(Location location, boolean isNew) {
        if (isNew) {
            location.getWorld().spawnParticle(Particle.CRIT, location.add(0.5, 0.5, 0.5), 20, 0.5, 0.5, 0.5, 0.0);
            location.getWorld().spawnParticle(Particle.HAPPY_VILLAGER, location, 15, 0.3, 0.3, 0.3, 0.0);
        } else {
            location.getWorld().spawnParticle(Particle.ENCHANT, location.add(0.5, 0.5, 0.5), 15, 0.5, 0.5, 0.5, 1.0);
        }
    }
}

