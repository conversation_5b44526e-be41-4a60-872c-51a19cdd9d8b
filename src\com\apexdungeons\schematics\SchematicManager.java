/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.scheduler.BukkitRunnable
 */
package com.apexdungeons.schematics;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.NBTSchematicReader;
import com.apexdungeons.schematics.SchematicData;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

public class SchematicManager {
    private final ApexDungeons plugin;
    private final File schematicsFolder;
    private final Map<String, SchematicData> loadedSchematics = new HashMap<String, SchematicData>();
    private final Random random = new Random();

    public SchematicManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.schematicsFolder = new File(plugin.getDataFolder(), "schematics");
        if (!this.schematicsFolder.exists()) {
            this.schematicsFolder.mkdirs();
            plugin.getLogger().info("Created schematics folder: " + this.schematicsFolder.getPath());
        }
        this.loadAllSchematics();
    }

    public void loadAllSchematics() {
        this.loadedSchematics.clear();
        this.plugin.getLogger().info("Loading schematics from: " + this.schematicsFolder.getAbsolutePath());
        File[] files = this.schematicsFolder.listFiles((dir, name) -> {
            String lowerName = name.toLowerCase();
            return lowerName.endsWith(".schem") || lowerName.endsWith(".schematic") || lowerName.endsWith(".nbt");
        });
        if (files == null || files.length == 0) {
            this.plugin.getLogger().info("No schematic files found in " + this.schematicsFolder.getPath());
            this.plugin.getLogger().info("Supported formats: .schem, .schematic, .nbt");
            this.plugin.getLogger().info("Creating example schematics. Place your schematic files in the schematics folder to use them.");
            this.createExampleSchematics();
            return;
        }
        this.plugin.getLogger().info("Found " + files.length + " schematic files, loading...");
        int loaded = 0;
        int failed = 0;
        Arrays.sort(files, (a, b) -> a.getName().compareToIgnoreCase(b.getName()));
        for (File file : files) {
            String name2;
            try {
                this.plugin.getLogger().info("Loading: " + file.getName() + " (" + this.formatFileSize(file.length()) + ")");
                SchematicData schematic = this.loadSchematicFile(file);
                if (schematic != null) {
                    name2 = file.getName().replaceFirst("[.][^.]+$", "");
                    this.loadedSchematics.put(name2, schematic);
                    ++loaded;
                    this.plugin.getLogger().info("\u2713 Loaded: " + name2 + " [" + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth() + "]");
                    continue;
                }
                ++failed;
                this.plugin.getLogger().warning("\u2717 Failed to load: " + file.getName() + " (returned null - unsupported format?)");
            }
            catch (Exception e) {
                this.plugin.getLogger().warning("\u2717 Failed to load: " + file.getName() + " - " + e.getMessage());
                this.plugin.getLogger().info("  \u2192 Creating fallback schematic for " + file.getName());
                try {
                    name2 = file.getName().replaceFirst("[.][^.]+$", "");
                    SchematicData fallback = this.createFallbackSchematic(file);
                    if (fallback != null) {
                        this.loadedSchematics.put(name2, fallback);
                        ++loaded;
                        this.plugin.getLogger().info("\u2713 Created fallback for: " + name2);
                        continue;
                    }
                    ++failed;
                }
                catch (Exception e2) {
                    ++failed;
                    this.plugin.getLogger().warning("Failed to create fallback for " + file.getName() + ": " + e2.getMessage());
                }
            }
        }
        this.plugin.getLogger().info("Schematic loading complete: " + loaded + " loaded, " + failed + " failed");
        if (loaded > 0) {
            this.plugin.getLogger().info("Available schematics: " + String.join((CharSequence)", ", this.loadedSchematics.keySet()));
            this.plugin.getLogger().info("\ud83c\udf89 All " + loaded + " schematics are now available in the Master Builder Wand!");
        }
        if (loaded == 0) {
            this.plugin.getLogger().info("No schematics loaded successfully, creating example schematics as fallback...");
            this.createExampleSchematics();
        }
    }

    public void loadSchematics() {
        this.loadAllSchematics();
    }

    private String formatFileSize(long bytes) {
        if (bytes < 1024L) {
            return bytes + " B";
        }
        if (bytes < 0x100000L) {
            return String.format("%.1f KB", (double)bytes / 1024.0);
        }
        return String.format("%.1f MB", (double)bytes / 1048576.0);
    }

    private SchematicData loadSchematicFile(File file) throws IOException {
        this.plugin.getLogger().info("Loading schematic file: " + file.getName());
        if (file.length() < 100L) {
            throw new IOException("File too small to be a valid schematic (" + file.length() + " bytes)");
        }
        try {
            SchematicData schematic = NBTSchematicReader.readSchematic(file);
            if (schematic != null) {
                this.plugin.getLogger().info("  \u2192 Successfully parsed " + file.getName() + " (Size: " + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth() + ")");
                return schematic;
            }
            throw new IOException("NBT reader returned null");
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("  \u2192 Failed to parse " + file.getName() + ": " + e.getMessage());
            throw new IOException("Failed to parse schematic file: " + e.getMessage(), e);
        }
    }

    private SchematicData createFallbackSchematic(File file) {
        String name = file.getName().toLowerCase();
        String baseName = file.getName().replaceFirst("[.][^.]+$", "");
        this.plugin.getLogger().info("Creating fallback schematic for: " + file.getName());
        if (name.contains("treasure") || name.contains("loot")) {
            this.plugin.getLogger().info("  \u2192 Fallback: treasure room");
            return this.createTreasureRoom(baseName);
        }
        if (name.contains("corridor") || name.contains("hallway") || name.contains("tunnel")) {
            this.plugin.getLogger().info("  \u2192 Fallback: corridor");
            return this.createSimpleCorridor(baseName);
        }
        if (name.contains("chamber") || name.contains("hall") || name.contains("large")) {
            this.plugin.getLogger().info("  \u2192 Fallback: large chamber");
            return this.createSimpleChamber(baseName);
        }
        if (name.contains("small")) {
            this.plugin.getLogger().info("  \u2192 Fallback: small room");
            return this.createSmallRoom(baseName);
        }
        if (name.contains("boss") || name.contains("arena")) {
            this.plugin.getLogger().info("  \u2192 Fallback: boss arena");
            return this.createBossArena(baseName);
        }
        if (name.contains("spawn") || name.contains("start")) {
            this.plugin.getLogger().info("  \u2192 Fallback: spawn room");
            return this.createSpawnRoom(baseName);
        }
        this.plugin.getLogger().info("  \u2192 Fallback: basic room");
        return this.createSimpleRoom(baseName);
    }

    private void createExampleSchematics() {
        this.plugin.getLogger().info("Creating example schematics...");
        this.loadedSchematics.put("basic_room", this.createSimpleRoom("basic_room"));
        this.loadedSchematics.put("treasure_room", this.createTreasureRoom("treasure_room"));
        this.loadedSchematics.put("corridor", this.createSimpleCorridor("corridor"));
        this.loadedSchematics.put("large_chamber", this.createSimpleChamber("large_chamber"));
        this.plugin.getLogger().info("Created " + this.loadedSchematics.size() + " example schematics");
    }

    private SchematicData createSimpleRoom(String name) {
        int width = 7;
        int height = 4;
        int depth = 7;
        Material[][][] blocks = new Material[height][depth][width];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        for (int z = 0; z < depth; ++z) {
            for (int x = 0; x < width; ++x) {
                blocks[0][z][x] = Material.STONE_BRICKS;
                blocks[height - 1][z][x] = Material.STONE_BRICKS;
                if (x != 0 && x != width - 1 && z != 0 && z != depth - 1) continue;
                for (int y = 1; y < height - 1; ++y) {
                    blocks[y][z][x] = Material.STONE_BRICKS;
                }
            }
        }
        blocks[1][0][width / 2] = Material.AIR;
        blocks[2][0][width / 2] = Material.AIR;
        return new SchematicData(name, width, height, depth, blocks);
    }

    private SchematicData createTreasureRoom(String name) {
        SchematicData room = this.createSimpleRoom(name);
        Material[][][] blocks = room.getBlocks();
        int centerX = room.getWidth() / 2;
        int centerZ = room.getDepth() / 2;
        blocks[1][centerZ][centerX] = Material.CHEST;
        blocks[1][centerZ - 1][centerX - 1] = Material.GOLD_BLOCK;
        blocks[1][centerZ - 1][centerX + 1] = Material.GOLD_BLOCK;
        blocks[1][centerZ + 1][centerX - 1] = Material.GOLD_BLOCK;
        blocks[1][centerZ + 1][centerX + 1] = Material.GOLD_BLOCK;
        return new SchematicData(name, room.getWidth(), room.getHeight(), room.getDepth(), blocks);
    }

    private SchematicData createSimpleCorridor(String name) {
        int width = 3;
        int height = 4;
        int depth = 10;
        Material[][][] blocks = new Material[height][depth][width];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        for (int z = 0; z < depth; ++z) {
            for (int x = 0; x < width; ++x) {
                blocks[0][z][x] = Material.STONE_BRICKS;
                blocks[height - 1][z][x] = Material.STONE_BRICKS;
                if (x != 0 && x != width - 1) continue;
                for (int y = 1; y < height - 1; ++y) {
                    blocks[y][z][x] = Material.STONE_BRICKS;
                }
            }
        }
        return new SchematicData(name, width, height, depth, blocks);
    }

    private SchematicData createSimpleChamber(String name) {
        int width = 11;
        int height = 6;
        int depth = 11;
        Material[][][] blocks = new Material[height][depth][width];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        for (int z = 0; z < depth; ++z) {
            for (int x = 0; x < width; ++x) {
                blocks[0][z][x] = Material.STONE_BRICKS;
                blocks[height - 1][z][x] = Material.STONE_BRICKS;
                if (x != 0 && x != width - 1 && z != 0 && z != depth - 1) continue;
                for (int y = 1; y < height - 1; ++y) {
                    blocks[y][z][x] = Material.STONE_BRICKS;
                }
            }
        }
        int pillarX1 = width / 3;
        int pillarX2 = 2 * width / 3;
        int pillarZ1 = depth / 3;
        int pillarZ2 = 2 * depth / 3;
        for (int y = 1; y < height - 1; ++y) {
            blocks[y][pillarZ1][pillarX1] = Material.STONE_BRICK_STAIRS;
            blocks[y][pillarZ1][pillarX2] = Material.STONE_BRICK_STAIRS;
            blocks[y][pillarZ2][pillarX1] = Material.STONE_BRICK_STAIRS;
            blocks[y][pillarZ2][pillarX2] = Material.STONE_BRICK_STAIRS;
        }
        return new SchematicData(name, width, height, depth, blocks);
    }

    private SchematicData createSmallRoom(String name) {
        int width = 5;
        int height = 3;
        int depth = 5;
        Material[][][] blocks = new Material[height][depth][width];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        for (int z = 0; z < depth; ++z) {
            for (int x = 0; x < width; ++x) {
                blocks[0][z][x] = Material.COBBLESTONE;
                blocks[height - 1][z][x] = Material.COBBLESTONE;
                if (x != 0 && x != width - 1 && z != 0 && z != depth - 1) continue;
                for (int y = 1; y < height - 1; ++y) {
                    blocks[y][z][x] = Material.COBBLESTONE;
                }
            }
        }
        blocks[1][0][width / 2] = Material.AIR;
        return new SchematicData(name, width, height, depth, blocks);
    }

    private SchematicData createBossArena(String name) {
        int x;
        int width = 15;
        int height = 8;
        int depth = 15;
        Material[][][] blocks = new Material[height][depth][width];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (x = 0; x < width; ++x) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        for (int z = 0; z < depth; ++z) {
            for (int x2 = 0; x2 < width; ++x2) {
                blocks[0][z][x2] = Material.BLACKSTONE;
                blocks[height - 1][z][x2] = Material.BLACKSTONE;
                if (x2 != 0 && x2 != width - 1 && z != 0 && z != depth - 1) continue;
                for (int y = 1; y < height - 1; ++y) {
                    blocks[y][z][x2] = Material.POLISHED_BLACKSTONE_BRICKS;
                }
            }
        }
        int centerX = width / 2;
        int centerZ = depth / 2;
        for (x = centerX - 2; x <= centerX + 2; ++x) {
            for (int z = centerZ - 2; z <= centerZ + 2; ++z) {
                blocks[1][z][x] = Material.OBSIDIAN;
            }
        }
        blocks[1][0][centerX] = Material.AIR;
        blocks[2][0][centerX] = Material.AIR;
        blocks[3][0][centerX] = Material.AIR;
        return new SchematicData(name, width, height, depth, blocks);
    }

    private SchematicData createSpawnRoom(String name) {
        int width = 9;
        int height = 5;
        int depth = 9;
        Material[][][] blocks = new Material[height][depth][width];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    blocks[y][z][x] = Material.AIR;
                }
            }
        }
        for (int z = 0; z < depth; ++z) {
            for (int x = 0; x < width; ++x) {
                blocks[0][z][x] = Material.QUARTZ_BLOCK;
                blocks[height - 1][z][x] = Material.QUARTZ_BLOCK;
                if (x != 0 && x != width - 1 && z != 0 && z != depth - 1) continue;
                for (int y = 1; y < height - 1; ++y) {
                    blocks[y][z][x] = Material.QUARTZ_BRICKS;
                }
            }
        }
        int centerX = width / 2;
        int centerZ = depth / 2;
        blocks[1][centerZ][centerX] = Material.BEACON;
        blocks[1][0][centerX] = Material.AIR;
        blocks[2][0][centerX] = Material.AIR;
        blocks[1][depth - 1][centerX] = Material.AIR;
        blocks[2][depth - 1][centerX] = Material.AIR;
        blocks[1][centerZ][0] = Material.AIR;
        blocks[2][centerZ][0] = Material.AIR;
        blocks[1][centerZ][width - 1] = Material.AIR;
        blocks[2][centerZ][width - 1] = Material.AIR;
        return new SchematicData(name, width, height, depth, blocks);
    }

    public CompletableFuture<Boolean> placeSchematic(String schematicName, Location location) {
        SchematicData schematic = this.loadedSchematics.get(schematicName);
        if (schematic == null) {
            this.plugin.getLogger().warning("Schematic not found: " + schematicName);
            return CompletableFuture.completedFuture(false);
        }
        return this.placeSchematicGradually(schematic, location);
    }

    public CompletableFuture<Boolean> placeRandomSchematic(Location location) {
        if (this.loadedSchematics.isEmpty()) {
            this.plugin.getLogger().warning("No schematics available for random placement");
            return CompletableFuture.completedFuture(false);
        }
        ArrayList<String> schematicNames = new ArrayList<String>(this.loadedSchematics.keySet());
        String randomName = (String)schematicNames.get(this.random.nextInt(schematicNames.size()));
        this.plugin.getLogger().info("Placing random schematic: " + randomName + " at " + location.getWorld().getName() + " " + location.getBlockX() + "," + location.getBlockY() + "," + location.getBlockZ());
        return this.placeSchematic(randomName, location);
    }

    private CompletableFuture<Boolean> placeSchematicGradually(SchematicData schematic, Location location) {
        final CompletableFuture<Boolean> future = new CompletableFuture<Boolean>();
        World world = location.getWorld();
        if (world == null) {
            future.complete(false);
            return future;
        }
        final int maxBlocksPerTick = this.plugin.getConfig().getInt("performance.blocksPerTick", 100);
        Material[][][] blocks = schematic.getBlocks();
        final ArrayList<BlockPlacement> placements = new ArrayList<BlockPlacement>();
        for (int y = 0; y < schematic.getHeight(); ++y) {
            for (int z = 0; z < schematic.getDepth(); ++z) {
                for (int x = 0; x < schematic.getWidth(); ++x) {
                    Material material = blocks[y][z][x];
                    if (material == Material.AIR) continue;
                    Location blockLoc = location.clone().add((double)x, (double)y, (double)z);
                    placements.add(new BlockPlacement(blockLoc, material));
                }
            }
        }
        new BukkitRunnable(this){
            int index = 0;

            public void run() {
                for (int placed = 0; this.index < placements.size() && placed < maxBlocksPerTick; ++placed) {
                    BlockPlacement placement = (BlockPlacement)placements.get(this.index++);
                    Block block = placement.location.getBlock();
                    block.setType(placement.material, false);
                }
                if (this.index >= placements.size()) {
                    this.cancel();
                    future.complete(true);
                }
            }
        }.runTaskTimer((Plugin)this.plugin, 1L, 1L);
        return future;
    }

    public Set<String> getLoadedSchematicNames() {
        return new HashSet<String>(this.loadedSchematics.keySet());
    }

    public SchematicData getSchematic(String name) {
        return this.loadedSchematics.get(name);
    }

    public void reloadSchematics() {
        this.plugin.getLogger().info("Reloading schematics...");
        this.loadAllSchematics();
    }

    public void shutdown() {
        this.loadedSchematics.clear();
        this.plugin.getLogger().info("SchematicManager shutdown complete.");
    }

    private static class BlockPlacement {
        final Location location;
        final Material material;

        BlockPlacement(Location location, Material material) {
            this.location = location;
            this.material = material;
        }
    }
}

