/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 *  org.bukkit.configuration.InvalidConfigurationException
 *  org.bukkit.configuration.file.YamlConfiguration
 */
package com.apexdungeons.gen;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bukkit.Material;
import org.bukkit.configuration.InvalidConfigurationException;
import org.bukkit.configuration.file.YamlConfiguration;

public class Blueprint {
    private final String name;
    private final String theme;
    private final int width;
    private final int height;
    private final int depth;
    private final List<Connector> connectors;
    private final Map<Integer, Material> palette;
    private final int[][][] layout;

    public Blueprint(String name, String theme, int width, int height, int depth, List<Connector> connectors, Map<Integer, Material> palette, int[][][] layout) {
        this.name = name;
        this.theme = theme;
        this.width = width;
        this.height = height;
        this.depth = depth;
        this.connectors = connectors;
        this.palette = palette;
        this.layout = layout;
    }

    public String getName() {
        return this.name;
    }

    public String getTheme() {
        return this.theme;
    }

    public int getWidth() {
        return this.width;
    }

    public int getHeight() {
        return this.height;
    }

    public int getDepth() {
        return this.depth;
    }

    public List<Connector> getConnectors() {
        return this.connectors;
    }

    public Map<Integer, Material> getPalette() {
        return this.palette;
    }

    public int[][][] getLayout() {
        return this.layout;
    }

    public static Blueprint load(File file) {
        int depth;
        int height;
        int width;
        if (file == null || !file.exists()) {
            return null;
        }
        YamlConfiguration conf = new YamlConfiguration();
        try {
            conf.load(file);
        }
        catch (IOException | InvalidConfigurationException e) {
            e.printStackTrace();
            return null;
        }
        String name = conf.getString("name", file.getName().replace(".yml", ""));
        String theme = conf.getString("theme", "default");
        if (conf.contains("width") && conf.contains("height") && conf.contains("depth")) {
            width = conf.getInt("width");
            height = conf.getInt("height");
            depth = conf.getInt("depth");
        } else {
            List sizeList = conf.getIntegerList("size");
            if (sizeList.size() < 3) {
                return null;
            }
            width = (Integer)sizeList.get(0);
            height = (Integer)sizeList.get(1);
            depth = (Integer)sizeList.get(2);
        }
        HashMap<Integer, Material> palette = new HashMap<Integer, Material>();
        HashMap<Character, Material> charPalette = new HashMap<Character, Material>();
        if (conf.isConfigurationSection("palette")) {
            for (Object key : conf.getConfigurationSection("palette").getKeys(false)) {
                Material mat;
                String materialStr = conf.getString("palette." + (String)key, "minecraft:air");
                if (materialStr.startsWith("minecraft:")) {
                    materialStr = materialStr.substring(10);
                }
                if ((mat = Material.matchMaterial((String)materialStr.toUpperCase())) == null) {
                    mat = Material.AIR;
                }
                try {
                    int id = Integer.parseInt((String)key);
                    palette.put(id, mat);
                }
                catch (NumberFormatException ex) {
                    if (((String)key).length() != 1) continue;
                    charPalette.put(Character.valueOf(((String)key).charAt(0)), mat);
                }
            }
        }
        int[][][] layout = new int[height][depth][width];
        if (conf.isConfigurationSection("layout")) {
            for (String yKey : conf.getConfigurationSection("layout").getKeys(false)) {
                try {
                    int y = Integer.parseInt(yKey);
                    if (y >= height) continue;
                    List rows = conf.getStringList("layout." + yKey);
                    for (z = 0; z < Math.min(depth, rows.size()); ++z) {
                        String row = (String)rows.get(z);
                        for (int x = 0; x < Math.min(width, row.length()); ++x) {
                            int id;
                            char c = row.charAt(x);
                            if (!charPalette.containsKey(Character.valueOf(c))) continue;
                            Material mat = (Material)charPalette.get(Character.valueOf(c));
                            layout[y][z][x] = id = Blueprint.getOrCreateMaterialId(palette, mat);
                        }
                    }
                }
                catch (NumberFormatException y) {
                }
            }
        } else {
            List layers = conf.getList("layout");
            if (layers != null) {
                for (int y = 0; y < Math.min(height, layers.size()); ++y) {
                    Object layerObj = layers.get(y);
                    if (!(layerObj instanceof List)) continue;
                    List rows = (List)layerObj;
                    for (z = 0; z < Math.min(depth, rows.size()); ++z) {
                        Object rowObj = rows.get(z);
                        if (!(rowObj instanceof List)) continue;
                        List runList = (List)rowObj;
                        int x = 0;
                        for (Object runObj : runList) {
                            List pair;
                            if (!(runObj instanceof List) || (pair = (List)runObj).size() < 2) continue;
                            int id = ((Number)pair.get(0)).intValue();
                            int count = ((Number)pair.get(1)).intValue();
                            for (int i = 0; i < count && x < width; ++x, ++i) {
                                layout[y][z][x] = id;
                            }
                        }
                    }
                }
            }
        }
        ArrayList<Connector> connectors = new ArrayList<Connector>();
        if (conf.isList("connectors")) {
            for (Object obj : conf.getList("connectors")) {
                if (!(obj instanceof Map)) continue;
                Map map = (Map)obj;
                String facing = (String)map.get("facing");
                List pos = (List)map.get("position");
                if (pos == null || pos.size() < 3) continue;
                int x = ((Number)pos.get(0)).intValue();
                int y = ((Number)pos.get(1)).intValue();
                int z = ((Number)pos.get(2)).intValue();
                connectors.add(new Connector(facing, x, y, z));
            }
        }
        return new Blueprint(name, theme, width, height, depth, connectors, palette, layout);
    }

    private static int getOrCreateMaterialId(Map<Integer, Material> palette, Material material) {
        for (Map.Entry<Integer, Material> entry : palette.entrySet()) {
            if (entry.getValue() != material) continue;
            return entry.getKey();
        }
        int newId = palette.size();
        palette.put(newId, material);
        return newId;
    }

    public record Connector(String facing, int x, int y, int z) {
    }
}

