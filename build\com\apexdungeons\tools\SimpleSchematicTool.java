/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class SimpleSchematicTool
implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey schematicToolKey;
    private final NamespacedKey schematicNameKey;

    public SimpleSchematicTool(ApexDungeons plugin) {
        this.plugin = plugin;
        this.schematicToolKey = new NamespacedKey((Plugin)plugin, "simple_schematic_tool");
        this.schematicNameKey = new NamespacedKey((Plugin)plugin, "schematic_name");
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public ItemStack createSchematicTool(String schematicName) {
        if (!this.plugin.getSchematicManager().getLoadedSchematicNames().contains(schematicName)) {
            return null;
        }
        ItemStack tool = new ItemStack(Material.STICK);
        ItemMeta meta = tool.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udcd0 Schematic: " + String.valueOf(ChatColor.YELLOW) + schematicName);
            ArrayList<Object> lore = new ArrayList<Object>();
            lore.add(String.valueOf(ChatColor.GRAY) + "Simple schematic placement tool");
            lore.add("");
            lore.add(String.valueOf(ChatColor.GREEN) + "Controls:");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Right-click: " + String.valueOf(ChatColor.WHITE) + "Place schematic");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Left-click: " + String.valueOf(ChatColor.WHITE) + "Show preview particles");
            lore.add("");
            lore.add(String.valueOf(ChatColor.YELLOW) + "Schematic: " + String.valueOf(ChatColor.WHITE) + schematicName);
            lore.add(String.valueOf(ChatColor.GRAY) + "Look at a block to target placement location");
            meta.setLore(lore);
            meta.getPersistentDataContainer().set(this.schematicToolKey, PersistentDataType.BYTE, (Object)1);
            meta.getPersistentDataContainer().set(this.schematicNameKey, PersistentDataType.STRING, (Object)schematicName);
            tool.setItemMeta(meta);
        }
        return tool;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        if (!this.isSimpleSchematicTool(item)) {
            return;
        }
        event.setCancelled(true);
        String schematicName = this.getSchematicName(item);
        if (schematicName == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid schematic tool!");
            return;
        }
        Block targetBlock = player.getTargetBlockExact(10);
        if (targetBlock == null || targetBlock.getType() == Material.AIR) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Look at a block to target placement location!");
            return;
        }
        Location targetLocation = targetBlock.getLocation().add(0.0, 1.0, 0.0);
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            this.placeSchematic(player, schematicName, targetLocation);
        } else if (event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK) {
            this.showPreviewParticles(player, schematicName, targetLocation);
        }
    }

    private void placeSchematic(Player player, String schematicName, Location location) {
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Placing schematic " + String.valueOf(ChatColor.AQUA) + schematicName + String.valueOf(ChatColor.YELLOW) + "...");
        this.plugin.getSchematicManager().placeSchematic(schematicName, location).thenAccept(success -> Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
            if (success.booleanValue()) {
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Schematic placed successfully!");
                player.playSound(location, Sound.BLOCK_ANVIL_USE, 1.0f, 1.2f);
                World world = location.getWorld();
                if (world != null) {
                    world.spawnParticle(Particle.HAPPY_VILLAGER, location.add(0.5, 1.0, 0.5), 10, 1.0, 1.0, 1.0, 0.0);
                }
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Failed to place schematic!");
                player.playSound(player.getLocation(), Sound.BLOCK_ANVIL_BREAK, 1.0f, 0.8f);
            }
        }));
    }

    private void showPreviewParticles(Player player, String schematicName, Location location) {
        Location[] corners;
        SchematicData schematic = this.plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Schematic not found: " + schematicName);
            return;
        }
        World world = location.getWorld();
        if (world == null) {
            return;
        }
        int width = schematic.getWidth();
        int height = schematic.getHeight();
        int depth = schematic.getDepth();
        for (Location corner : corners = new Location[]{location.clone(), location.clone().add((double)(width - 1), 0.0, 0.0), location.clone().add(0.0, 0.0, (double)(depth - 1)), location.clone().add((double)(width - 1), 0.0, (double)(depth - 1)), location.clone().add(0.0, (double)(height - 1), 0.0), location.clone().add((double)(width - 1), (double)(height - 1), 0.0), location.clone().add(0.0, (double)(height - 1), (double)(depth - 1)), location.clone().add((double)(width - 1), (double)(height - 1), (double)(depth - 1))}) {
            world.spawnParticle(Particle.END_ROD, corner.add(0.5, 0.5, 0.5), 1, 0.0, 0.0, 0.0, 0.0);
        }
        for (int x = 0; x < width; x += Math.max(1, width / 10)) {
            world.spawnParticle(Particle.ENCHANT, location.clone().add((double)x + 0.5, 0.5, 0.5), 1);
            world.spawnParticle(Particle.ENCHANT, location.clone().add((double)x + 0.5, 0.5, (double)depth - 0.5), 1);
        }
        for (int z = 0; z < depth; z += Math.max(1, depth / 10)) {
            world.spawnParticle(Particle.ENCHANT, location.clone().add(0.5, 0.5, (double)z + 0.5), 1);
            world.spawnParticle(Particle.ENCHANT, location.clone().add((double)width - 0.5, 0.5, (double)z + 0.5), 1);
        }
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Preview shown for: " + String.valueOf(ChatColor.YELLOW) + schematicName);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Size: " + width + "x" + height + "x" + depth);
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);
    }

    private boolean isSimpleSchematicTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(this.schematicToolKey, PersistentDataType.BYTE);
    }

    private String getSchematicName(ItemStack item) {
        if (!this.isSimpleSchematicTool(item)) {
            return null;
        }
        ItemMeta meta = item.getItemMeta();
        return (String)meta.getPersistentDataContainer().get(this.schematicNameKey, PersistentDataType.STRING);
    }
}

