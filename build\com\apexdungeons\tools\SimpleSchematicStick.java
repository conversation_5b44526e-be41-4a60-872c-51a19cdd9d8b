/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class SimpleSchematicStick
implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey schematicKey;

    public SimpleSchematicStick(ApexDungeons plugin) {
        this.plugin = plugin;
        this.schematicKey = new NamespacedKey((Plugin)plugin, "schematic_stick");
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public ItemStack createSchematicStick(String schematicName) {
        ItemStack stick = new ItemStack(Material.STICK);
        ItemMeta meta = stick.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udcd0 " + schematicName);
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + "Schematic placement tool");
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "Right-click: Place " + schematicName);
        lore.add(String.valueOf(ChatColor.GRAY) + "Simple and reliable placement");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "Made by Vexy");
        meta.setLore(lore);
        meta.getPersistentDataContainer().set(this.schematicKey, PersistentDataType.STRING, (Object)schematicName);
        stick.setItemMeta(meta);
        return stick;
    }

    public boolean isSchematicStick(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        return item.getItemMeta().getPersistentDataContainer().has(this.schematicKey, PersistentDataType.STRING);
    }

    public String getSchematicName(ItemStack item) {
        if (!this.isSchematicStick(item)) {
            return null;
        }
        return (String)item.getItemMeta().getPersistentDataContainer().get(this.schematicKey, PersistentDataType.STRING);
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        if (!this.isSchematicStick(item)) {
            return;
        }
        Action action = event.getAction();
        if (action == Action.RIGHT_CLICK_BLOCK || action == Action.RIGHT_CLICK_AIR) {
            event.setCancelled(true);
            Location targetLocation = null;
            if (action == Action.RIGHT_CLICK_BLOCK) {
                Block clickedBlock = event.getClickedBlock();
                if (clickedBlock != null) {
                    targetLocation = clickedBlock.getLocation().add(0.0, 1.0, 0.0);
                }
            } else {
                Block targetBlock = player.getTargetBlockExact(5);
                targetLocation = targetBlock != null ? targetBlock.getLocation().add(0.0, 1.0, 0.0) : player.getLocation().getBlock().getLocation().add(0.0, 1.0, 0.0);
            }
            String schematicName = this.getSchematicName(item);
            if (schematicName == null) {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid schematic stick!");
                return;
            }
            this.placeSchematic(player, schematicName, targetLocation);
        }
    }

    private void placeSchematic(Player player, String schematicName, Location location) {
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Placing schematic: " + String.valueOf(ChatColor.AQUA) + schematicName);
        try {
            if (this.plugin.getSchematicManager() != null && this.plugin.getSchematicManager().getSchematic(schematicName) != null) {
                this.plugin.getSchematicManager().placeSchematic(schematicName, location).thenAccept(success -> Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
                    if (success.booleanValue()) {
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Schematic placed successfully!");
                    } else if (!this.tryWorldEditOrFallback(player, schematicName, location)) {
                        this.trySimplePlacement(player, schematicName, location);
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Placed a simple room fallback for: " + schematicName);
                    }
                }));
                return;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("[SimpleSchematicStick] In-memory placement attempt failed: " + e.getMessage());
        }
        if (this.tryWorldEditOrFallback(player, schematicName, location)) {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Schematic placed successfully!");
            return;
        }
        if (this.trySimplePlacement(player, schematicName, location)) {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Placed a simple room fallback for: " + schematicName);
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Failed to place schematic. Check console for details.");
        }
    }

    private boolean tryWorldEditOrFallback(Player player, String schematicName, Location location) {
        try {
            File schematicFile = new File(this.plugin.getDataFolder(), "schematics/" + schematicName + ".schem");
            if (!schematicFile.exists()) {
                schematicFile = new File(this.plugin.getDataFolder(), "schematics/" + schematicName + ".schematic");
            }
            if (schematicFile.exists() && this.tryWorldEditPlacement(player, schematicFile, location)) {
                return true;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().info("[SimpleSchematicStick] WorldEdit/file placement failed: " + e.getMessage());
        }
        return false;
    }

    private boolean tryWorldEditPlacement(Player player, File schematicFile, Location location) {
        try {
            if (this.plugin.getServer().getPluginManager().getPlugin("WorldEdit") == null) {
                return false;
            }
            this.plugin.getLogger().info("[SimpleSchematicStick] Attempting WorldEdit placement...");
            Class<?> worldEditPlugin = Class.forName("com.sk89q.worldedit.bukkit.WorldEditPlugin");
            Plugin wePlugin = this.plugin.getServer().getPluginManager().getPlugin("WorldEdit");
            if (wePlugin != null) {
                Object session = worldEditPlugin.getMethod("getSession", Player.class).invoke((Object)wePlugin, player);
                Class<?> clipboardFormat = Class.forName("com.sk89q.worldedit.extent.clipboard.io.ClipboardFormats");
                Object format = clipboardFormat.getMethod("findByFile", File.class).invoke(null, schematicFile);
                if (format != null) {
                    Object reader = format.getClass().getMethod("getReader", InputStream.class).invoke(format, new FileInputStream(schematicFile));
                    Object clipboard = reader.getClass().getMethod("read", new Class[0]).invoke(reader, new Object[0]);
                    session.getClass().getMethod("setClipboard", Class.forName("com.sk89q.worldedit.extent.clipboard.Clipboard")).invoke(session, clipboard);
                    Class<?> bukkitAdapter = Class.forName("com.sk89q.worldedit.bukkit.BukkitAdapter");
                    Object weLocation = bukkitAdapter.getMethod("adapt", Location.class).invoke(null, location);
                    Object editSession = session.getClass().getMethod("createEditSession", Class.forName("com.sk89q.worldedit.world.World")).invoke(session, bukkitAdapter.getMethod("adapt", World.class).invoke(null, location.getWorld()));
                    Object operation = clipboard.getClass().getMethod("createPaste", Class.forName("com.sk89q.worldedit.extent.Extent")).invoke(clipboard, editSession);
                    operation.getClass().getMethod("to", Class.forName("com.sk89q.worldedit.math.BlockVector3")).invoke(operation, weLocation.getClass().getMethod("toVector", new Class[0]).invoke(weLocation, new Object[0]));
                    Class<?> operations = Class.forName("com.sk89q.worldedit.function.operation.Operations");
                    operations.getMethod("complete", Class.forName("com.sk89q.worldedit.function.operation.Operation")).invoke(null, operation);
                    this.plugin.getLogger().info("[SimpleSchematicStick] WorldEdit placement successful!");
                    return true;
                }
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().info("[SimpleSchematicStick] WorldEdit placement failed: " + e.getMessage());
        }
        return false;
    }

    private boolean trySimplePlacement(Player player, String schematicName, Location location) {
        try {
            this.plugin.getLogger().info("[SimpleSchematicStick] Attempting simple placement for: " + schematicName);
            if (this.plugin.getSchematicManager() != null) {
                try {
                    this.plugin.getSchematicManager().placeSchematic(schematicName, location);
                    return true;
                }
                catch (Exception e) {
                    this.plugin.getLogger().warning("[SimpleSchematicStick] SchematicManager placement failed: " + e.getMessage());
                }
            }
            this.createSimpleStructure(location, schematicName);
            return true;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[SimpleSchematicStick] Simple placement failed: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    private void createSimpleStructure(Location location, String schematicName) {
        World world = location.getWorld();
        int x = location.getBlockX();
        int y = location.getBlockY();
        int z = location.getBlockZ();
        if (schematicName.toLowerCase().contains("start")) {
            this.createRoom(world, x, y, z, 7, 4, 7, Material.STONE_BRICKS, Material.EMERALD_BLOCK);
        } else if (schematicName.toLowerCase().contains("end")) {
            this.createRoom(world, x, y, z, 9, 5, 9, Material.STONE_BRICKS, Material.DIAMOND_BLOCK);
        } else if (schematicName.toLowerCase().contains("boss")) {
            this.createRoom(world, x, y, z, 15, 6, 15, Material.STONE_BRICKS, Material.BEACON);
        } else {
            this.createRoom(world, x, y, z, 9, 4, 9, Material.COBBLESTONE, Material.TORCH);
        }
        this.plugin.getLogger().info("[SimpleSchematicStick] Created simple structure for: " + schematicName);
    }

    private void createRoom(World world, int startX, int startY, int startZ, int width, int height, int depth, Material wallMaterial, Material centerMaterial) {
        int y;
        int z;
        int x;
        for (x = 0; x < width; ++x) {
            for (z = 0; z < depth; ++z) {
                world.getBlockAt(startX + x, startY, startZ + z).setType(wallMaterial);
            }
        }
        for (int y2 = 1; y2 < height; ++y2) {
            for (int x2 = 0; x2 < width; ++x2) {
                world.getBlockAt(startX + x2, startY + y2, startZ).setType(wallMaterial);
                world.getBlockAt(startX + x2, startY + y2, startZ + depth - 1).setType(wallMaterial);
            }
            for (z = 0; z < depth; ++z) {
                world.getBlockAt(startX, startY + y2, startZ + z).setType(wallMaterial);
                world.getBlockAt(startX + width - 1, startY + y2, startZ + z).setType(wallMaterial);
            }
        }
        for (x = 0; x < width; ++x) {
            for (z = 0; z < depth; ++z) {
                world.getBlockAt(startX + x, startY + height, startZ + z).setType(wallMaterial);
            }
        }
        for (x = 1; x < width - 1; ++x) {
            for (z = 1; z < depth - 1; ++z) {
                for (y = 1; y < height; ++y) {
                    world.getBlockAt(startX + x, startY + y, startZ + z).setType(Material.AIR);
                }
            }
        }
        world.getBlockAt(startX + width / 2, startY + 1, startZ + depth / 2).setType(centerMaterial);
        int doorY = startY + 1;
        int doorHeight = 2;
        for (y = 0; y < doorHeight; ++y) {
            world.getBlockAt(startX + width / 2, doorY + y, startZ).setType(Material.AIR);
        }
        for (y = 0; y < doorHeight; ++y) {
            world.getBlockAt(startX + width / 2, doorY + y, startZ + depth - 1).setType(Material.AIR);
        }
    }

    public List<ItemStack> getAllSchematicSticks() {
        File[] files;
        ArrayList<ItemStack> sticks = new ArrayList<ItemStack>();
        File schematicsDir = new File(this.plugin.getDataFolder(), "schematics");
        if (schematicsDir.exists() && schematicsDir.isDirectory() && (files = schematicsDir.listFiles((dir, name) -> name.endsWith(".schem") || name.endsWith(".schematic"))) != null) {
            for (File file : files) {
                String name2 = file.getName().replaceAll("\\.(schem|schematic)$", "");
                sticks.add(this.createSchematicStick(name2));
            }
        }
        if (sticks.isEmpty()) {
            sticks.add(this.createSchematicStick("DunStart"));
            sticks.add(this.createSchematicStick("DunEnd"));
            sticks.add(this.createSchematicStick("DunZombie1"));
            sticks.add(this.createSchematicStick("DunSkeleton1"));
            sticks.add(this.createSchematicStick("FireBoss"));
            sticks.add(this.createSchematicStick("MiniBossA"));
        }
        return sticks;
    }
}

