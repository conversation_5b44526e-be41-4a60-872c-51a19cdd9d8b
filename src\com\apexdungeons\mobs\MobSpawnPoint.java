/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.GameMode
 *  org.bukkit.Location
 *  org.bukkit.Particle
 *  org.bukkit.entity.ArmorStand
 *  org.bukkit.entity.Player
 *  org.bukkit.entity.TextDisplay
 */
package com.apexdungeons.mobs;

import com.apexdungeons.mobs.DungeonMobConfig;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.bukkit.ChatColor;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Player;
import org.bukkit.entity.TextDisplay;

public class MobSpawnPoint {
    private final Location location;
    private final String mobName;
    private final double radius;
    private final boolean isBoss;
    private long lastSpawnTime = 0L;
    private long cooldownMs = 30000L;
    private int maxConcurrentMobs = 3;
    private final Set<UUID> spawnedMobs = new HashSet<UUID>();
    private ArmorStand detectionEntity;
    private boolean isActive = true;
    private String displayName;
    private TextDisplay builderDisplay;
    private DungeonMobConfig mobConfig;

    public MobSpawnPoint(Location location, String mobName, double radius, boolean isBoss) {
        this.location = location.clone();
        this.mobName = mobName;
        this.radius = radius;
        this.isBoss = isBoss;
        this.displayName = (isBoss ? "Boss: " : "Mob: ") + mobName;
        this.maxConcurrentMobs = isBoss ? 1 : 3;
        this.cooldownMs = isBoss ? 60000L : 30000L;
    }

    public Location getLocation() {
        return this.location.clone();
    }

    public String getMobName() {
        return this.mobName;
    }

    public double getRadius() {
        return this.radius;
    }

    public boolean isBoss() {
        return this.isBoss;
    }

    public long getLastSpawnTime() {
        return this.lastSpawnTime;
    }

    public void setLastSpawnTime(long lastSpawnTime) {
        this.lastSpawnTime = lastSpawnTime;
    }

    public long getCooldownMs() {
        return this.cooldownMs;
    }

    public void setCooldownMs(long cooldownMs) {
        this.cooldownMs = cooldownMs;
    }

    public int getMaxConcurrentMobs() {
        return this.maxConcurrentMobs;
    }

    public void setMaxConcurrentMobs(int maxConcurrentMobs) {
        this.maxConcurrentMobs = maxConcurrentMobs;
    }

    public Set<UUID> getSpawnedMobs() {
        return this.spawnedMobs;
    }

    public ArmorStand getDetectionEntity() {
        return this.detectionEntity;
    }

    public void setDetectionEntity(ArmorStand detectionEntity) {
        this.detectionEntity = detectionEntity;
    }

    public boolean isActive() {
        return this.isActive;
    }

    public void setActive(boolean active) {
        this.isActive = active;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public boolean isOnCooldown() {
        return System.currentTimeMillis() - this.lastSpawnTime < this.cooldownMs;
    }

    public boolean isAtMaxCapacity() {
        return this.spawnedMobs.size() >= this.maxConcurrentMobs;
    }

    public long getRemainingCooldownSeconds() {
        long remaining = this.cooldownMs - (System.currentTimeMillis() - this.lastSpawnTime);
        return Math.max(0L, remaining / 1000L);
    }

    public void addSpawnedMob(UUID mobId) {
        this.spawnedMobs.add(mobId);
    }

    public void removeSpawnedMob(UUID mobId) {
        this.spawnedMobs.remove(mobId);
    }

    public String getStatus() {
        if (!this.isActive) {
            return "Inactive";
        }
        if (this.isOnCooldown()) {
            return "Cooldown: " + this.getRemainingCooldownSeconds() + "s";
        }
        if (this.isAtMaxCapacity()) {
            return "Max spawned";
        }
        return "Ready";
    }

    public void setMobConfig(DungeonMobConfig config) {
        this.mobConfig = config;
        if (config != null) {
            this.displayName = config.getDisplayName();
            this.cooldownMs = (long)((config.getCooldownMin() + config.getCooldownMax()) / 2) * 1000L;
            this.maxConcurrentMobs = config.getMaxConcurrent();
        }
    }

    public DungeonMobConfig getMobConfig() {
        return this.mobConfig;
    }

    public void createBuilderDisplay() {
        if (this.builderDisplay != null) {
            this.builderDisplay.remove();
        }
        try {
            Location displayLoc = this.location.clone().add(0.0, 2.5, 0.0);
            this.builderDisplay = (TextDisplay)this.location.getWorld().spawn(displayLoc, TextDisplay.class);
            this.builderDisplay.setInvulnerable(true);
            this.builderDisplay.setGravity(false);
            this.builderDisplay.setPersistent(false);
            this.builderDisplay.setVisibleByDefault(false);
            this.updateBuilderDisplayText();
        }
        catch (Exception e) {
            this.createLegacyBuilderDisplay();
        }
    }

    public void updateBuilderDisplayText() {
        if (this.builderDisplay == null) {
            return;
        }
        StringBuilder text = new StringBuilder();
        text.append(ChatColor.GOLD).append("\u2694 ").append(this.displayName).append("\n");
        if (this.mobConfig != null) {
            text.append(this.mobConfig.getDifficultyColor()).append(this.mobConfig.getDifficulty().toUpperCase()).append(" ");
            text.append(this.mobConfig.getCategoryColor()).append(this.mobConfig.getCategory().toUpperCase()).append("\n");
        }
        text.append(ChatColor.GRAY).append("Radius: ").append(ChatColor.WHITE).append((int)this.radius).append("m\n");
        text.append(ChatColor.GRAY).append("Cooldown: ").append(ChatColor.WHITE).append(this.cooldownMs / 1000L).append("s\n");
        text.append(ChatColor.GRAY).append("Max: ").append(ChatColor.WHITE).append(this.maxConcurrentMobs).append(" mobs\n");
        String status = this.getBuilderStatusText();
        text.append(status);
        this.builderDisplay.setText(text.toString());
    }

    private void createLegacyBuilderDisplay() {
        if (this.detectionEntity != null) {
            this.detectionEntity.setCustomName(this.getBuilderDisplayName());
            this.detectionEntity.setCustomNameVisible(true);
        }
    }

    private String getBuilderStatusText() {
        if (!this.isActive) {
            return String.valueOf(ChatColor.GRAY) + "\u23f8 INACTIVE";
        }
        long timeSinceSpawn = System.currentTimeMillis() - this.lastSpawnTime;
        if (timeSinceSpawn < this.cooldownMs) {
            long remaining = (this.cooldownMs - timeSinceSpawn) / 1000L;
            return String.valueOf(ChatColor.RED) + "\u23f3 Cooldown: " + remaining + "s";
        }
        if (this.spawnedMobs.size() >= this.maxConcurrentMobs) {
            return String.valueOf(ChatColor.YELLOW) + "\u26a0 Max spawned (" + this.spawnedMobs.size() + ")";
        }
        return String.valueOf(ChatColor.GREEN) + "\u2713 Ready to spawn";
    }

    private String getBuilderDisplayName() {
        String status = this.getBuilderStatusText();
        return String.valueOf(ChatColor.GOLD) + this.displayName + " " + status;
    }

    public void showBuilderFeedback(Player player) {
        if (this.builderDisplay != null) {
            this.builderDisplay.setVisibleByDefault(false);
        }
        this.showRadiusParticles(player);
    }

    public void hideBuilderFeedback(Player player) {
        if (this.builderDisplay != null) {
            this.builderDisplay.setVisibleByDefault(false);
        }
    }

    private void showRadiusParticles(Player player) {
        int points = 32;
        for (int i = 0; i < points; ++i) {
            double angle = Math.PI * 2 * (double)i / (double)points;
            double x = this.location.getX() + this.radius * Math.cos(angle);
            double z = this.location.getZ() + this.radius * Math.sin(angle);
            Location particleLoc = new Location(this.location.getWorld(), x, this.location.getY() + 0.1, z);
            Particle particle = this.isBoss ? Particle.FLAME : Particle.HAPPY_VILLAGER;
            player.spawnParticle(particle, particleLoc, 1, 0.0, 0.0, 0.0, 0.0);
        }
    }

    public void removeBuilderDisplay() {
        if (this.builderDisplay != null) {
            this.builderDisplay.remove();
            this.builderDisplay = null;
        }
    }

    public boolean shouldShowBuilderFeedback(Player player) {
        return player.hasPermission("apexdungeons.builder") || player.hasPermission("apexdungeons.admin") || player.getGameMode() == GameMode.CREATIVE;
    }

    public String toString() {
        return String.format("MobSpawnPoint{mob=%s, location=%s, radius=%.1f, boss=%s, status=%s}", this.mobName, this.location, this.radius, this.isBoss, this.getStatus());
    }
}

