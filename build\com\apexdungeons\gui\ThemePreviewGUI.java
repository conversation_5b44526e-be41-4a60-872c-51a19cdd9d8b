/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.DungeonBrowserGUI;
import com.apexdungeons.gui.PresetGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class ThemePreviewGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.LIGHT_PURPLE) + "\ud83c\udfa8 Building Preview";

    public static void open(Player player, ApexDungeons plugin, String preset) {
        // Simplified - no theme preview, just building info
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)27, (String)(GUI_NAME + " - " + preset));
        ThemePreviewGUI.fillBackground(inv);
        ThemePreviewGUI.createBuildingPreview(inv, preset);
        ThemePreviewGUI.createNavigationButtons(inv, plugin, preset);
        player.openInventory(inv);
        ThemePreviewGUI.registerEventListener(plugin, preset);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 18, 19, 20, 21, 22, 23, 24, 25, 26}) {
            inv.setItem(slot, background);
        }
    }

    private static void createBuildingPreview(Inventory inv, String preset) {
        // Simplified building preview - no themes
        ItemStack preview = new ItemStack(Material.STRUCTURE_BLOCK);
        ItemMeta meta = preview.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83c\udfe0 Building Mode Preview");
        ArrayList<CallSite> lore = new ArrayList<CallSite>();
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Custom building dungeon with:")));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.AQUA) + "\u2022 No theme restrictions")));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.AQUA) + "\u2022 Full creative freedom")));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.AQUA) + "\u2022 Instance isolation")));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.AQUA) + "\u2022 MythicMobs integration")));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.GREEN) + "Preset: " + preset)));
        meta.setLore(lore);
        preview.setItemMeta(meta);
        inv.setItem(13, preview);
    }

    private static void createNavigationButtons(Inventory inv, ApexDungeons plugin, String preset) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2190 Back to Presets");
        back.setItemMeta(backMeta);
        inv.setItem(9, back);

        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u271a Create " + preset + " Dungeon");
        ArrayList<CallSite> createLore = new ArrayList<CallSite>();
        createLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Create a new building-focused dungeon")));
        createLore.add((CallSite)((Object)(String.valueOf(ChatColor.AQUA) + "✓ Instance isolated")));
        createLore.add((CallSite)((Object)(String.valueOf(ChatColor.AQUA) + "✓ MythicMobs ready")));
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(17, create);
    }

    private static void registerEventListener(final ApexDungeons plugin, String preset) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().contains(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 9: {
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                            break;
                        }
                        case 17: {
                            clicker.closeInventory();
                            // Create dungeon with selected preset
                            plugin.getDungeonManager().createDungeon(clicker, preset, true);
                        }
                    }
                }
            }
        }, (Plugin)plugin);
    }
}

