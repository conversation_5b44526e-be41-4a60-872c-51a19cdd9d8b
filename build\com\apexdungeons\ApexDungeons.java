/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.command.CommandExecutor
 *  org.bukkit.event.Listener
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.plugin.java.JavaPlugin
 */
package com.apexdungeons;

import com.apexdungeons.blocks.DungeonBlockManager;
import com.apexdungeons.chests.ChestLootManager;
import com.apexdungeons.chests.ChestSpawnData;
import com.apexdungeons.chests.ChestSpawnManager;
import com.apexdungeons.commands.DgnCommand;
import com.apexdungeons.config.DungeonConfig;
import com.apexdungeons.effects.EffectsManager;
import com.apexdungeons.gen.DungeonManager;
import com.apexdungeons.gui.PartyGUI;
import com.apexdungeons.instance.DungeonInstanceManager;
import com.apexdungeons.integration.MobAdapter;
import com.apexdungeons.integration.MythicMobsAdapter;
import com.apexdungeons.integration.VanillaAdapter;
import com.apexdungeons.listeners.ChestSpawnToolListener;
import com.apexdungeons.listeners.MobSpawnToolListener;
import com.apexdungeons.mobs.CustomMobManager;
import com.apexdungeons.mobs.DungeonMobManager;
import com.apexdungeons.mobs.MobSpawnData;
import com.apexdungeons.mobs.MobSpawnManager;
import com.apexdungeons.party.PartyManager;
import com.apexdungeons.player.PlayerLocationManager;
import com.apexdungeons.saved.SavedDungeonManager;
import com.apexdungeons.schematics.PreviewInputHandler;
import com.apexdungeons.schematics.SchematicManager;
import com.apexdungeons.templates.DungeonTemplateManager;
import com.apexdungeons.tools.ChestSpawnTool;
import com.apexdungeons.tools.MasterBuilderWand;
import com.apexdungeons.tools.MobSpawnTool;
import com.apexdungeons.tools.RoomConnector;
import com.apexdungeons.tools.SchematicTool;
import com.apexdungeons.tools.SimpleSchematicStick;
import com.apexdungeons.tools.SimpleSchematicTool;
import com.apexdungeons.wand.WandManager;
import com.apexdungeons.world.WorldManager;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.CopyOption;
import java.nio.file.Files;
import java.util.logging.Level;
import org.bukkit.command.CommandExecutor;
import org.bukkit.event.Listener;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

public class ApexDungeons
extends JavaPlugin {
    private DungeonManager dungeonManager;
    private WandManager wandManager;
    private WorldManager worldManager;
    private EffectsManager effectsManager;
    private PlayerLocationManager playerLocationManager;
    private DungeonBlockManager dungeonBlockManager;
    private SchematicManager schematicManager;
    private SchematicTool schematicTool;
    private SimpleSchematicTool simpleSchematicTool;
    private SimpleSchematicStick simpleSchematicStick;
    private MasterBuilderWand masterBuilderWand;
    private PreviewInputHandler previewInputHandler;
    private RoomConnector roomConnector;
    private CustomMobManager customMobManager;
    private MobAdapter mobAdapter;
    private MobSpawnTool mobSpawnTool;
    private MobSpawnManager mobSpawnManager;
    private MobSpawnData mobSpawnData;
    private DungeonMobManager dungeonMobManager;
    private ChestSpawnTool chestSpawnTool;
    private ChestSpawnManager chestSpawnManager;
    private ChestSpawnData chestSpawnData;
    private ChestLootManager chestLootManager;
    private SavedDungeonManager savedDungeonManager;
    private DungeonConfig dungeonConfig;
    private DungeonTemplateManager templateManager;
    private DungeonInstanceManager instanceManager;
    private PartyManager partyManager;
    private PartyGUI partyGUI;

    public void onEnable() {
        this.saveDefaultConfig();
        this.saveResourceIfMissing("loot.yml");
        this.saveResourceIfMissing("mobs.yml");
        this.saveResourceIfMissing("bosses.yml");
        this.saveResourceIfMissing("messages.yml");
        this.saveResourceIfMissing("presets/small.yml");
        this.saveResourceIfMissing("presets/medium.yml");
        this.saveResourceIfMissing("presets/large.yml");
        this.saveResourceIfMissing("presets/normal.yml");
        this.saveResourceIfMissing("presets/temple.yml");
        this.saveResourceIfMissing("presets/crypt.yml");
        this.getLogger().info("Copying default room blueprints...");
        this.saveResourceFolderIfMissing("rooms");
        this.getLogger().info("Initializing managers...");
        this.worldManager = new WorldManager(this);
        this.effectsManager = new EffectsManager(this);
        this.playerLocationManager = new PlayerLocationManager(this);
        this.dungeonBlockManager = new DungeonBlockManager(this);
        this.schematicManager = new SchematicManager(this);
        this.schematicTool = new SchematicTool(this);
        this.simpleSchematicTool = new SimpleSchematicTool(this);
        this.simpleSchematicStick = new SimpleSchematicStick(this);
        this.masterBuilderWand = new MasterBuilderWand(this);
        this.previewInputHandler = new PreviewInputHandler(this);
        this.roomConnector = new RoomConnector(this);
        this.dungeonManager = new DungeonManager(this);
        this.getLogger().info("Loading dungeon blueprints and presets...");
        this.dungeonManager.loadBlueprints();
        this.dungeonManager.loadPresets();
        this.wandManager = new WandManager(this);
        this.mobAdapter = this.initializeMobAdapter();
        this.mobSpawnTool = new MobSpawnTool(this);
        this.mobSpawnData = new MobSpawnData(this);
        this.mobSpawnManager = new MobSpawnManager(this);
        this.dungeonMobManager = new DungeonMobManager(this);
        this.chestLootManager = new ChestLootManager(this);
        this.chestSpawnTool = new ChestSpawnTool(this);
        this.chestSpawnData = new ChestSpawnData(this);
        this.chestSpawnManager = new ChestSpawnManager(this);
        this.savedDungeonManager = new SavedDungeonManager(this);
        this.dungeonConfig = new DungeonConfig(this);
        this.templateManager = new DungeonTemplateManager(this);
        this.instanceManager = new DungeonInstanceManager(this);
        this.partyManager = new PartyManager(this);
        this.partyGUI = new PartyGUI(this);
        this.customMobManager = new CustomMobManager(this);
        this.getLogger().info("Registering commands and listeners...");
        this.getCommand("dgn").setExecutor((CommandExecutor)new DgnCommand(this));
        this.getServer().getPluginManager().registerEvents((Listener)new MobSpawnToolListener(this), (Plugin)this);
        this.getServer().getPluginManager().registerEvents((Listener)new ChestSpawnToolListener(this), (Plugin)this);
        this.getLogger().info("ApexDungeons has been enabled.");
    }

    public void onDisable() {
        if (this.dungeonManager != null) {
            this.dungeonManager.shutdown();
        }
        if (this.worldManager != null) {
            this.worldManager.shutdown();
        }
        if (this.playerLocationManager != null) {
            this.playerLocationManager.shutdown();
        }
        if (this.mobSpawnManager != null) {
            this.mobSpawnManager.shutdown();
        }
        if (this.mobSpawnData != null) {
            this.mobSpawnData.shutdown();
        }
        if (this.savedDungeonManager != null) {
            this.savedDungeonManager.shutdown();
        }
        if (this.chestSpawnManager != null) {
            this.chestSpawnManager.shutdown();
        }
        if (this.chestSpawnData != null) {
            this.chestSpawnData.shutdown();
        }
        if (this.chestLootManager != null) {
            this.chestLootManager.shutdown();
        }
        if (this.dungeonBlockManager != null) {
            this.dungeonBlockManager.shutdown();
        }
        if (this.schematicManager != null) {
            this.schematicManager.shutdown();
        }
        if (this.schematicTool != null) {
            this.schematicTool.shutdown();
        }
        if (this.previewInputHandler != null) {
            this.previewInputHandler.shutdown();
        }
        if (this.roomConnector != null) {
            this.roomConnector.shutdown();
        }
        this.getLogger().info("ApexDungeons has been disabled.");
    }

    private void saveResourceIfMissing(String resourcePath) {
        File outFile = new File(this.getDataFolder(), resourcePath);
        if (!outFile.exists()) {
            if (outFile.getParentFile() != null) {
                outFile.getParentFile().mkdirs();
            }
            try (InputStream in = this.getResource(resourcePath);){
                if (in == null) {
                    this.getLogger().warning("Resource " + resourcePath + " not found inside jar.");
                    return;
                }
                Files.copy(in, outFile.toPath(), new CopyOption[0]);
            }
            catch (IOException ex) {
                this.getLogger().log(Level.SEVERE, "Failed to save resource " + resourcePath, ex);
            }
        }
    }

    private void saveResourceFolderIfMissing(String folderPath) {
        try {
            this.getLogger().info("Copying blueprint files from " + folderPath + "...");
            this.saveResourceIfMissing(folderPath + "/starter_room.yml");
            this.saveResourceIfMissing(folderPath + "/corridor.yml");
            this.saveResourceIfMissing(folderPath + "/boss_room.yml");
            this.saveResourceIfMissing(folderPath + "/castle_hall.yml");
            this.saveResourceIfMissing(folderPath + "/castle_tower.yml");
            this.saveResourceIfMissing(folderPath + "/castle_dungeon.yml");
            this.saveResourceIfMissing(folderPath + "/cave_tunnel.yml");
            this.saveResourceIfMissing(folderPath + "/underground_lake.yml");
            this.saveResourceIfMissing(folderPath + "/temple_chamber.yml");
            this.saveResourceIfMissing(folderPath + "/crypt_chamber.yml");
            this.getLogger().info("Blueprint files copied successfully!");
        }
        catch (Exception ex) {
            this.getLogger().log(Level.SEVERE, "Failed to copy resource folder " + folderPath, ex);
        }
    }

    public DungeonManager getDungeonManager() {
        return this.dungeonManager;
    }

    public WandManager getWandManager() {
        return this.wandManager;
    }

    public WorldManager getWorldManager() {
        return this.worldManager;
    }

    public EffectsManager getEffectsManager() {
        return this.effectsManager;
    }

    public PlayerLocationManager getPlayerLocationManager() {
        return this.playerLocationManager;
    }

    public DungeonConfig getDungeonConfig() {
        return this.dungeonConfig;
    }

    public DungeonTemplateManager getTemplateManager() {
        return this.templateManager;
    }

    public MobAdapter getMobAdapter() {
        return this.mobAdapter;
    }

    public MobSpawnTool getMobSpawnTool() {
        return this.mobSpawnTool;
    }

    public MobSpawnManager getMobSpawnManager() {
        return this.mobSpawnManager;
    }

    public MobSpawnData getMobSpawnData() {
        return this.mobSpawnData;
    }

    public SavedDungeonManager getSavedDungeonManager() {
        return this.savedDungeonManager;
    }

    public ChestSpawnTool getChestSpawnTool() {
        return this.chestSpawnTool;
    }

    public ChestSpawnManager getChestSpawnManager() {
        return this.chestSpawnManager;
    }

    public ChestSpawnData getChestSpawnData() {
        return this.chestSpawnData;
    }

    public ChestLootManager getChestLootManager() {
        return this.chestLootManager;
    }

    private MobAdapter initializeMobAdapter() {
        if (this.getServer().getPluginManager().getPlugin("MythicMobs") != null) {
            this.getLogger().info("MythicMobs detected, using MythicMobs adapter");
            return new MythicMobsAdapter(this);
        }
        this.getLogger().info("MythicMobs not found, using vanilla adapter");
        return new VanillaAdapter();
    }

    public DungeonInstanceManager getInstanceManager() {
        return this.instanceManager;
    }

    public PartyManager getPartyManager() {
        return this.partyManager;
    }

    public PartyGUI getPartyGUI() {
        return this.partyGUI;
    }

    public DungeonBlockManager getDungeonBlockManager() {
        return this.dungeonBlockManager;
    }

    public SchematicManager getSchematicManager() {
        return this.schematicManager;
    }

    public SchematicTool getSchematicTool() {
        return this.schematicTool;
    }

    public SimpleSchematicTool getSimpleSchematicTool() {
        return this.simpleSchematicTool;
    }

    public MasterBuilderWand getMasterBuilderWand() {
        return this.masterBuilderWand;
    }

    public PreviewInputHandler getPreviewInputHandler() {
        return this.previewInputHandler;
    }

    public RoomConnector getRoomConnector() {
        return this.roomConnector;
    }

    public SimpleSchematicStick getSimpleSchematicStick() {
        return this.simpleSchematicStick;
    }

    public DungeonMobManager getDungeonMobManager() {
        return this.dungeonMobManager;
    }

    public CustomMobManager getCustomMobManager() {
        return this.customMobManager;
    }
}

