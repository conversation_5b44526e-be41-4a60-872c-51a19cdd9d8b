/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.EnhancedMainGUI;
import com.apexdungeons.gui.PresetGUI;
import com.apexdungeons.gui.ThemePreviewGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class DungeonBrowserGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_PURPLE) + "\ud83d\udcda Dungeon Browser";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        DungeonBrowserGUI.fillBackground(inv);
        DungeonBrowserGUI.createThemeButtons(inv, plugin);
        DungeonBrowserGUI.createNavigationButtons(inv);
        player.openInventory(inv);
        DungeonBrowserGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createThemeButtons(Inventory inv, ApexDungeons plugin) {
        ItemStack castle = new ItemStack(Material.STONE_BRICKS);
        ItemMeta castleMeta = castle.getItemMeta();
        castleMeta.setDisplayName(String.valueOf(ChatColor.GRAY) + "\ud83c\udff0 Castle Theme");
        ArrayList<Object> castleLore = new ArrayList<Object>();
        castleLore.add(String.valueOf(ChatColor.YELLOW) + "Medieval Fortress Dungeons");
        castleLore.add("");
        castleLore.add(String.valueOf(ChatColor.GRAY) + "Features:");
        castleLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Stone brick architecture");
        castleLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Multi-level towers");
        castleLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Throne rooms & armories");
        castleLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Medieval atmosphere");
        castleLore.add("");
        castleLore.add(String.valueOf(ChatColor.YELLOW) + "Difficulty: " + String.valueOf(ChatColor.GREEN) + "Medium");
        castleLore.add(String.valueOf(ChatColor.YELLOW) + "Recommended Level: " + String.valueOf(ChatColor.WHITE) + "10-20");
        castleLore.add(String.valueOf(ChatColor.YELLOW) + "Estimated Time: " + String.valueOf(ChatColor.WHITE) + "25-35 minutes");
        castleLore.add("");
        castleLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to preview!");
        castleMeta.setLore(castleLore);
        castle.setItemMeta(castleMeta);
        inv.setItem(20, castle);
        ItemStack cave = new ItemStack(Material.COBBLESTONE);
        ItemMeta caveMeta = cave.getItemMeta();
        caveMeta.setDisplayName(String.valueOf(ChatColor.DARK_GRAY) + "\ud83d\udd73 Cave Theme");
        ArrayList<Object> caveLore = new ArrayList<Object>();
        caveLore.add(String.valueOf(ChatColor.YELLOW) + "Underground Cave Systems");
        caveLore.add("");
        caveLore.add(String.valueOf(ChatColor.GRAY) + "Features:");
        caveLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Natural stone formations");
        caveLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Underground pools & rivers");
        caveLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Ore veins & crystals");
        caveLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Dark atmosphere");
        caveLore.add("");
        caveLore.add(String.valueOf(ChatColor.YELLOW) + "Difficulty: " + String.valueOf(ChatColor.GOLD) + "Hard");
        caveLore.add(String.valueOf(ChatColor.YELLOW) + "Recommended Level: " + String.valueOf(ChatColor.WHITE) + "15-25");
        caveLore.add(String.valueOf(ChatColor.YELLOW) + "Estimated Time: " + String.valueOf(ChatColor.WHITE) + "20-30 minutes");
        caveLore.add("");
        caveLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to preview!");
        caveMeta.setLore(caveLore);
        cave.setItemMeta(caveMeta);
        inv.setItem(22, cave);
        ItemStack temple = new ItemStack(Material.SANDSTONE);
        ItemMeta templeMeta = temple.getItemMeta();
        templeMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83c\udfdb Temple Theme");
        ArrayList<Object> templeLore = new ArrayList<Object>();
        templeLore.add(String.valueOf(ChatColor.YELLOW) + "Ancient Mystical Temples");
        templeLore.add("");
        templeLore.add(String.valueOf(ChatColor.GRAY) + "Features:");
        templeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Golden decorations");
        templeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Mystical chambers");
        templeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Ancient traps & puzzles");
        templeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Sacred atmosphere");
        templeLore.add("");
        templeLore.add(String.valueOf(ChatColor.YELLOW) + "Difficulty: " + String.valueOf(ChatColor.RED) + "Expert");
        templeLore.add(String.valueOf(ChatColor.YELLOW) + "Recommended Level: " + String.valueOf(ChatColor.WHITE) + "20-30");
        templeLore.add(String.valueOf(ChatColor.YELLOW) + "Estimated Time: " + String.valueOf(ChatColor.WHITE) + "45-60 minutes");
        templeLore.add("");
        templeLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to preview!");
        templeMeta.setLore(templeLore);
        temple.setItemMeta(templeMeta);
        inv.setItem(24, temple);
        ItemStack sizeInfo = new ItemStack(Material.COMPASS);
        ItemMeta sizeInfoMeta = sizeInfo.getItemMeta();
        sizeInfoMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccf Size Variants");
        ArrayList<Object> sizeInfoLore = new ArrayList<Object>();
        sizeInfoLore.add(String.valueOf(ChatColor.GRAY) + "Each theme offers multiple sizes:");
        sizeInfoLore.add("");
        sizeInfoLore.add(String.valueOf(ChatColor.GREEN) + "Small: " + String.valueOf(ChatColor.WHITE) + "5-8 rooms (15-20 min)");
        sizeInfoLore.add(String.valueOf(ChatColor.YELLOW) + "Medium: " + String.valueOf(ChatColor.WHITE) + "8-12 rooms (25-35 min)");
        sizeInfoLore.add(String.valueOf(ChatColor.GOLD) + "Large: " + String.valueOf(ChatColor.WHITE) + "12-18 rooms (45-60 min)");
        sizeInfoLore.add(String.valueOf(ChatColor.RED) + "Massive: " + String.valueOf(ChatColor.WHITE) + "18-25 rooms (60-90 min)");
        sizeInfoLore.add("");
        sizeInfoLore.add(String.valueOf(ChatColor.GRAY) + "Choose your preferred size");
        sizeInfoLore.add(String.valueOf(ChatColor.GRAY) + "when creating a dungeon!");
        sizeInfoMeta.setLore(sizeInfoLore);
        sizeInfo.setItemMeta(sizeInfoMeta);
        inv.setItem(31, sizeInfo);
        ItemStack featuresInfo = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta featuresInfoMeta = featuresInfo.getItemMeta();
        featuresInfoMeta.setDisplayName(String.valueOf(ChatColor.LIGHT_PURPLE) + "\u2728 Special Features");
        ArrayList<Object> featuresInfoLore = new ArrayList<Object>();
        featuresInfoLore.add(String.valueOf(ChatColor.GRAY) + "All dungeons include:");
        featuresInfoLore.add("");
        featuresInfoLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Isolated world instances");
        featuresInfoLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Portal travel system");
        featuresInfoLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Custom mob spawning");
        featuresInfoLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Treasure chests & loot");
        featuresInfoLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Boss encounters");
        featuresInfoLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Visual & sound effects");
        featuresInfoLore.add("");
        featuresInfoLore.add(String.valueOf(ChatColor.GRAY) + "Experience the ultimate");
        featuresInfoLore.add(String.valueOf(ChatColor.GRAY) + "dungeon adventure!");
        featuresInfoMeta.setLore(featuresInfoLore);
        featuresInfo.setItemMeta(featuresInfoMeta);
        inv.setItem(40, featuresInfo);
    }

    private static void createNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2190 Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u271a Create Dungeon");
        ArrayList<CallSite> createLore = new ArrayList<CallSite>();
        createLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Ready to create your dungeon?")));
        createLore.add((CallSite)((Object)(String.valueOf(ChatColor.GREEN) + "Click to start the creation process!")));
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(53, create);
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 20: {
                            clicker.closeInventory();
                            ThemePreviewGUI.open(clicker, plugin, "castle");
                            break;
                        }
                        case 22: {
                            clicker.closeInventory();
                            ThemePreviewGUI.open(clicker, plugin, "cave");
                            break;
                        }
                        case 24: {
                            clicker.closeInventory();
                            ThemePreviewGUI.open(clicker, plugin, "temple");
                            break;
                        }
                        case 45: {
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                        }
                    }
                }
            }
        }, (Plugin)plugin);
    }
}

