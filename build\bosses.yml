# ------------------------------------------------------------------------------
# Boss definitions for ApexDungeons - MythicMobs Integration
# Bosses use MythicMobs system directly with full command integration
# Supports MythicMobs levels, skills, and all native features
# ------------------------------------------------------------------------------

# MythicMobs boss integration
mythic_boss_integration:
  enabled: true
  use_mythic_commands: true
  use_mythic_levels: true
  use_mythic_skills: true

bosses:
  # Primary dungeon boss - uses MythicMobs
  dungeon_lord:
    type: MM:DungeonLord
    level: 8-12
    announce: true
    spawn_command: "mm mobs spawn DungeonLord {location} {level}"
    death_rewards: true

  # Elite knight boss
  elite_knight:
    type: MM:EliteKnight
    level: 6-10
    announce: true
    spawn_command: "mm mobs spawn EliteKnight {location} {level}"
    death_rewards: true

  # Ancient lich boss
  ancient_lich:
    type: MM:AncientLich
    level: 10-15
    announce: true
    spawn_command: "mm mobs spawn AncientLich {location} {level}"
    death_rewards: true

  # Fallback vanilla bosses
  wither_basic:
    type: VANILLA:WITHER
    healthMultiplier: 2.0
    damageMultiplier: 1.5
    announce: true

  iron_golem_champion:
    type: VANILLA:IRON_GOLEM
    healthMultiplier: 3.0
    damageMultiplier: 2.0
    announce: true