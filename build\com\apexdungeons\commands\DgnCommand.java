/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.World
 *  org.bukkit.command.Command
 *  org.bukkit.command.CommandExecutor
 *  org.bukkit.command.CommandSender
 *  org.bukkit.command.TabCompleter
 *  org.bukkit.entity.Player
 *  org.bukkit.plugin.Plugin
 *  org.jetbrains.annotations.NotNull
 *  org.jetbrains.annotations.Nullable
 */
package com.apexdungeons.commands;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.gui.AdminGUI;
import com.apexdungeons.gui.BuildingToolsGUI;
import com.apexdungeons.gui.DungeonManagementGUI;
import com.apexdungeons.gui.EnhancedMainGUI;
import com.apexdungeons.gui.StatisticsGUI;
import com.apexdungeons.party.Party;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class DgnCommand
implements CommandExecutor,
TabCompleter {
    private final ApexDungeons plugin;

    public DgnCommand(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    public boolean onCommand(@NotNull CommandSender sender, @NotNull Command command, @NotNull String label, @NotNull String[] args) {
        String sub;
        if (args.length == 0) {
            if (!(sender instanceof Player)) {
                sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to use this command.");
                return true;
            }
            Player player = (Player)sender;
            if (!player.hasPermission("apexdungeons.use")) {
                player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use Soaps Dungeons.");
                return true;
            }
            EnhancedMainGUI.open(player, this.plugin);
            return true;
        }
        switch (sub = args[0].toLowerCase()) {
            case "admin": {
                return this.handleAdmin(sender);
            }
            case "manage": {
                return this.handleManage(sender);
            }
            case "create": {
                return this.handleCreate(sender, args);
            }
            case "delete": 
            case "remove": {
                return this.handleDelete(sender, args);
            }
            case "list": {
                return this.handleList(sender, args);
            }
            case "tp": 
            case "teleport": {
                return this.handleTeleport(sender, args);
            }
            case "info": {
                return this.handleInfo(sender, args);
            }
            case "portal": {
                return this.handlePortal(sender, args);
            }
            case "stats": {
                return this.handleStats(sender);
            }
            case "reload": {
                return this.handleReload(sender);
            }
            case "reloadschematics": {
                return this.handleReloadSchematics(sender);
            }
            case "testworld": {
                return this.handleTestWorld(sender);
            }
            case "testdungeon": {
                return this.handleTestDungeon(sender);
            }
            case "tools": {
                return this.handleTools(sender);
            }
            case "version": {
                return this.handleVersion(sender);
            }
            case "help": {
                return this.handleHelp(sender, args);
            }
            case "setspawn": {
                return this.handleSetSpawn(sender, args);
            }
            case "setexit": {
                return this.handleSetExit(sender, args);
            }
            case "save": {
                return this.handleSave(sender, args);
            }
            case "load": {
                return this.handleLoad(sender, args);
            }
            case "party": {
                return this.handleParty(sender, args);
            }
            case "mobspawn": {
                return this.handleMobSpawn(sender, args);
            }
            case "bossspawn": {
                return this.handleBossSpawn(sender, args);
            }
            case "chestspawn": {
                return this.handleChestSpawn(sender, args);
            }
            case "givewand": {
                if (!sender.hasPermission("apexdungeons.admin")) {
                    sender.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
                    return true;
                }
                if (args.length < 2) {
                    sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn givewand <player>");
                    return true;
                }
                Player target = Bukkit.getPlayerExact((String)args[1]);
                if (target == null) {
                    sender.sendMessage(String.valueOf(ChatColor.RED) + "Player not found.");
                    return true;
                }
                this.plugin.getWandManager().giveWand(target);
                sender.sendMessage(String.valueOf(ChatColor.GREEN) + "Gave a wand to " + target.getName());
                return true;
            }
            case "leave": {
                return this.handleLeave(sender);
            }
            case "start": {
                return this.handleStart(sender, args);
            }
        }
        sender.sendMessage(String.valueOf(ChatColor.RED) + "Unknown sub command.");
        return true;
    }

    private boolean handleAdmin(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to use this command.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use the admin GUI.");
            return true;
        }
        AdminGUI.open(player, this.plugin);
        return true;
    }

    private boolean handleManage(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to use this command.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.use")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to manage dungeons.");
            return true;
        }
        DungeonManagementGUI.open(player, this.plugin);
        return true;
    }

    private boolean handleCreate(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to create dungeons.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.create")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to create dungeons.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn create <name>");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Creates a new dungeon with the specified name");
            return true;
        }
        String dungeonName = args[1];
        if (dungeonName.length() < 3 || dungeonName.length() > 32) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon name must be 3-32 characters long!");
            return true;
        }
        if (!dungeonName.matches("[a-zA-Z0-9_-]+")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon name can only contain letters, numbers, hyphens, and underscores!");
            return true;
        }
        this.plugin.getDungeonManager().createDungeon(dungeonName, player);
        return true;
    }

    private boolean handleDelete(CommandSender sender, String[] args) {
        if (!sender.hasPermission("apexdungeons.delete")) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to delete dungeons.");
            return true;
        }
        if (args.length < 2) {
            sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn delete <dungeon_name>");
            return true;
        }
        String dungeonName = args[1];
        if (!this.plugin.getDungeonManager().getDungeons().containsKey(dungeonName)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeonName + "' not found.");
            return true;
        }
        this.plugin.getDungeonManager().removeDungeon(dungeonName, sender);
        return true;
    }

    private boolean handleList(CommandSender sender, String[] args) {
        if (!sender.hasPermission("apexdungeons.list")) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to list dungeons.");
            return true;
        }
        Map<String, DungeonInstance> dungeons = this.plugin.getDungeonManager().getDungeons();
        if (dungeons.isEmpty()) {
            sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "No active dungeons found.");
            return true;
        }
        sender.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Active Dungeons ===");
        for (Map.Entry<String, DungeonInstance> entry : dungeons.entrySet()) {
            DungeonInstance dungeon = entry.getValue();
            String status = dungeon.isGenerating() ? String.valueOf(ChatColor.YELLOW) + "Generating" : String.valueOf(ChatColor.GREEN) + "Ready";
            sender.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + dungeon.getDisplayName() + String.valueOf(ChatColor.GRAY) + " (" + dungeon.getCreator() + ") - " + status);
        }
        return true;
    }

    private boolean handleTeleport(CommandSender sender, String[] args) {
        Location spawnLoc;
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to teleport.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.teleport")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to teleport to dungeons.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn tp <dungeon_name>");
            return true;
        }
        String dungeonName = args[1];
        if (dungeonName.equals("test")) {
            World testWorld = this.plugin.getWorldManager().getDungeonWorld("test");
            if (testWorld != null) {
                Location spawnLoc2 = new Location(testWorld, 0.0, 64.0, 0.0);
                player.teleport(spawnLoc2);
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Teleported to test world: " + testWorld.getName());
                this.plugin.getPlayerLocationManager().storePlayerLocation(player, "test");
                return true;
            }
            player.sendMessage(String.valueOf(ChatColor.RED) + "Test world not found. Create it first with /dgn testworld");
            return true;
        }
        DungeonInstance dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            this.plugin.getLogger().warning("[/dgn tp] Dungeon not found in manager: " + dungeonName + " \u2014 attempting recovery from world mapping.");
            boolean recovered = this.plugin.getDungeonManager().recoverDungeonFromWorld(dungeonName, player);
            if (recovered) {
                dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
                this.plugin.getLogger().info("[/dgn tp] Recovery successful for '" + dungeonName + "'.");
            }
            if (dungeon == null) {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeonName + "' not found.");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "Use /dgn list to see available dungeons.");
                Set<String> availableDungeons = this.plugin.getDungeonManager().getDungeons().keySet();
                this.plugin.getLogger().info("Teleport failed - dungeon not found: " + dungeonName);
                this.plugin.getLogger().info("Available dungeons: " + String.valueOf(availableDungeons));
                this.plugin.getLogger().info("World mappings: " + String.valueOf(this.plugin.getWorldManager().getDungeonToWorldMappings()));
                return true;
            }
        }
        if (dungeon.isGenerating()) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon is still generating. Please wait...");
            return true;
        }
        World dungeonWorld = dungeon.getWorld();
        if (dungeonWorld == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon world is not available!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "The dungeon may need to be recreated.");
            this.plugin.getLogger().warning("Dungeon " + dungeonName + " has null world!");
            return true;
        }
        if (Bukkit.getWorld((String)dungeonWorld.getName()) == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon world is not loaded!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Attempting to reload world...");
            this.plugin.getLogger().warning("Dungeon world not loaded: " + dungeonWorld.getName());
            World reloadedWorld = this.plugin.getWorldManager().getDungeonWorld(dungeonName);
            if (reloadedWorld == null) {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to reload dungeon world!");
                return true;
            }
            dungeonWorld = reloadedWorld;
        }
        if ((spawnLoc = this.plugin.getWorldManager().getDungeonSpawnLocation(dungeonName)) != null) {
            this.plugin.getEffectsManager().playDungeonEntryEffects(player, dungeon);
            player.teleport(spawnLoc);
            dungeon.addPlayer(player);
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "Teleported to dungeon '" + dungeon.getDisplayName() + "'!");
            this.plugin.getLogger().info("Successfully teleported " + player.getName() + " to dungeon: " + dungeonName);
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to find dungeon spawn location!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Debug information:");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Dungeon: " + dungeonName);
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 World: " + (dungeonWorld != null ? dungeonWorld.getName() : "null"));
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Origin: " + (dungeon.getOrigin() != null ? dungeon.getOrigin().toString() : "null"));
            this.plugin.getLogger().warning("Teleport failed for dungeon: " + dungeonName);
            this.plugin.getLogger().info("Dungeon world: " + (dungeonWorld != null ? dungeonWorld.getName() : "null"));
            this.plugin.getLogger().info("Dungeon origin: " + (dungeon.getOrigin() != null ? dungeon.getOrigin().toString() : "null"));
            this.plugin.getLogger().info("World mappings: " + String.valueOf(this.plugin.getWorldManager().getDungeonToWorldMappings()));
        }
        return true;
    }

    private boolean handleInfo(CommandSender sender, String[] args) {
        if (!sender.hasPermission("apexdungeons.info")) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to view dungeon info.");
            return true;
        }
        if (args.length < 2) {
            sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn info <dungeon_name>");
            return true;
        }
        String dungeonName = args[1];
        DungeonInstance dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeonName + "' not found.");
            return true;
        }
        sender.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Dungeon Information ===");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Name: " + String.valueOf(ChatColor.WHITE) + dungeon.getDisplayName());
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Creator: " + String.valueOf(ChatColor.WHITE) + dungeon.getCreator());
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "World: " + String.valueOf(ChatColor.WHITE) + dungeon.getWorld().getName());
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Rooms: " + String.valueOf(ChatColor.WHITE) + dungeon.getRoomCount());
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Status: " + (dungeon.isGenerating() ? String.valueOf(ChatColor.GOLD) + "Generating" : String.valueOf(ChatColor.GREEN) + "Ready"));
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Players: " + String.valueOf(ChatColor.WHITE) + dungeon.getPlayers().size());
        return true;
    }

    private boolean handlePortal(CommandSender sender, String[] args) {
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Portal management coming soon!");
        return true;
    }

    private boolean handleStats(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to view statistics.");
            return true;
        }
        Player player = (Player)sender;
        StatisticsGUI.open(player, this.plugin);
        return true;
    }

    private boolean handleReload(CommandSender sender) {
        if (!sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to reload the plugin.");
            return true;
        }
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Reloading Soaps Dungeons...");
        sender.sendMessage(String.valueOf(ChatColor.GREEN) + "Soaps Dungeons reloaded successfully!");
        return true;
    }

    private boolean handleReloadSchematics(CommandSender sender) {
        if (!sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to reload schematics.");
            return true;
        }
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Reloading schematics from folder...");
        this.plugin.getSchematicManager().reloadSchematics();
        int count = this.plugin.getSchematicManager().getLoadedSchematicNames().size();
        sender.sendMessage(String.valueOf(ChatColor.GREEN) + "Schematics reloaded! " + count + " schematics available.");
        sender.sendMessage(String.valueOf(ChatColor.GRAY) + "Available: " + String.join((CharSequence)", ", this.plugin.getSchematicManager().getLoadedSchematicNames()));
        return true;
    }

    private boolean handleTestWorld(CommandSender sender) {
        if (!sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to test world generation.");
            return true;
        }
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to test world generation.");
            return true;
        }
        Player player = (Player)sender;
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Creating test superflat world...");
        sender.sendMessage(String.valueOf(ChatColor.GRAY) + "This may take a moment...");
        ((CompletableFuture)this.plugin.getWorldManager().createDungeonWorld("test").thenAccept(world -> Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
            if (world != null) {
                if (Bukkit.getWorld((String)world.getName()) == null) {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Test world created but not accessible!");
                    this.plugin.getLogger().severe("Test world created but not found in Bukkit: " + world.getName());
                    return;
                }
                Location spawnLoc = new Location(world, 0.0, 64.0, 0.0);
                player.teleport(spawnLoc);
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Test world created! You've been teleported to: " + world.getName());
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "Check if the world is properly superflat. Use /dgn leave to return.");
                player.sendMessage(String.valueOf(ChatColor.DARK_GRAY) + "World type: " + String.valueOf(world.getWorldType()));
                this.plugin.getPlayerLocationManager().storePlayerLocation(player, "test");
                this.plugin.getLogger().info("Successfully created test world: " + world.getName() + " for " + player.getName());
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to create test world!");
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "This may be due to:");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Server configuration or permissions");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Insufficient disk space");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Bukkit/Spigot version compatibility");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "Check the console for detailed error information.");
                this.plugin.getLogger().warning("Test world creation failed for player: " + player.getName());
            }
        }))).exceptionally(throwable -> {
            Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Critical error during test world creation!");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "Error: " + throwable.getMessage());
                this.plugin.getLogger().severe("Critical error during test world creation: " + throwable.getMessage());
                throwable.printStackTrace();
            });
            return null;
        });
        return true;
    }

    private boolean handleTools(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to access building tools.");
            return true;
        }
        Player player = (Player)sender;
        player.closeInventory();
        BuildingToolsGUI.open(player, this.plugin);
        return true;
    }

    private boolean handleVersion(CommandSender sender) {
        sender.sendMessage(String.valueOf(ChatColor.GOLD) + "Soaps Dungeons " + String.valueOf(ChatColor.WHITE) + "v1.0.0 " + String.valueOf(ChatColor.GRAY) + "- Made by Vexy");
        sender.sendMessage(String.valueOf(ChatColor.GRAY) + "Enhanced dungeon creation and management plugin");
        return true;
    }

    private boolean handleLeave(CommandSender sender) {
        DungeonInstance dungeon;
        String dungeonName;
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to use this command.");
            return true;
        }
        Player player = (Player)sender;
        if (!this.plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You are not currently in a dungeon!");
            return true;
        }
        Location returnLocation = this.plugin.getPlayerLocationManager().getPlayerReturnLocation(player);
        if (returnLocation == null) {
            returnLocation = ((World)this.plugin.getServer().getWorlds().get(0)).getSpawnLocation();
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "No return location found, teleporting to main world spawn.");
        }
        if ((dungeonName = this.plugin.getWorldManager().getDungeonNameFromWorld(player.getWorld())) != null && (dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName)) != null) {
            dungeon.removePlayer(player);
        }
        this.plugin.getPlayerLocationManager().clearPlayerLocation(player);
        this.plugin.getEffectsManager().playDungeonExitEffects(player, dungeonName != null ? dungeonName : "Unknown");
        player.teleport(returnLocation);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "You have left the dungeon and returned to your previous location!");
        return true;
    }

    private boolean handleStart(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to use this command.");
            return true;
        }
        Player player = (Player)sender;
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn start <dungeon_name>");
            return true;
        }
        String dungeonName = args[1];
        DungeonInstance dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }
        if (dungeon.isGenerating()) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon is still generating. Please wait...");
            return true;
        }
        this.plugin.getPlayerLocationManager().storePlayerLocation(player, dungeonName);
        Location spawnLocation = this.plugin.getWorldManager().getDungeonSpawnLocation(dungeonName);
        if (spawnLocation == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to find dungeon spawn location!");
            this.plugin.getPlayerLocationManager().clearPlayerLocation(player);
            return true;
        }
        dungeon.addPlayer(player);
        this.plugin.getEffectsManager().playDungeonEntryEffects(player, dungeon);
        player.teleport(spawnLocation);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Started dungeon '" + dungeon.getDisplayName() + "'! Use /dgn leave to exit.");
        return true;
    }

    private boolean handleHelp(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "Help guides are only available to players.");
            return true;
        }
        Player player = (Player)sender;
        if (args.length >= 2) {
            String topic;
            switch (topic = args[1].toLowerCase()) {
                case "rooms": {
                    this.showRoomGuide(player);
                    return true;
                }
                case "connections": {
                    this.showConnectionGuide(player);
                    return true;
                }
                case "blocks": {
                    this.showBlocksGuide(player);
                    return true;
                }
                case "schematics": {
                    this.showSchematicsGuide(player);
                    return true;
                }
                case "guide": 
                case "system": {
                    this.showMainGuide(player);
                    return true;
                }
            }
            player.sendMessage(String.valueOf(ChatColor.RED) + "Unknown help topic: " + topic);
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Available topics: rooms, connections, blocks, schematics, guide");
            return true;
        }
        sender.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Soaps Dungeons Commands - Made by Vexy ===");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn" + String.valueOf(ChatColor.WHITE) + " - Open main GUI");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn manage" + String.valueOf(ChatColor.WHITE) + " - Open dungeon management");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn create <name>" + String.valueOf(ChatColor.WHITE) + " - Create a flat dungeon world");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn delete <name>" + String.valueOf(ChatColor.WHITE) + " - Delete a dungeon");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn list" + String.valueOf(ChatColor.WHITE) + " - List active dungeons");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn tp <name>" + String.valueOf(ChatColor.WHITE) + " - Teleport to dungeon");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn start <name>" + String.valueOf(ChatColor.WHITE) + " - Start/enter a dungeon");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn leave" + String.valueOf(ChatColor.WHITE) + " - Leave current dungeon");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn tools" + String.valueOf(ChatColor.WHITE) + " - Open building tools GUI");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party" + String.valueOf(ChatColor.WHITE) + " - Party management system");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn info <name>" + String.valueOf(ChatColor.WHITE) + " - View dungeon info");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn stats" + String.valueOf(ChatColor.WHITE) + " - View your statistics");
        sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn version" + String.valueOf(ChatColor.WHITE) + " - Show plugin version");
        if (sender.hasPermission("apexdungeons.admin")) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn admin" + String.valueOf(ChatColor.WHITE) + " - Admin tools");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn reload" + String.valueOf(ChatColor.WHITE) + " - Reload plugin");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn reloadschematics" + String.valueOf(ChatColor.WHITE) + " - Reload schematic files");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn testworld" + String.valueOf(ChatColor.WHITE) + " - Create test superflat world");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn testdungeon" + String.valueOf(ChatColor.WHITE) + " - Test dungeon creation");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn setspawn <dungeon>" + String.valueOf(ChatColor.WHITE) + " - Set custom spawn location");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn setexit <dungeon>" + String.valueOf(ChatColor.WHITE) + " - Set custom exit location");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn save <dungeon> <template>" + String.valueOf(ChatColor.WHITE) + " - Save dungeon as template");
            sender.sendMessage(String.valueOf(ChatColor.RED) + "/dgn load <template> <dungeon>" + String.valueOf(ChatColor.WHITE) + " - Load template as new dungeon");
        }
        sender.sendMessage("");
        sender.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udff0 ROOM SYSTEM GUIDES:");
        sender.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help guide" + String.valueOf(ChatColor.WHITE) + " - Complete room system overview");
        sender.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help rooms" + String.valueOf(ChatColor.WHITE) + " - Room types and design");
        sender.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help connections" + String.valueOf(ChatColor.WHITE) + " - Connecting rooms");
        sender.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help blocks" + String.valueOf(ChatColor.WHITE) + " - Start/End blocks");
        sender.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help schematics" + String.valueOf(ChatColor.WHITE) + " - Using schematics");
        return true;
    }

    private boolean handleSetSpawn(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn setspawn <dungeon_name>");
            return true;
        }
        String dungeonName = args[1];
        if (this.plugin.getDungeonManager().getDungeon(dungeonName) == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }
        Location currentLocation = player.getLocation();
        this.plugin.getDungeonConfig().setCustomSpawnLocation(dungeonName, currentLocation);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Custom spawn location set for dungeon '" + dungeonName + "'!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Location: " + currentLocation.getWorld().getName() + " " + currentLocation.getBlockX() + "," + currentLocation.getBlockY() + "," + currentLocation.getBlockZ());
        return true;
    }

    private boolean handleSetExit(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn setexit <dungeon_name>");
            return true;
        }
        String dungeonName = args[1];
        if (this.plugin.getDungeonManager().getDungeon(dungeonName) == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }
        Location currentLocation = player.getLocation();
        this.plugin.getDungeonConfig().setCustomExitLocation(dungeonName, currentLocation);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Custom exit location set for dungeon '" + dungeonName + "'!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Location: " + currentLocation.getWorld().getName() + " " + currentLocation.getBlockX() + "," + currentLocation.getBlockY() + "," + currentLocation.getBlockZ());
        return true;
    }

    private boolean handleSave(CommandSender sender, String[] args) {
        String worldName;
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn save <saved_name> [dungeon_name]");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "If dungeon_name is not specified, uses your current dungeon");
            return true;
        }
        String savedName = args[1];
        String dungeonName = args.length >= 3 ? args[2] : ((worldName = player.getWorld().getName()).startsWith("dungeon_") ? worldName.substring(8) : null);
        if (dungeonName == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Could not determine dungeon name. Please specify: /dgn save <saved_name> <dungeon_name>");
            return true;
        }
        if (this.plugin.getDungeonManager().getDungeon(dungeonName) == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeonName + "' not found!");
            return true;
        }
        if (this.plugin.getSavedDungeonManager().exists(savedName)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Saved dungeon '" + savedName + "' already exists!");
            return true;
        }
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Saving dungeon '" + dungeonName + "' as '" + savedName + "'...");
        this.plugin.getServer().getScheduler().runTaskAsynchronously((Plugin)this.plugin, () -> {
            boolean success = this.plugin.getSavedDungeonManager().saveDungeon(dungeonName, savedName, player);
            this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> {
                if (success) {
                    player.sendMessage(String.valueOf(ChatColor.GREEN) + "Successfully saved dungeon as '" + savedName + "'!");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "You can now load it from the Saved Dungeons menu or use /dgn load " + savedName + " <new_name>");
                } else {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to save dungeon. Check console for details.");
                }
            });
        });
        return true;
    }

    private boolean handleLoad(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
            return true;
        }
        if (args.length < 3) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn load <saved_name> <new_dungeon_name>");
            return true;
        }
        String savedName = args[1];
        String newDungeonName = args[2];
        if (!this.plugin.getSavedDungeonManager().exists(savedName)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Saved dungeon '" + savedName + "' not found!");
            return true;
        }
        if (this.plugin.getDungeonManager().getDungeon(newDungeonName) != null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + newDungeonName + "' already exists!");
            return true;
        }
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Loading saved dungeon '" + savedName + "' as '" + newDungeonName + "'...");
        boolean success = this.plugin.getSavedDungeonManager().loadSavedDungeon(savedName, newDungeonName, player);
        if (!success) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to load saved dungeon. It may not exist or there was an error.");
        }
        return true;
    }

    private boolean handleParty(CommandSender sender, String[] args) {
        String subCommand;
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (args.length < 2) {
            if (this.plugin.getPartyManager().isInParty(player)) {
                this.plugin.getPartyGUI().openPartyGUI(player);
            } else {
                this.showPartyHelp(player);
            }
            return true;
        }
        switch (subCommand = args[1].toLowerCase()) {
            case "create": {
                return this.handlePartyCreate(player);
            }
            case "invite": {
                return this.handlePartyInvite(player, args);
            }
            case "accept": {
                return this.handlePartyAccept(player, args);
            }
            case "decline": {
                return this.handlePartyDecline(player, args);
            }
            case "leave": {
                return this.handlePartyLeave(player);
            }
            case "kick": {
                return this.handlePartyKick(player, args);
            }
            case "info": {
                return this.handlePartyInfo(player);
            }
            case "help": {
                this.showPartyHelp(player);
                return true;
            }
        }
        this.showPartyHelp(player);
        return true;
    }

    private boolean handlePartyCreate(Player player) {
        if (this.plugin.getPartyManager().isInParty(player)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You are already in a party! Use /dgn party leave to leave your current party.");
            return true;
        }
        Party party = this.plugin.getPartyManager().createParty(player);
        if (party != null) {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udf89 Party created! You are now the party leader.");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.WHITE) + "/dgn party invite <player>" + String.valueOf(ChatColor.YELLOW) + " to invite players to your party.");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.WHITE) + "/dgn party" + String.valueOf(ChatColor.YELLOW) + " to open the party management GUI.");
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to create party!");
        }
        return true;
    }

    private boolean handlePartyInvite(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn party invite <player>");
            return true;
        }
        Party party = this.plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You are not in a party! Use /dgn party create to create one.");
            return true;
        }
        if (!party.isLeader(player)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Only the party leader can invite players!");
            return true;
        }
        String targetName = args[2];
        Player target = this.plugin.getServer().getPlayer(targetName);
        if (target == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Player '" + targetName + "' not found or not online!");
            return true;
        }
        if (target.equals((Object)player)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You cannot invite yourself!");
            return true;
        }
        if (this.plugin.getPartyManager().invitePlayer(party, player, target)) {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "Invitation sent to " + target.getName() + "!");
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to invite player. They may already be in a party or have a pending invite.");
        }
        return true;
    }

    private boolean handlePartyAccept(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn party accept <leader_name>");
            return true;
        }
        String leaderName = args[2];
        if (this.plugin.getPartyManager().acceptInvite(player, leaderName)) {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udf89 You joined " + leaderName + "'s party!");
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "No pending invitation from " + leaderName + " or failed to join party!");
        }
        return true;
    }

    private boolean handlePartyDecline(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn party decline <leader_name>");
            return true;
        }
        String leaderName = args[2];
        if (this.plugin.getPartyManager().declineInvite(player, leaderName)) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You declined the party invitation from " + leaderName + ".");
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "No pending invitation from " + leaderName + "!");
        }
        return true;
    }

    private boolean handlePartyLeave(Player player) {
        if (!this.plugin.getPartyManager().leaveParty(player)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You are not in a party!");
        }
        return true;
    }

    private boolean handlePartyKick(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Usage: /dgn party kick <player>");
            return true;
        }
        String targetName = args[2];
        Player target = this.plugin.getServer().getPlayer(targetName);
        if (target == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Player '" + targetName + "' not found or not online!");
            return true;
        }
        if (!this.plugin.getPartyManager().kickPlayer(player, target)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to kick player. You may not be the party leader or the player is not in your party.");
        }
        return true;
    }

    private boolean handlePartyInfo(Player player) {
        Party party = this.plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You are not in a party!");
            return true;
        }
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Party Information ===");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Leader: " + String.valueOf(ChatColor.WHITE) + party.getLeader().getName());
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Size: " + String.valueOf(ChatColor.WHITE) + party.getSize() + "/" + party.getMaxSize());
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Members:");
        for (Player member : party.getMembers()) {
            String status = member.isOnline() ? String.valueOf(ChatColor.GREEN) + "Online" : String.valueOf(ChatColor.RED) + "Offline";
            String role = party.isLeader(member) ? String.valueOf(ChatColor.GOLD) + " (Leader)" : "";
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "  \u2022 " + String.valueOf(ChatColor.WHITE) + member.getName() + role + " - " + status);
        }
        return true;
    }

    private void showPartyHelp(Player player) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Party System Help ===");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Party Commands:");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party create" + String.valueOf(ChatColor.WHITE) + " - Create a new party");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party invite <player>" + String.valueOf(ChatColor.WHITE) + " - Invite a player to your party");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party accept <leader>" + String.valueOf(ChatColor.WHITE) + " - Accept a party invitation");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party decline <leader>" + String.valueOf(ChatColor.WHITE) + " - Decline a party invitation");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party leave" + String.valueOf(ChatColor.WHITE) + " - Leave your current party");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party kick <player>" + String.valueOf(ChatColor.WHITE) + " - Kick a player from your party (leader only)");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party info" + String.valueOf(ChatColor.WHITE) + " - Show party information");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "/dgn party" + String.valueOf(ChatColor.WHITE) + " - Open party management GUI");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\ud83d\udca1 Tips:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Maximum 4 players per party");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Use the GUI for easy party management");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Parties work with dungeon instancing");
    }

    private void showMainGuide(Player player) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Soaps Dungeons Complete Guide - Made by Vexy ===");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udff0 Welcome to Soaps Dungeons!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "This plugin provides advanced dungeon building and management tools.");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udcda Available Help Topics:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help rooms" + String.valueOf(ChatColor.WHITE) + " - Room design and types");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help connections" + String.valueOf(ChatColor.WHITE) + " - Connecting rooms");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help blocks" + String.valueOf(ChatColor.WHITE) + " - Start and end blocks");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn help schematics" + String.valueOf(ChatColor.WHITE) + " - Using schematics");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf Quick Start:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "1. Use /dgn tools to get building tools");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "2. Create your dungeon with /dgn create <name>");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "3. Build rooms and connect them");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "4. Place start and end blocks");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "5. Test with /dgn start <name>");
    }

    private void showRoomGuide(Player player) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Room Design Guide ===");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfd7\ufe0f Room Types:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Entrance Room" + String.valueOf(ChatColor.WHITE) + " - Contains start block");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Challenge Room" + String.valueOf(ChatColor.WHITE) + " - Puzzles, mobs, obstacles");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Boss Room" + String.valueOf(ChatColor.WHITE) + " - Final challenge with end block");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Treasure Room" + String.valueOf(ChatColor.WHITE) + " - Rewards and loot");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 Design Tips:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Leave 3-block high spaces for doorways");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Use varied room sizes for interest");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Consider lighting and atmosphere");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Plan your layout before building");
    }

    private void showConnectionGuide(Player player) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Room Connection Guide ===");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd17 Connection Tool Usage:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Get the Room Connector from /dgn tools");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Left-click on first room wall");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Right-click on second room wall");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Type connection type in chat (1, 2, or 3)");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udeaa Connection Types:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Type 1:" + String.valueOf(ChatColor.WHITE) + " Simple doorway (3x3 opening)");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Type 2:" + String.valueOf(ChatColor.WHITE) + " Decorated doorway with frame");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Type 3:" + String.valueOf(ChatColor.WHITE) + " Full corridor passage");
    }

    private void showBlocksGuide(Player player) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Start & End Blocks Guide ===");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf Start Block (Emerald Block):");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Place in your entrance room");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Players right-click to begin challenge");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Only one per dungeon");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Starts timer and teleports player");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfc6 End Block (Diamond Block):");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Place in your final/boss room");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Players right-click to complete challenge");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Gives rewards and teleports back");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Must activate start block first");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u2699\ufe0f Custom Locations:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn setspawn <dungeon>" + String.valueOf(ChatColor.WHITE) + " - Set custom spawn");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn setexit <dungeon>" + String.valueOf(ChatColor.WHITE) + " - Set custom exit");
    }

    private void showSchematicsGuide(Player player) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Schematics Guide ===");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udcd0 Schematic Tools:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Get schematic tools from /dgn tools");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Right-click for 3D preview mode");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Left-click for immediate placement");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfae Preview Controls:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "W/A/S/D:" + String.valueOf(ChatColor.WHITE) + " Move preview position");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "R:" + String.valueOf(ChatColor.WHITE) + " Rotate 90 degrees");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Enter:" + String.valueOf(ChatColor.WHITE) + " Confirm placement");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Escape:" + String.valueOf(ChatColor.WHITE) + " Cancel preview");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udca1 Tips:");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Use preview to avoid placement mistakes");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Schematics include pre-built rooms");
        player.sendMessage(String.valueOf(ChatColor.WHITE) + "\u2022 Perfect for consistent room designs");
    }

    @Nullable
    public List<String> onTabComplete(@NotNull CommandSender sender, @NotNull Command command, @NotNull String alias, @NotNull String[] args) {
        ArrayList<String> completions = new ArrayList<String>();
        if (args.length == 1) {
            completions.add("admin");
            completions.add("givewand");
            completions.add("create");
            completions.add("remove");
            completions.add("tp");
            completions.add("start");
            completions.add("leave");
            completions.add("tools");
            completions.add("list");
            completions.add("info");
            completions.add("stats");
            completions.add("version");
            completions.add("help");
            if (sender.hasPermission("apexdungeons.admin")) {
                completions.add("reloadschematics");
                completions.add("testworld");
                completions.add("setspawn");
                completions.add("setexit");
                completions.add("save");
                completions.add("load");
                completions.add("party");
            }
            return completions;
        }
        if (args.length == 2) {
            String sub;
            switch (sub = args[0].toLowerCase()) {
                case "givewand": {
                    for (Player p : Bukkit.getOnlinePlayers()) {
                        completions.add(p.getName());
                    }
                    break;
                }
                case "remove": 
                case "tp": 
                case "start": 
                case "info": 
                case "setspawn": 
                case "setexit": 
                case "save": {
                    completions.addAll(this.plugin.getDungeonManager().listDungeonNames());
                    break;
                }
                case "load": {
                    completions.addAll(this.plugin.getTemplateManager().getTemplateNames());
                    break;
                }
                case "party": {
                    completions.add("create");
                    completions.add("invite");
                    completions.add("accept");
                    completions.add("decline");
                    completions.add("leave");
                    completions.add("kick");
                    completions.add("info");
                    completions.add("help");
                    break;
                }
                case "help": {
                    completions.add("guide");
                    completions.add("rooms");
                    completions.add("connections");
                    completions.add("blocks");
                    completions.add("schematics");
                    break;
                }
            }
        }
        return completions;
    }

    private boolean handleMobSpawn(CommandSender sender, String[] args) {
        String subCommand;
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.mobspawn")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Usage: /dgn mobspawn <set|clear|list|radius> [mob_name|radius]");
            return true;
        }
        switch (subCommand = args[1].toLowerCase()) {
            case "set": {
                return this.handleMobSpawnSet(player, args);
            }
            case "clear": {
                return this.handleMobSpawnClear(player);
            }
            case "list": {
                return this.handleMobSpawnList(player);
            }
            case "radius": {
                return this.handleMobSpawnRadius(player, args);
            }
        }
        player.sendMessage(String.valueOf(ChatColor.RED) + "Unknown subcommand. Use: set, clear, list, or radius");
        return true;
    }

    private boolean handleChestSpawn(CommandSender sender, String[] args) {
        String subCommand;
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.chestspawn")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use chest spawn commands.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Chest Spawn Commands:");
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn chestspawn set <loot_table> [radius] " + String.valueOf(ChatColor.GRAY) + "- Set loot table for chest spawning");
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn chestspawn list " + String.valueOf(ChatColor.GRAY) + "- List available loot tables");
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn chestspawn clear " + String.valueOf(ChatColor.GRAY) + "- Clear your loot table selection");
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "/dgn chestspawn info " + String.valueOf(ChatColor.GRAY) + "- Show your current settings");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Available loot tables: " + String.join((CharSequence)", ", this.plugin.getChestLootManager().getLootTableNames()));
            return true;
        }
        switch (subCommand = args[1].toLowerCase()) {
            case "set": {
                if (args.length < 3) {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Usage: /dgn chestspawn set <loot_table> [radius]");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "Available loot tables: " + String.join((CharSequence)", ", this.plugin.getChestLootManager().getLootTableNames()));
                    return true;
                }
                String lootTable = args[2];
                if (!this.plugin.getChestLootManager().getLootTableNames().contains(lootTable)) {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Unknown loot table: " + lootTable);
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "Available loot tables: " + String.join((CharSequence)", ", this.plugin.getChestLootManager().getLootTableNames()));
                    return true;
                }
                double radius = 3.0;
                if (args.length >= 4) {
                    try {
                        radius = Double.parseDouble(args[3]);
                        if (radius <= 0.0 || radius > 10.0) {
                            player.sendMessage(String.valueOf(ChatColor.RED) + "Radius must be between 0.1 and 10.0");
                            return true;
                        }
                    }
                    catch (NumberFormatException e) {
                        player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid radius. Must be a number between 0.1 and 10.0");
                        return true;
                    }
                }
                this.plugin.getChestSpawnData().setPlayerLootTableSelection(player.getUniqueId(), lootTable);
                this.plugin.getChestSpawnData().setPlayerRadius(player.getUniqueId(), radius);
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Chest spawn settings updated!");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "Loot Table: " + String.valueOf(ChatColor.WHITE) + lootTable);
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "Radius: " + String.valueOf(ChatColor.WHITE) + radius + " blocks");
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Now use the Chest Spawn Tool to place spawn points!");
                break;
            }
            case "list": {
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Available Loot Tables:");
                for (String tableName : this.plugin.getChestLootManager().getLootTableNames()) {
                    player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + tableName);
                }
                break;
            }
            case "clear": {
                this.plugin.getChestSpawnData().clearPlayerLootTableSelection(player.getUniqueId());
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Chest spawn loot table selection cleared.");
                break;
            }
            case "info": {
                String currentTable = this.plugin.getChestSpawnData().getPlayerLootTableSelection(player.getUniqueId());
                double currentRadius = this.plugin.getChestSpawnData().getPlayerRadius(player.getUniqueId(), 3.0);
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Your Chest Spawn Settings:");
                if (currentTable != null) {
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "Loot Table: " + String.valueOf(ChatColor.WHITE) + currentTable);
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "Radius: " + String.valueOf(ChatColor.WHITE) + currentRadius + " blocks");
                    break;
                }
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "No loot table configured. Use /dgn chestspawn set <loot_table>");
                break;
            }
            default: {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Unknown chest spawn command: " + subCommand);
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use /dgn chestspawn for help");
            }
        }
        return true;
    }

    private boolean handleBossSpawn(CommandSender sender, String[] args) {
        String subCommand;
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "This command can only be used by players.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin") && !player.hasPermission("apexdungeons.bossspawn")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
            return true;
        }
        if (args.length < 2) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Usage: /dgn bossspawn <set|clear|list|radius> [boss_name|radius]");
            return true;
        }
        switch (subCommand = args[1].toLowerCase()) {
            case "set": {
                return this.handleBossSpawnSet(player, args);
            }
            case "clear": {
                return this.handleBossSpawnClear(player);
            }
            case "list": {
                return this.handleBossSpawnList(player);
            }
            case "radius": {
                return this.handleBossSpawnRadius(player, args);
            }
        }
        player.sendMessage(String.valueOf(ChatColor.RED) + "Unknown subcommand. Use: set, clear, list, or radius");
        return true;
    }

    private boolean handleMobSpawnSet(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Usage: /dgn mobspawn set <mob_name>");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Available mobs:");
            String[] availableMobs = this.plugin.getMobAdapter().getAvailableMobs();
            if (availableMobs.length > 0) {
                for (String mob : availableMobs) {
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 " + String.valueOf(ChatColor.WHITE) + mob);
                }
            } else {
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "No mobs available");
            }
            return true;
        }
        String mobName = args[2];
        this.plugin.getMobSpawnData().setPlayerMobSelection(player.getUniqueId(), mobName);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Mob spawn configured!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Mob: " + String.valueOf(ChatColor.WHITE) + mobName);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Now use the Mob Spawn Tool to place spawn points");
        return true;
    }

    private boolean handleMobSpawnClear(Player player) {
        this.plugin.getMobSpawnData().clearPlayerMobSelection(player.getUniqueId());
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Mob spawn selection cleared.");
        return true;
    }

    private boolean handleMobSpawnList(Player player) {
        String[] availableMobs = this.plugin.getMobAdapter().getAvailableMobs();
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "=== Available Mobs ===");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Adapter: " + String.valueOf(ChatColor.WHITE) + this.plugin.getMobAdapter().getAdapterName());
        player.sendMessage("");
        if (availableMobs.length > 0) {
            for (String mob : availableMobs) {
                player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + mob);
            }
        } else {
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "No mobs available");
        }
        String currentSelection = this.plugin.getMobSpawnData().getPlayerMobSelection(player.getUniqueId());
        if (currentSelection != null) {
            player.sendMessage("");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Current selection: " + String.valueOf(ChatColor.WHITE) + currentSelection);
        }
        return true;
    }

    private boolean handleMobSpawnRadius(Player player, String[] args) {
        if (args.length < 3) {
            double currentRadius = this.plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 5.0);
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Current spawn radius: " + String.valueOf(ChatColor.WHITE) + currentRadius + " blocks");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Usage: /dgn mobspawn radius <number>");
            return true;
        }
        try {
            double radius = Double.parseDouble(args[2]);
            if (radius < 1.0 || radius > 20.0) {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Radius must be between 1.0 and 20.0 blocks");
                return true;
            }
            this.plugin.getMobSpawnData().setPlayerRadius(player.getUniqueId(), radius);
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Spawn radius set to " + radius + " blocks");
        }
        catch (NumberFormatException e) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid number: " + args[2]);
        }
        return true;
    }

    private boolean handleBossSpawnSet(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Usage: /dgn bossspawn set <boss_name>");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Available bosses:");
            String[] availableBosses = this.plugin.getMobAdapter().getAvailableBosses();
            if (availableBosses.length > 0) {
                for (String boss : availableBosses) {
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 " + String.valueOf(ChatColor.WHITE) + boss);
                }
            } else {
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "No bosses available");
            }
            return true;
        }
        String bossName = args[2];
        this.plugin.getMobSpawnData().setPlayerBossSelection(player.getUniqueId(), bossName);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Boss spawn configured!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Boss: " + String.valueOf(ChatColor.WHITE) + bossName);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Now use the Boss Spawn Tool to place spawn points");
        return true;
    }

    private boolean handleBossSpawnClear(Player player) {
        this.plugin.getMobSpawnData().clearPlayerBossSelection(player.getUniqueId());
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Boss spawn selection cleared.");
        return true;
    }

    private boolean handleBossSpawnList(Player player) {
        String[] availableBosses = this.plugin.getMobAdapter().getAvailableBosses();
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "=== Available Bosses ===");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Adapter: " + String.valueOf(ChatColor.WHITE) + this.plugin.getMobAdapter().getAdapterName());
        player.sendMessage("");
        if (availableBosses.length > 0) {
            for (String boss : availableBosses) {
                player.sendMessage(String.valueOf(ChatColor.DARK_RED) + "\u2022 " + String.valueOf(ChatColor.WHITE) + boss);
            }
        } else {
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "No bosses available");
        }
        String currentSelection = this.plugin.getMobSpawnData().getPlayerBossSelection(player.getUniqueId());
        if (currentSelection != null) {
            player.sendMessage("");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Current selection: " + String.valueOf(ChatColor.WHITE) + currentSelection);
        }
        return true;
    }

    private boolean handleBossSpawnRadius(Player player, String[] args) {
        if (args.length < 3) {
            double currentRadius = this.plugin.getMobSpawnData().getPlayerRadius(player.getUniqueId(), 7.0);
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Current boss spawn radius: " + String.valueOf(ChatColor.WHITE) + currentRadius + " blocks");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Usage: /dgn bossspawn radius <number>");
            return true;
        }
        try {
            double radius = Double.parseDouble(args[2]);
            if (radius < 1.0 || radius > 20.0) {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Radius must be between 1.0 and 20.0 blocks");
                return true;
            }
            this.plugin.getMobSpawnData().setPlayerRadius(player.getUniqueId(), radius);
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Boss spawn radius set to " + radius + " blocks");
        }
        catch (NumberFormatException e) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid number: " + args[2]);
        }
        return true;
    }

    private boolean handleTestDungeon(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "You must be a player to use this command.");
            return true;
        }
        Player player = (Player)sender;
        if (!player.hasPermission("apexdungeons.admin")) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to use this command.");
            return true;
        }
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Testing dungeon creation system...");
        String testName = "test_dungeon_" + System.currentTimeMillis();
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Creating test dungeon: " + testName);
        this.plugin.getDungeonManager().createDungeon(testName, player);
        return true;
    }
}

