/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.entity.Player
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import java.util.ArrayList;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class ChestSpawnTool {
    private final ApexDungeons plugin;
    private final NamespacedKey toolKey;

    public ChestSpawnTool(ApexDungeons plugin) {
        this.plugin = plugin;
        this.toolKey = new NamespacedKey((Plugin)plugin, "chest_spawn_tool");
    }

    public ItemStack createChestSpawnTool() {
        ItemStack tool = new ItemStack(Material.CHEST);
        ItemMeta meta = tool.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "Chest Spawn Tool");
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + "Right-click blocks to set chest spawn points");
        lore.add(String.valueOf(ChatColor.GRAY) + "Use /dgn chestspawn set <loot_table> to configure");
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccd How to use:");
        lore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Right-click a block to mark chest spawn");
        lore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Use /dgn chestspawn set <loot_table>");
        lore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Configure loot with weighted RNG");
        lore.add(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Players find chests when exploring");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\ud83d\udcb0 Features:");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Weighted item spawning");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Custom loot tables");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Configurable spawn rates");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\ud83d\udca1 Example loot tables:");
        lore.add(String.valueOf(ChatColor.GRAY) + "common, rare, epic, boss_rewards");
        meta.setLore(lore);
        meta.getPersistentDataContainer().set(this.toolKey, PersistentDataType.BYTE, (Object)1);
        tool.setItemMeta(meta);
        return tool;
    }

    public boolean isChestSpawnTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(this.toolKey, PersistentDataType.BYTE);
    }

    public void giveChestSpawnTool(Player player) {
        player.getInventory().addItem(new ItemStack[]{this.createChestSpawnTool()});
    }
}

