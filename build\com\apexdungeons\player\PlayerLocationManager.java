/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.player;

import com.apexdungeons.ApexDungeons;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Location;
import org.bukkit.entity.Player;

public class PlayerLocationManager {
    private final ApexDungeons plugin;
    private final Map<UUID, Location> playerReturnLocations = new HashMap<UUID, Location>();
    private final Map<UUID, String> playerCurrentDungeon = new HashMap<UUID, String>();

    public PlayerLocationManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    public void storePlayerLocation(Player player, String dungeonName) {
        UUID playerId = player.getUniqueId();
        Location currentLocation = player.getLocation().clone();
        this.playerReturnLocations.put(playerId, currentLocation);
        this.playerCurrentDungeon.put(playerId, dungeonName);
        this.plugin.getLogger().info("Stored return location for player " + player.getName() + " entering dungeon " + dungeonName + ": " + this.locationToString(currentLocation));
    }

    public Location getPlayerReturnLocation(Player player) {
        UUID playerId = player.getUniqueId();
        return this.playerReturnLocations.get(playerId);
    }

    public String getPlayerCurrentDungeon(Player player) {
        UUID playerId = player.getUniqueId();
        return this.playerCurrentDungeon.get(playerId);
    }

    public void clearPlayerLocation(Player player) {
        UUID playerId = player.getUniqueId();
        Location returnLoc = this.playerReturnLocations.remove(playerId);
        String dungeonName = this.playerCurrentDungeon.remove(playerId);
        if (returnLoc != null) {
            this.plugin.getLogger().info("Cleared return location for player " + player.getName() + " leaving dungeon " + dungeonName);
        }
    }

    public boolean hasStoredLocation(Player player) {
        UUID playerId = player.getUniqueId();
        return this.playerReturnLocations.containsKey(playerId);
    }

    public boolean isPlayerInDungeon(Player player) {
        UUID playerId = player.getUniqueId();
        return this.playerCurrentDungeon.containsKey(playerId);
    }

    public void updatePlayerDungeon(Player player, String newDungeonName) {
        UUID playerId = player.getUniqueId();
        if (this.playerCurrentDungeon.containsKey(playerId)) {
            this.playerCurrentDungeon.put(playerId, newDungeonName);
            this.plugin.getLogger().info("Updated player " + player.getName() + " current dungeon to: " + newDungeonName);
        }
    }

    public Map<UUID, String> getAllPlayersInDungeons() {
        return new HashMap<UUID, String>(this.playerCurrentDungeon);
    }

    public void cleanupOfflinePlayers() {
        this.playerReturnLocations.entrySet().removeIf(entry -> this.plugin.getServer().getPlayer((UUID)entry.getKey()) == null);
        this.playerCurrentDungeon.entrySet().removeIf(entry -> this.plugin.getServer().getPlayer((UUID)entry.getKey()) == null);
    }

    public void shutdown() {
        this.plugin.getLogger().info("Cleaning up player location data for " + this.playerReturnLocations.size() + " players...");
        this.playerReturnLocations.clear();
        this.playerCurrentDungeon.clear();
    }

    private String locationToString(Location loc) {
        return String.format("%s %.1f,%.1f,%.1f", loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }
}

