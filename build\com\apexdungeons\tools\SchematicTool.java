/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.event.player.PlayerQuitEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.scheduler.BukkitTask
 */
package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import com.apexdungeons.schematics.SchematicPreview;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitTask;

public class SchematicTool
implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey schematicToolKey;
    private final NamespacedKey schematicNameKey;
    private final Map<UUID, SchematicPreview> activePreviews = new HashMap<UUID, SchematicPreview>();
    private final Map<UUID, BukkitTask> previewTasks = new HashMap<UUID, BukkitTask>();

    public SchematicTool(ApexDungeons plugin) {
        this.plugin = plugin;
        this.schematicToolKey = new NamespacedKey((Plugin)plugin, "schematic_tool");
        this.schematicNameKey = new NamespacedKey((Plugin)plugin, "schematic_name");
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public ItemStack createSchematicTool(String schematicName) {
        SchematicData schematic = this.plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            return null;
        }
        ItemStack tool = new ItemStack(Material.GOLDEN_SHOVEL);
        ItemMeta meta = tool.getItemMeta();
        if (meta != null) {
            int blockCount = this.countNonAirBlocks(schematic);
            String complexity = this.getComplexityLevel(blockCount);
            meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udcd0 Schematic Tool: " + String.valueOf(ChatColor.YELLOW) + schematicName);
            ArrayList<Object> lore = new ArrayList<Object>();
            lore.add(String.valueOf(ChatColor.GRAY) + "Advanced schematic placement with 3D preview");
            lore.add("");
            lore.add(String.valueOf(ChatColor.AQUA) + "\ud83d\udcca Schematic Info:");
            lore.add(String.valueOf(ChatColor.YELLOW) + "  Size: " + String.valueOf(ChatColor.WHITE) + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
            lore.add(String.valueOf(ChatColor.YELLOW) + "  Blocks: " + String.valueOf(ChatColor.WHITE) + blockCount);
            lore.add(String.valueOf(ChatColor.YELLOW) + "  Complexity: " + complexity);
            lore.add("");
            lore.add(String.valueOf(ChatColor.GREEN) + "\ud83c\udfae Basic Controls:");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Right-click: " + String.valueOf(ChatColor.WHITE) + "Start 3D preview");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Left-click: " + String.valueOf(ChatColor.WHITE) + "Quick place (no preview)");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Shift+Right-click: " + String.valueOf(ChatColor.WHITE) + "Cancel preview");
            lore.add("");
            lore.add(String.valueOf(ChatColor.GREEN) + "\u2328\ufe0f Preview Controls:");
            lore.add(String.valueOf(ChatColor.AQUA) + "  W/A/S/D: " + String.valueOf(ChatColor.WHITE) + "Move horizontally");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Space/Shift: " + String.valueOf(ChatColor.WHITE) + "Move up/down");
            lore.add(String.valueOf(ChatColor.AQUA) + "  R: " + String.valueOf(ChatColor.WHITE) + "Rotate 90\u00b0 clockwise");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Q/E: " + String.valueOf(ChatColor.WHITE) + "Rotate left/right");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Enter: " + String.valueOf(ChatColor.WHITE) + "Confirm placement");
            lore.add(String.valueOf(ChatColor.AQUA) + "  Escape: " + String.valueOf(ChatColor.WHITE) + "Cancel preview");
            lore.add("");
            lore.add(String.valueOf(ChatColor.GRAY) + "\ud83d\udca1 Type control keys in chat during preview!");
            meta.setLore(lore);
            meta.getPersistentDataContainer().set(this.schematicToolKey, PersistentDataType.BYTE, (Object)1);
            meta.getPersistentDataContainer().set(this.schematicNameKey, PersistentDataType.STRING, (Object)schematicName);
            tool.setItemMeta(meta);
        }
        return tool;
    }

    private int countNonAirBlocks(SchematicData schematic) {
        Material[][][] blocks;
        int count = 0;
        Material[][][] materialArray = blocks = schematic.getBlocks();
        int n = materialArray.length;
        for (int i = 0; i < n; ++i) {
            Material[][] layer;
            Material[][] materialArray2 = layer = materialArray[i];
            int n2 = materialArray2.length;
            for (int j = 0; j < n2; ++j) {
                Material[] row;
                for (Material block : row = materialArray2[j]) {
                    if (block == Material.AIR) continue;
                    ++count;
                }
            }
        }
        return count;
    }

    private String getComplexityLevel(int blockCount) {
        if (blockCount < 50) {
            return String.valueOf(ChatColor.GREEN) + "Simple";
        }
        if (blockCount < 200) {
            return String.valueOf(ChatColor.YELLOW) + "Medium";
        }
        if (blockCount < 500) {
            return String.valueOf(ChatColor.GOLD) + "Complex";
        }
        return String.valueOf(ChatColor.RED) + "Very Complex";
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        if (!this.isSchematicTool(item)) {
            return;
        }
        event.setCancelled(true);
        String schematicName = this.getSchematicName(item);
        if (schematicName == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid schematic tool!");
            return;
        }
        Block targetBlock = player.getTargetBlockExact(10);
        if (targetBlock == null || targetBlock.getType() == Material.AIR) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Look at a block to target placement location!");
            return;
        }
        Location targetLocation = targetBlock.getLocation().add(0.0, 1.0, 0.0);
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            if (player.isSneaking()) {
                this.cancelPreview(player);
            } else {
                this.showSchematicPreview(player, schematicName, targetLocation);
            }
        } else if (event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK) {
            this.placeSchematic(player, schematicName, targetLocation);
        }
    }

    private void showSchematicPreview(Player player, String schematicName, Location location) {
        SchematicData schematic = this.plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Schematic not found: " + schematicName);
            return;
        }
        this.cancelPreview(player);
        SchematicPreview preview = new SchematicPreview(this.plugin, player, schematic, location);
        this.activePreviews.put(player.getUniqueId(), preview);
        this.plugin.getPreviewInputHandler().registerPreview(player, preview);
        preview.startPreview();
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Enhanced preview started for: " + String.valueOf(ChatColor.YELLOW) + schematicName);
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Size: " + String.valueOf(ChatColor.WHITE) + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);
    }

    private void showCornerParticles(Player player, Location base, int width, int height, int depth) {
        Location[] corners;
        World world = base.getWorld();
        if (world == null) {
            return;
        }
        for (Location corner : corners = new Location[]{base.clone(), base.clone().add((double)(width - 1), 0.0, 0.0), base.clone().add(0.0, 0.0, (double)(depth - 1)), base.clone().add((double)(width - 1), 0.0, (double)(depth - 1)), base.clone().add(0.0, (double)(height - 1), 0.0), base.clone().add((double)(width - 1), (double)(height - 1), 0.0), base.clone().add(0.0, (double)(height - 1), (double)(depth - 1)), base.clone().add((double)(width - 1), (double)(height - 1), (double)(depth - 1))}) {
            world.spawnParticle(Particle.END_ROD, corner.add(0.5, 0.5, 0.5), 1, 0.0, 0.0, 0.0, 0.0);
        }
    }

    private void showEdgeParticles(Player player, Location base, int width, int height, int depth) {
        World world = base.getWorld();
        if (world == null) {
            return;
        }
        for (int x = 0; x < width; x += 2) {
            world.spawnParticle(Particle.ENCHANT, base.clone().add((double)x + 0.5, 0.5, 0.5), 1, 0.0, 0.0, 0.0, 0.0);
            world.spawnParticle(Particle.ENCHANT, base.clone().add((double)x + 0.5, 0.5, (double)depth - 0.5), 1, 0.0, 0.0, 0.0, 0.0);
        }
        for (int z = 0; z < depth; z += 2) {
            world.spawnParticle(Particle.ENCHANT, base.clone().add(0.5, 0.5, (double)z + 0.5), 1, 0.0, 0.0, 0.0, 0.0);
            world.spawnParticle(Particle.ENCHANT, base.clone().add((double)width - 0.5, 0.5, (double)z + 0.5), 1, 0.0, 0.0, 0.0, 0.0);
        }
    }

    private void showKeyBlockParticles(Player player, SchematicData schematic, Location base) {
        World world = base.getWorld();
        if (world == null) {
            return;
        }
        int sampleRate = Math.max(1, schematic.getSolidBlocks() / 50);
        int count = 0;
        for (int y = 0; y < schematic.getHeight() && count < 50; ++y) {
            for (int z = 0; z < schematic.getDepth() && count < 50; z += sampleRate) {
                for (int x = 0; x < schematic.getWidth() && count < 50; x += sampleRate) {
                    Material material = schematic.getBlockAt(x, y, z);
                    if (material == Material.AIR) continue;
                    Location blockLoc = base.clone().add((double)x + 0.5, (double)y + 0.5, (double)z + 0.5);
                    world.spawnParticle(Particle.HAPPY_VILLAGER, blockLoc, 1, 0.0, 0.0, 0.0, 0.0);
                    ++count;
                }
            }
        }
    }

    private void placeSchematic(Player player, String schematicName, Location location) {
        this.cancelPreview(player);
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Placing schematic " + String.valueOf(ChatColor.AQUA) + schematicName + String.valueOf(ChatColor.YELLOW) + "...");
        this.plugin.getSchematicManager().placeSchematic(schematicName, location).thenAccept(success -> {
            if (success.booleanValue()) {
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Schematic placed successfully!");
                player.playSound(location, Sound.BLOCK_ANVIL_USE, 1.0f, 1.2f);
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to place schematic!");
            }
        });
    }

    private void cancelPreview(Player player) {
        UUID playerId = player.getUniqueId();
        SchematicPreview preview = this.activePreviews.remove(playerId);
        if (preview != null) {
            preview.stopPreview();
        }
        this.plugin.getPreviewInputHandler().unregisterPreview(player);
        BukkitTask task = this.previewTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Preview cancelled.");
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        this.cancelPreview(event.getPlayer());
    }

    private boolean isSchematicTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(this.schematicToolKey, PersistentDataType.BYTE);
    }

    private String getSchematicName(ItemStack item) {
        if (!this.isSchematicTool(item)) {
            return null;
        }
        ItemMeta meta = item.getItemMeta();
        return (String)meta.getPersistentDataContainer().get(this.schematicNameKey, PersistentDataType.STRING);
    }

    public Map<UUID, SchematicPreview> getActivePreviews() {
        return new HashMap<UUID, SchematicPreview>(this.activePreviews);
    }

    public void shutdown() {
        for (BukkitTask task : this.previewTasks.values()) {
            task.cancel();
        }
        this.previewTasks.clear();
        this.activePreviews.clear();
        this.plugin.getLogger().info("SchematicTool shutdown complete.");
    }
}

