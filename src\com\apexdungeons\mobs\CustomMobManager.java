/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.Location
 *  org.bukkit.command.CommandSender
 *  org.bukkit.entity.Player
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.mobs;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.mobs.CustomMobConfig;
import com.apexdungeons.mobs.CustomMobSpawnPoint;
import java.io.File;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

public class CustomMobManager {
    private final ApexDungeons plugin;
    private final Map<String, CustomMobConfig> customMobs = new HashMap<String, CustomMobConfig>();

    public CustomMobManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.loadCustomMobs();
    }

    public void loadCustomMobs() {
        this.customMobs.clear();
        File mobsDir = new File("ApexDungeons/dungeon_mobs");
        if (!mobsDir.exists() && !(mobsDir = new File(this.plugin.getDataFolder(), "dungeon_mobs")).exists()) {
            this.plugin.getLogger().info("No dungeon_mobs folder found at expected locations");
            this.plugin.getLogger().info("Tried: ApexDungeons/dungeon_mobs and " + String.valueOf(this.plugin.getDataFolder()) + "/dungeon_mobs");
            return;
        }
        this.plugin.getLogger().info("Loading custom mobs from: " + mobsDir.getAbsolutePath());
        File[] mobFiles = mobsDir.listFiles((dir, name) -> name.endsWith(".yml"));
        if (mobFiles == null || mobFiles.length == 0) {
            this.plugin.getLogger().info("No custom mob files found in dungeon_mobs folder");
            return;
        }
        for (File mobFile : mobFiles) {
            try {
                CustomMobConfig config = new CustomMobConfig(mobFile);
                this.customMobs.put(config.getFileName(), config);
                this.plugin.getLogger().info("Loaded custom mob: " + config.getDisplayName());
            }
            catch (Exception e) {
                this.plugin.getLogger().warning("Failed to load custom mob file: " + mobFile.getName() + " - " + e.getMessage());
            }
        }
        this.plugin.getLogger().info("Loaded " + this.customMobs.size() + " custom dungeon mobs");
    }

    public Collection<CustomMobConfig> getAllCustomMobs() {
        return this.customMobs.values();
    }

    public List<CustomMobConfig> getMobsByCategory(String category) {
        return this.customMobs.values().stream().filter(mob -> mob.getCategory().equalsIgnoreCase(category)).collect(Collectors.toList());
    }

    public List<CustomMobConfig> getMobsByDifficulty(String difficulty) {
        return this.customMobs.values().stream().filter(mob -> mob.getDifficulty().equalsIgnoreCase(difficulty)).collect(Collectors.toList());
    }

    public Set<String> getCategories() {
        return this.customMobs.values().stream().map(CustomMobConfig::getCategory).collect(Collectors.toSet());
    }

    public Set<String> getDifficulties() {
        return this.customMobs.values().stream().map(CustomMobConfig::getDifficulty).collect(Collectors.toSet());
    }

    public CustomMobConfig getCustomMob(String fileName) {
        return this.customMobs.get(fileName);
    }

    public boolean spawnCustomMob(String mobFileName, Location location, boolean isBuilderMode) {
        CustomMobConfig config = this.customMobs.get(mobFileName);
        if (config == null) {
            this.plugin.getLogger().warning("Custom mob not found: " + mobFileName);
            return false;
        }
        try {
            for (String command : config.getSpawnCommands()) {
                String processedCommand = command.replace("%x%", String.valueOf(location.getX())).replace("%y%", String.valueOf(location.getY())).replace("%z%", String.valueOf(location.getZ()));
                Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> Bukkit.dispatchCommand((CommandSender)Bukkit.getConsoleSender(), (String)processedCommand));
            }
            if (isBuilderMode) {
                for (Player player : location.getWorld().getPlayers()) {
                    if (!(player.getLocation().distance(location) <= 20.0)) continue;
                    player.sendMessage("\u00a7a[Builder] \u00a77Spawned custom mob: \u00a7f" + config.getDisplayName());
                }
            }
            this.plugin.getLogger().info("Spawned custom mob: " + config.getDisplayName() + " at " + location.getBlockX() + "," + location.getBlockY() + "," + location.getBlockZ());
            return true;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("Error spawning custom mob " + config.getDisplayName() + ": " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public void createCustomMobSpawnPoint(Location location, String mobFileName, Player creator) {
        CustomMobConfig config = this.customMobs.get(mobFileName);
        if (config == null) {
            creator.sendMessage("\u00a7cCustom mob not found: " + mobFileName);
            return;
        }
        String spawnPointId = "custom_" + mobFileName + "_" + System.currentTimeMillis();
        CustomMobSpawnPoint spawnPoint = new CustomMobSpawnPoint(spawnPointId, location, config, creator.getUniqueId());
        this.plugin.getMobSpawnManager().addCustomMobSpawnPoint(spawnPoint);
        creator.sendMessage("\u00a7a\u2713 Custom mob spawn point created!");
        creator.sendMessage("\u00a77Mob: \u00a7f" + config.getDisplayName());
        creator.sendMessage("\u00a77Radius: \u00a7f" + config.getSpawnRadius() + " blocks");
        creator.sendMessage("\u00a77Cooldown: \u00a7f" + config.getMinCooldown() + "-" + config.getMaxCooldown() + "s");
    }

    public boolean hasCustomMob(String fileName) {
        return this.customMobs.containsKey(fileName);
    }

    public int getCustomMobCount() {
        return this.customMobs.size();
    }

    public void reload() {
        this.plugin.getLogger().info("Reloading custom dungeon mobs...");
        this.loadCustomMobs();
    }
}

