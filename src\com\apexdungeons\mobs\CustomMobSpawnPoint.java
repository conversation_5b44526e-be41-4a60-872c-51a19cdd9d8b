/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 */
package com.apexdungeons.mobs;

import com.apexdungeons.mobs.CustomMobConfig;
import java.util.UUID;
import org.bukkit.Location;

public class CustomMobSpawnPoint {
    private final String id;
    private final Location location;
    private final CustomMobConfig mobConfig;
    private final UUID creatorId;
    private long lastSpawnTime;
    private boolean active;

    public CustomMobSpawnPoint(String id, Location location, CustomMobConfig mobConfig, UUID creatorId) {
        this.id = id;
        this.location = location.clone();
        this.mobConfig = mobConfig;
        this.creatorId = creatorId;
        this.lastSpawnTime = 0L;
        this.active = true;
    }

    public String getId() {
        return this.id;
    }

    public Location getLocation() {
        return this.location.clone();
    }

    public CustomMobConfig getMobConfig() {
        return this.mobConfig;
    }

    public UUID getCreatorId() {
        return this.creatorId;
    }

    public long getLastSpawnTime() {
        return this.lastSpawnTime;
    }

    public boolean isActive() {
        return this.active;
    }

    public void setLastSpawnTime(long time) {
        this.lastSpawnTime = time;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public boolean isOnCooldown() {
        long cooldownMs;
        if (this.lastSpawnTime == 0L) {
            return false;
        }
        long currentTime = System.currentTimeMillis();
        return currentTime - this.lastSpawnTime < (cooldownMs = ((long)this.mobConfig.getMinCooldown() + (long)(Math.random() * (double)(this.mobConfig.getMaxCooldown() - this.mobConfig.getMinCooldown()))) * 1000L);
    }

    public long getRemainingCooldownSeconds() {
        if (!this.isOnCooldown()) {
            return 0L;
        }
        long currentTime = System.currentTimeMillis();
        long cooldownMs = ((long)this.mobConfig.getMinCooldown() + (long)(Math.random() * (double)(this.mobConfig.getMaxCooldown() - this.mobConfig.getMinCooldown()))) * 1000L;
        return Math.max(0L, (cooldownMs - (currentTime - this.lastSpawnTime)) / 1000L);
    }

    public double getSpawnRadius() {
        return this.mobConfig.getSpawnRadius();
    }

    public boolean isInRange(Location playerLocation) {
        if (!playerLocation.getWorld().equals((Object)this.location.getWorld())) {
            return false;
        }
        return playerLocation.distance(this.location) <= this.getSpawnRadius();
    }
}

