/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.command.CommandSender
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.gui.DungeonManagementGUI;
import com.apexdungeons.gui.DungeonOptionsGUI;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class DungeonDeleteConfirmGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_RED) + "\u26a0 Confirm Deletion";

    public static void open(Player player, ApexDungeons plugin, DungeonInstance dungeon) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)27, (String)GUI_NAME);
        DungeonDeleteConfirmGUI.fillBackground(inv);
        DungeonDeleteConfirmGUI.createWarningDisplay(inv, dungeon);
        DungeonDeleteConfirmGUI.createConfirmationButtons(inv, plugin, dungeon);
        player.openInventory(inv);
        DungeonDeleteConfirmGUI.registerEventListener(plugin, dungeon);
    }

    private static void fillBackground(Inventory inv) {
        ItemStack warning = new ItemStack(Material.RED_STAINED_GLASS_PANE);
        ItemMeta warningMeta = warning.getItemMeta();
        warningMeta.setDisplayName(" ");
        warning.setItemMeta(warningMeta);
        ItemStack danger = new ItemStack(Material.ORANGE_STAINED_GLASS_PANE);
        ItemMeta dangerMeta = danger.getItemMeta();
        dangerMeta.setDisplayName(" ");
        danger.setItemMeta(dangerMeta);
        for (int i = 0; i < 27; ++i) {
            if (i % 2 == 0) {
                inv.setItem(i, warning);
                continue;
            }
            inv.setItem(i, danger);
        }
    }

    private static void createWarningDisplay(Inventory inv, DungeonInstance dungeon) {
        ItemStack warning = new ItemStack(Material.BARRIER);
        ItemMeta warningMeta = warning.getItemMeta();
        warningMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u26a0 DELETION WARNING \u26a0");
        ArrayList<Object> warningLore = new ArrayList<Object>();
        warningLore.add(String.valueOf(ChatColor.GRAY) + "\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501");
        warningLore.add(String.valueOf(ChatColor.RED) + "You are about to PERMANENTLY DELETE:");
        warningLore.add("");
        warningLore.add(String.valueOf(ChatColor.YELLOW) + "Dungeon: " + String.valueOf(ChatColor.WHITE) + dungeon.getDisplayName());
        warningLore.add(String.valueOf(ChatColor.YELLOW) + "Creator: " + String.valueOf(ChatColor.WHITE) + dungeon.getCreator());
        warningLore.add(String.valueOf(ChatColor.YELLOW) + "World: " + String.valueOf(ChatColor.WHITE) + dungeon.getWorld().getName());
        warningLore.add("");
        warningLore.add(String.valueOf(ChatColor.RED) + "THIS ACTION WILL:");
        warningLore.add(String.valueOf(ChatColor.DARK_RED) + "\u2022 Delete the entire dungeon world");
        warningLore.add(String.valueOf(ChatColor.DARK_RED) + "\u2022 Remove all portals");
        warningLore.add(String.valueOf(ChatColor.DARK_RED) + "\u2022 Kick all current players");
        warningLore.add(String.valueOf(ChatColor.DARK_RED) + "\u2022 Permanently destroy all data");
        warningLore.add("");
        warningLore.add(String.valueOf(ChatColor.RED) + "\u26a0 THIS CANNOT BE UNDONE! \u26a0");
        warningLore.add(String.valueOf(ChatColor.GRAY) + "\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501");
        warningMeta.setLore(warningLore);
        warning.setItemMeta(warningMeta);
        inv.setItem(13, warning);
        if (!dungeon.getPlayers().isEmpty()) {
            ItemStack playerWarning = new ItemStack(Material.PLAYER_HEAD);
            ItemMeta playerMeta = playerWarning.getItemMeta();
            playerMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\u26a0 Active Players Warning");
            ArrayList<Object> playerLore = new ArrayList<Object>();
            playerLore.add(String.valueOf(ChatColor.GRAY) + "There are currently players in this dungeon!");
            playerLore.add("");
            playerLore.add(String.valueOf(ChatColor.YELLOW) + "Active Players: " + String.valueOf(ChatColor.WHITE) + dungeon.getPlayers().size());
            playerLore.add("");
            playerLore.add(String.valueOf(ChatColor.RED) + "Deleting will immediately kick all players");
            playerLore.add(String.valueOf(ChatColor.RED) + "and teleport them to the main world spawn.");
            playerMeta.setLore(playerLore);
            playerWarning.setItemMeta(playerMeta);
            inv.setItem(4, playerWarning);
        }
    }

    private static void createConfirmationButtons(Inventory inv, ApexDungeons plugin, DungeonInstance dungeon) {
        ItemStack cancel = new ItemStack(Material.LIME_DYE);
        ItemMeta cancelMeta = cancel.getItemMeta();
        cancelMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u2713 CANCEL - Keep Dungeon");
        ArrayList<Object> cancelLore = new ArrayList<Object>();
        cancelLore.add(String.valueOf(ChatColor.GRAY) + "Return to dungeon options");
        cancelLore.add(String.valueOf(ChatColor.GRAY) + "without deleting anything");
        cancelLore.add("");
        cancelLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to cancel deletion");
        cancelMeta.setLore(cancelLore);
        cancel.setItemMeta(cancelMeta);
        inv.setItem(10, cancel);
        ItemStack confirm = new ItemStack(Material.TNT);
        ItemMeta confirmMeta = confirm.getItemMeta();
        confirmMeta.setDisplayName(String.valueOf(ChatColor.DARK_RED) + "\ud83d\udca5 CONFIRM DELETE");
        ArrayList<Object> confirmLore = new ArrayList<Object>();
        confirmLore.add(String.valueOf(ChatColor.RED) + "PERMANENTLY DELETE this dungeon");
        confirmLore.add("");
        confirmLore.add(String.valueOf(ChatColor.DARK_RED) + "\u26a0 FINAL WARNING \u26a0");
        confirmLore.add(String.valueOf(ChatColor.RED) + "This action is IRREVERSIBLE!");
        confirmLore.add(String.valueOf(ChatColor.RED) + "All data will be LOST FOREVER!");
        confirmLore.add("");
        confirmLore.add(String.valueOf(ChatColor.YELLOW) + "Only click if you are absolutely sure!");
        confirmLore.add("");
        confirmLore.add(String.valueOf(ChatColor.DARK_RED) + "\u25b6 Click to DELETE PERMANENTLY");
        confirmMeta.setLore(confirmLore);
        confirm.setItemMeta(confirmMeta);
        inv.setItem(16, confirm);
        ItemStack info = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2139 Deletion Information");
        ArrayList<Object> infoLore = new ArrayList<Object>();
        infoLore.add(String.valueOf(ChatColor.GRAY) + "What happens when you delete:");
        infoLore.add("");
        infoLore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "All players are safely teleported out");
        infoLore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Portal connections are removed");
        infoLore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "World files are deleted from disk");
        infoLore.add(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Dungeon is removed from memory");
        infoLore.add("");
        infoLore.add(String.valueOf(ChatColor.GRAY) + "The deletion process is safe and will");
        infoLore.add(String.valueOf(ChatColor.GRAY) + "not affect other dungeons or the");
        infoLore.add(String.valueOf(ChatColor.GRAY) + "main server world.");
        infoMeta.setLore(infoLore);
        info.setItemMeta(infoMeta);
        inv.setItem(22, info);
    }

    private static void registerEventListener(final ApexDungeons plugin, final DungeonInstance dungeon) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 10: {
                            clicker.closeInventory();
                            DungeonOptionsGUI.open(clicker, plugin, dungeon);
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Deletion cancelled. Dungeon is safe!");
                            break;
                        }
                        case 16: {
                            clicker.closeInventory();
                            String dungeonName = dungeon.getName();
                            String displayName = dungeon.getDisplayName();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Deleting dungeon '" + displayName + "'...");
                            plugin.getDungeonManager().removeDungeon(dungeonName, (CommandSender)clicker);
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Dungeon '" + displayName + "' has been permanently deleted.");
                            clicker.sendMessage(String.valueOf(ChatColor.GRAY) + "All players have been safely teleported out.");
                            DungeonManagementGUI.open(clicker, plugin);
                            break;
                        }
                    }
                }
            }
        }, (Plugin)plugin);
    }
}

