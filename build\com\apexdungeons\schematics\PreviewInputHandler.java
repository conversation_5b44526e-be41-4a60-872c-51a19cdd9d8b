/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.player.AsyncPlayerChatEvent
 *  org.bukkit.event.player.PlayerQuitEvent
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.schematics;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.SchematicConfirmationGUI;
import com.apexdungeons.schematics.SchematicData;
import com.apexdungeons.schematics.SchematicPreview;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.plugin.Plugin;

public class PreviewInputHandler
implements Listener {
    private final ApexDungeons plugin;
    private final Map<UUID, SchematicPreview> activePreviews = new HashMap<UUID, SchematicPreview>();
    private static final Map<String, String> CONTROL_MAPPINGS = new HashMap<String, String>();

    public PreviewInputHandler(ApexDungeons plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public void registerPreview(Player player, SchematicPreview preview) {
        this.activePreviews.put(player.getUniqueId(), preview);
        this.showControlInstructions(player);
    }

    public void unregisterPreview(Player player) {
        this.activePreviews.remove(player.getUniqueId());
    }

    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        SchematicPreview preview = this.activePreviews.get(playerId);
        if (preview == null || !preview.isActive()) {
            return;
        }
        String message = event.getMessage().toLowerCase().trim();
        String action = CONTROL_MAPPINGS.get(message);
        if (action != null) {
            event.setCancelled(true);
            this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> this.handlePreviewControl(player, preview, action));
        }
    }

    private void handlePreviewControl(Player player, SchematicPreview preview, String action) {
        if (!preview.isActive()) {
            return;
        }
        switch (action) {
            case "north": 
            case "south": 
            case "east": 
            case "west": 
            case "up": 
            case "down": {
                preview.move(action);
                break;
            }
            case "rotate": 
            case "rotate_right": {
                preview.rotate();
                break;
            }
            case "rotate_left": {
                preview.rotate();
                preview.rotate();
                preview.rotate();
                break;
            }
            case "confirm": {
                this.confirmPlacement(player, preview);
                break;
            }
            case "cancel": {
                this.cancelPreview(player, preview);
            }
        }
    }

    private void confirmPlacement(Player player, SchematicPreview preview) {
        if (!preview.isActive()) {
            return;
        }
        String schematicName = preview.getSchematic().getName();
        int blockCount = this.countNonAirBlocks(preview.getSchematic());
        preview.stopPreview();
        this.unregisterPreview(player);
        if (blockCount > 200) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Large schematic detected - showing confirmation dialog...");
            SchematicConfirmationGUI.open(player, this.plugin, schematicName, preview.getBaseLocation(), preview.getRotation());
        } else {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "Placing schematic: " + String.valueOf(ChatColor.YELLOW) + schematicName);
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Rotation: " + preview.getRotation() + "\u00b0");
            this.placeRotatedSchematic(player, preview);
        }
    }

    private int countNonAirBlocks(SchematicData schematic) {
        Material[][][] blocks;
        int count = 0;
        Material[][][] materialArray = blocks = schematic.getBlocks();
        int n = materialArray.length;
        for (int i = 0; i < n; ++i) {
            Material[][] layer;
            Material[][] materialArray2 = layer = materialArray[i];
            int n2 = materialArray2.length;
            for (int j = 0; j < n2; ++j) {
                Material[] row;
                for (Material block : row = materialArray2[j]) {
                    if (block == Material.AIR) continue;
                    ++count;
                }
            }
        }
        return count;
    }

    private void cancelPreview(Player player, SchematicPreview preview) {
        preview.stopPreview();
        this.unregisterPreview(player);
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Schematic preview cancelled.");
    }

    private void placeRotatedSchematic(Player player, SchematicPreview preview) {
        this.plugin.getSchematicManager().placeSchematic(preview.getSchematic().getName(), preview.getBaseLocation()).thenAccept(success -> {
            if (success.booleanValue()) {
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Schematic placed successfully!");
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to place schematic!");
            }
        });
    }

    private void showControlInstructions(Player player) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Enhanced Preview Controls ===");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Movement:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "  W/A/S/D" + String.valueOf(ChatColor.WHITE) + " - Move horizontally");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "  Space/Shift" + String.valueOf(ChatColor.WHITE) + " - Move up/down");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Rotation:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "  R" + String.valueOf(ChatColor.WHITE) + " - Rotate 90\u00b0 clockwise");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "  Q/E" + String.valueOf(ChatColor.WHITE) + " - Rotate left/right");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Actions:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "  Enter" + String.valueOf(ChatColor.WHITE) + " - Confirm placement");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "  Escape" + String.valueOf(ChatColor.WHITE) + " - Cancel preview");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Type the control keys in chat to use them!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Or use left-click to confirm, shift+right-click to cancel");
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        UUID playerId = event.getPlayer().getUniqueId();
        SchematicPreview preview = this.activePreviews.remove(playerId);
        if (preview != null) {
            preview.stopPreview();
        }
    }

    public SchematicPreview getActivePreview(Player player) {
        return this.activePreviews.get(player.getUniqueId());
    }

    public boolean hasActivePreview(Player player) {
        SchematicPreview preview = this.activePreviews.get(player.getUniqueId());
        return preview != null && preview.isActive();
    }

    public Map<UUID, SchematicPreview> getActivePreviews() {
        return new HashMap<UUID, SchematicPreview>(this.activePreviews);
    }

    public void shutdown() {
        for (SchematicPreview preview : this.activePreviews.values()) {
            preview.stopPreview();
        }
        this.activePreviews.clear();
        this.plugin.getLogger().info("PreviewInputHandler shutdown complete.");
    }

    static {
        CONTROL_MAPPINGS.put("w", "north");
        CONTROL_MAPPINGS.put("a", "west");
        CONTROL_MAPPINGS.put("s", "south");
        CONTROL_MAPPINGS.put("d", "east");
        CONTROL_MAPPINGS.put("space", "up");
        CONTROL_MAPPINGS.put("shift", "down");
        CONTROL_MAPPINGS.put("r", "rotate");
        CONTROL_MAPPINGS.put("q", "rotate_left");
        CONTROL_MAPPINGS.put("e", "rotate_right");
        CONTROL_MAPPINGS.put("enter", "confirm");
        CONTROL_MAPPINGS.put("escape", "cancel");
        CONTROL_MAPPINGS.put("esc", "cancel");
    }
}

