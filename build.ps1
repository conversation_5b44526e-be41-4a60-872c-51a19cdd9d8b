# Build script for ApexDungeons
Write-Host "Building ApexDungeons with modifications..." -ForegroundColor Green

# Create build directory
if (Test-Path "build") {
    Remove-Item -Recurse -Force "build"
}
New-Item -ItemType Directory -Path "build" | Out-Null

# Copy source files
Write-Host "Copying source files..." -ForegroundColor Yellow
Copy-Item -Recurse "src\*" "build\"

# Copy configuration files
Write-Host "Copying configuration files..." -ForegroundColor Yellow
Copy-Item "plugin.yml" "build\"
Copy-Item "config.yml" "build\"
Copy-Item "mobs.yml" "build\"
Copy-Item "bosses.yml" "build\"
Copy-Item "messages.yml" "build\"
Copy-Item "loot.yml" "build\"
Copy-Item -Recurse "presets" "build\"
Copy-Item -Recurse "rooms" "build\"
Copy-Item "mythic_integration.yml" "build\"
Copy-Item "instances.yml" "build\"

# Create manifest
$manifest = @"
Manifest-Version: 1.0
Main-Class: com.apexdungeons.ApexDungeons
"@
$manifest | Out-File -FilePath "build\META-INF\MANIFEST.MF" -Encoding ASCII

Write-Host "Attempting to compile Java files..." -ForegroundColor Yellow

# Try to find Bukkit/Spigot JAR for compilation
$bukkitJar = ""
$possiblePaths = @(
    "C:\Users\<USER>\Desktop\server\spigot*.jar",
    "C:\Users\<USER>\Downloads\spigot*.jar",
    ".\spigot*.jar",
    ".\bukkit*.jar"
)

foreach ($path in $possiblePaths) {
    $found = Get-ChildItem $path -ErrorAction SilentlyContinue | Select-Object -First 1
    if ($found) {
        $bukkitJar = $found.FullName
        break
    }
}

if ($bukkitJar) {
    Write-Host "Found Bukkit/Spigot JAR: $bukkitJar" -ForegroundColor Green
    
    # Compile Java files
    $javaFiles = Get-ChildItem -Recurse "build\com\apexdungeons\*.java" | ForEach-Object { $_.FullName }
    
    if ($javaFiles.Count -gt 0) {
        Write-Host "Compiling $($javaFiles.Count) Java files..." -ForegroundColor Yellow
        
        $compileCommand = "javac -cp `"$bukkitJar`" -d build " + ($javaFiles -join " ")
        
        try {
            Invoke-Expression $compileCommand
            Write-Host "Compilation successful!" -ForegroundColor Green
            
            # Remove .java files from build directory
            Get-ChildItem -Recurse "build\*.java" | Remove-Item
            
        } catch {
            Write-Host "Compilation failed: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Proceeding with existing class files..." -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "Bukkit/Spigot JAR not found. Using existing class files..." -ForegroundColor Yellow
    # Copy existing class files
    Copy-Item -Recurse "com\*" "build\" -Force
}

# Create JAR file
Write-Host "Creating JAR file..." -ForegroundColor Yellow
Set-Location "build"

try {
    $jarCommand = "jar -cfm ..\ApexDungeons-Modified.jar META-INF\MANIFEST.MF ."
    Invoke-Expression $jarCommand
    Write-Host "JAR created successfully: ApexDungeons-Modified.jar" -ForegroundColor Green
} catch {
    Write-Host "JAR creation failed: $($_.Exception.Message)" -ForegroundColor Red
}

Set-Location ".."

Write-Host "Build process completed!" -ForegroundColor Green
Write-Host "Modified plugin: ApexDungeons-Modified.jar" -ForegroundColor Cyan
