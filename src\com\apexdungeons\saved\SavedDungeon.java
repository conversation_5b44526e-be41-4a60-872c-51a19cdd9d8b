/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.Location
 *  org.bukkit.World
 *  org.bukkit.configuration.ConfigurationSection
 */
package com.apexdungeons.saved;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.ConfigurationSection;

public class SavedDungeon {
    private String name;
    private String originalName;
    private String description;
    private String creator;
    private long createdTime;
    private Location spawnLocation;
    private Location exitLocation;
    private List<Location> startBlocks = new ArrayList<Location>();
    private List<Location> endBlocks = new ArrayList<Location>();
    private List<Location> mobSpawns = new ArrayList<Location>();
    private List<Location> bossSpawns = new ArrayList<Location>();
    private List<Location> chestSpawns = new ArrayList<Location>();
    private Map<String, Object> settings = new HashMap<String, Object>();

    public SavedDungeon() {
        this.createdTime = System.currentTimeMillis();
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOriginalName() {
        return this.originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public long getCreatedTime() {
        return this.createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    public Location getSpawnLocation() {
        return this.spawnLocation;
    }

    public void setSpawnLocation(Location spawnLocation) {
        this.spawnLocation = spawnLocation;
    }

    public Location getExitLocation() {
        return this.exitLocation;
    }

    public void setExitLocation(Location exitLocation) {
        this.exitLocation = exitLocation;
    }

    public List<Location> getStartBlocks() {
        return this.startBlocks;
    }

    public void setStartBlocks(List<Location> startBlocks) {
        this.startBlocks = startBlocks;
    }

    public List<Location> getEndBlocks() {
        return this.endBlocks;
    }

    public void setEndBlocks(List<Location> endBlocks) {
        this.endBlocks = endBlocks;
    }

    public List<Location> getMobSpawns() {
        return this.mobSpawns;
    }

    public void setMobSpawns(List<Location> mobSpawns) {
        this.mobSpawns = mobSpawns;
    }

    public List<Location> getBossSpawns() {
        return this.bossSpawns;
    }

    public void setBossSpawns(List<Location> bossSpawns) {
        this.bossSpawns = bossSpawns;
    }

    public List<Location> getChestSpawns() {
        return this.chestSpawns;
    }

    public void setChestSpawns(List<Location> chestSpawns) {
        this.chestSpawns = chestSpawns;
    }

    public Map<String, Object> getSettings() {
        return this.settings;
    }

    public void setSettings(Map<String, Object> settings) {
        this.settings = settings;
    }

    public void saveToConfig(ConfigurationSection config) {
        config.set("name", (Object)this.name);
        config.set("original_name", (Object)this.originalName);
        config.set("description", (Object)this.description);
        config.set("creator", (Object)this.creator);
        config.set("created_time", (Object)this.createdTime);
        if (this.spawnLocation != null) {
            this.saveLocationToConfig(config, "spawn_location", this.spawnLocation);
        }
        if (this.exitLocation != null) {
            this.saveLocationToConfig(config, "exit_location", this.exitLocation);
        }
        ArrayList<Map<String, Object>> startBlocksList = new ArrayList<Map<String, Object>>();
        for (Location location : this.startBlocks) {
            startBlocksList.add(this.locationToMap(location));
        }
        config.set("start_blocks", startBlocksList);
        ArrayList<Map<String, Object>> endBlocksList = new ArrayList<Map<String, Object>>();
        for (Location location : this.endBlocks) {
            endBlocksList.add(this.locationToMap(location));
        }
        config.set("end_blocks", endBlocksList);
        ArrayList<Map<String, Object>> arrayList = new ArrayList<Map<String, Object>>();
        for (Location location : this.mobSpawns) {
            arrayList.add(this.locationToMap(location));
        }
        config.set("mob_spawns", arrayList);
        ArrayList<Map<String, Object>> arrayList2 = new ArrayList<Map<String, Object>>();
        for (Location loc : this.bossSpawns) {
            arrayList2.add(this.locationToMap(loc));
        }
        config.set("boss_spawns", arrayList2);
        ArrayList<Map<String, Object>> arrayList3 = new ArrayList<Map<String, Object>>();
        for (Location location : this.chestSpawns) {
            arrayList3.add(this.locationToMap(location));
        }
        config.set("chest_spawns", arrayList3);
        for (Map.Entry entry : this.settings.entrySet()) {
            config.set("settings." + (String)entry.getKey(), entry.getValue());
        }
    }

    public static SavedDungeon fromConfig(ConfigurationSection config) {
        Location loc;
        SavedDungeon savedDungeon = new SavedDungeon();
        savedDungeon.name = config.getString("name", "Unknown");
        savedDungeon.originalName = config.getString("original_name", "");
        savedDungeon.description = config.getString("description", "");
        savedDungeon.creator = config.getString("creator", "");
        savedDungeon.createdTime = config.getLong("created_time", System.currentTimeMillis());
        if (config.contains("spawn_location")) {
            savedDungeon.spawnLocation = SavedDungeon.loadLocationFromConfig(config, "spawn_location");
        }
        if (config.contains("exit_location")) {
            savedDungeon.exitLocation = SavedDungeon.loadLocationFromConfig(config, "exit_location");
        }
        if (config.contains("start_blocks")) {
            List startBlocksList = config.getMapList("start_blocks");
            for (Map locMap : startBlocksList) {
                loc = SavedDungeon.mapToLocation(locMap);
                if (loc == null) continue;
                savedDungeon.startBlocks.add(loc);
            }
        }
        if (config.contains("end_blocks")) {
            List endBlocksList = config.getMapList("end_blocks");
            for (Map locMap : endBlocksList) {
                loc = SavedDungeon.mapToLocation(locMap);
                if (loc == null) continue;
                savedDungeon.endBlocks.add(loc);
            }
        }
        if (config.contains("mob_spawns")) {
            List mobSpawnsList = config.getMapList("mob_spawns");
            for (Map locMap : mobSpawnsList) {
                loc = SavedDungeon.mapToLocation(locMap);
                if (loc == null) continue;
                savedDungeon.mobSpawns.add(loc);
            }
        }
        if (config.contains("boss_spawns")) {
            List bossSpawnsList = config.getMapList("boss_spawns");
            for (Map locMap : bossSpawnsList) {
                loc = SavedDungeon.mapToLocation(locMap);
                if (loc == null) continue;
                savedDungeon.bossSpawns.add(loc);
            }
        }
        if (config.contains("chest_spawns")) {
            List chestSpawnsList = config.getMapList("chest_spawns");
            for (Map locMap : chestSpawnsList) {
                loc = SavedDungeon.mapToLocation(locMap);
                if (loc == null) continue;
                savedDungeon.chestSpawns.add(loc);
            }
        }
        if (config.contains("settings")) {
            ConfigurationSection settingsSection = config.getConfigurationSection("settings");
            for (String key : settingsSection.getKeys(false)) {
                savedDungeon.settings.put(key, settingsSection.get(key));
            }
        }
        return savedDungeon;
    }

    private void saveLocationToConfig(ConfigurationSection config, String path, Location location) {
        config.set(path + ".world", (Object)location.getWorld().getName());
        config.set(path + ".x", (Object)location.getX());
        config.set(path + ".y", (Object)location.getY());
        config.set(path + ".z", (Object)location.getZ());
        config.set(path + ".yaw", (Object)Float.valueOf(location.getYaw()));
        config.set(path + ".pitch", (Object)Float.valueOf(location.getPitch()));
    }

    private static Location loadLocationFromConfig(ConfigurationSection config, String path) {
        String worldName = config.getString(path + ".world");
        if (worldName == null) {
            return null;
        }
        World world = Bukkit.getWorld((String)worldName);
        if (world == null) {
            return null;
        }
        double x = config.getDouble(path + ".x");
        double y = config.getDouble(path + ".y");
        double z = config.getDouble(path + ".z");
        float yaw = (float)config.getDouble(path + ".yaw");
        float pitch = (float)config.getDouble(path + ".pitch");
        return new Location(world, x, y, z, yaw, pitch);
    }

    private Map<String, Object> locationToMap(Location location) {
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("world", location.getWorld().getName());
        map.put("x", location.getX());
        map.put("y", location.getY());
        map.put("z", location.getZ());
        map.put("yaw", Float.valueOf(location.getYaw()));
        map.put("pitch", Float.valueOf(location.getPitch()));
        return map;
    }

    private static Location mapToLocation(Map<?, ?> map) {
        try {
            String worldName = (String)map.get("world");
            World world = Bukkit.getWorld((String)worldName);
            if (world == null) {
                return null;
            }
            double x = ((Number)map.get("x")).doubleValue();
            double y = ((Number)map.get("y")).doubleValue();
            double z = ((Number)map.get("z")).doubleValue();
            float yaw = ((Number)map.get("yaw")).floatValue();
            float pitch = ((Number)map.get("pitch")).floatValue();
            return new Location(world, x, y, z, yaw, pitch);
        }
        catch (Exception e) {
            return null;
        }
    }
}

