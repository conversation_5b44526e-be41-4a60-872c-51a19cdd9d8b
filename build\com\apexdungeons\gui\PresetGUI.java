/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonPreset;
import com.apexdungeons.gui.NamingGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class PresetGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_GRAY) + "Select Preset";

    public static void open(Player player, final ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)27, (String)GUI_NAME);
        int index = 10;
        // Simplified preset selection - removed themes, focus on building
        for (String presetName : new String[]{"small", "medium", "large", "building"}) {
            ItemStack paper = new ItemStack(Material.PAPER);
            ItemMeta meta = paper.getItemMeta();
            meta.setDisplayName(String.valueOf(ChatColor.GOLD) + presetName.substring(0, 1).toUpperCase() + presetName.substring(1) + " Preset");
            ArrayList<CallSite> lore = new ArrayList<CallSite>();
            DungeonPreset preset = plugin.getDungeonManager().loadPreset(presetName);
            if (preset != null) {
                lore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Max Rooms: " + preset.getMaxRooms())));
                lore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Branch Chance: " + preset.getBranchChance())));
                // Removed theme display - focus on building capabilities
                lore.add((CallSite)((Object)(String.valueOf(ChatColor.GREEN) + "✓ Building Focused")));
                lore.add((CallSite)((Object)(String.valueOf(ChatColor.AQUA) + "✓ Instance Isolated")));
            }
            meta.setLore(lore);
            paper.setItemMeta(meta);
            inv.setItem(index, paper);
            index += 2;
        }
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot == 10 || slot == 12 || slot == 14 || slot == 16) {
                        String presetName = switch (slot) {
                            case 10 -> "small";
                            case 12 -> "medium";
                            case 14 -> "large";
                            default -> "building";
                        };
                        e.getWhoClicked().closeInventory();
                        NamingGUI.open((Player)e.getWhoClicked(), plugin, presetName);
                    }
                }
            }
        }, (Plugin)pl);
        player.openInventory(inv);
    }
}

