/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.World
 *  org.bukkit.WorldCreator
 *  org.bukkit.entity.Player
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.instance;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.instance.DungeonInstanceData;
import java.io.File;
import java.io.IOException;
import java.nio.file.FileVisitOption;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.WorldCreator;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

public class DungeonInstanceManager {
    private final ApexDungeons plugin;
    private final Map<String, List<DungeonInstanceData>> activeInstances = new HashMap<String, List<DungeonInstanceData>>();
    private final Map<UUID, DungeonInstanceData> playerInstances = new HashMap<UUID, DungeonInstanceData>();
    private final Map<String, Integer> instanceCounters = new HashMap<String, Integer>();

    public DungeonInstanceManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    public CompletableFuture<DungeonInstanceData> createInstance(String templateName, List<Player> team) {
        CompletableFuture<DungeonInstanceData> future = new CompletableFuture<DungeonInstanceData>();

        // Check if any team member already has an instance
        for (Player player : team) {
            DungeonInstanceData existingInstance = this.getPlayerInstance(player);
            if (existingInstance != null) {
                this.plugin.getLogger().info("Player " + player.getName() + " already has instance: " + existingInstance.getInstanceId());
                // If in party, share the instance
                if (this.plugin.getPartyManager() != null && this.plugin.getPartyManager().areInSameParty(team.get(0), player)) {
                    future.complete(existingInstance);
                    return future;
                }
            }
        }

        int instanceNumber = this.instanceCounters.getOrDefault(templateName, 0) + 1;
        this.instanceCounters.put(templateName, instanceNumber);
        String instanceId = templateName + "_instance_" + instanceNumber + "_" + System.currentTimeMillis();
        this.plugin.getLogger().info("Creating NEW dungeon instance: " + instanceId + " for " + team.size() + " players");

        this.plugin.getServer().getScheduler().runTaskAsynchronously((Plugin)this.plugin, () -> {
            try {
                World templateWorld = this.getTemplateWorld(templateName);
                if (templateWorld == null) {
                    future.complete(null);
                    return;
                }
                World instanceWorld = this.createInstanceWorld(instanceId, templateWorld);
                if (instanceWorld == null) {
                    future.complete(null);
                    return;
                }
                DungeonInstanceData instanceData = new DungeonInstanceData(instanceId, templateName, instanceWorld, team, System.currentTimeMillis());
                this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> {
                    this.registerInstance(instanceData);
                    future.complete(instanceData);
                });
            }
            catch (Exception e) {
                this.plugin.getLogger().severe("Failed to create dungeon instance: " + e.getMessage());
                e.printStackTrace();
                future.complete(null);
            }
        });
        return future;
    }

    private World getTemplateWorld(String templateName) {
        World world = this.plugin.getWorldManager().getDungeonWorld(templateName);
        if (world != null) {
            return world;
        }
        for (World w : Bukkit.getWorlds()) {
            if (!w.getName().contains(templateName)) continue;
            return w;
        }
        this.plugin.getLogger().warning("Template world not found for: " + templateName);
        return null;
    }

    private World createInstanceWorld(String instanceId, World templateWorld) {
        try {
            String templateWorldName = templateWorld.getName();
            String instanceWorldName = "instance_" + instanceId;
            File templateFolder = templateWorld.getWorldFolder();
            File instanceFolder = new File(Bukkit.getWorldContainer(), instanceWorldName);
            this.plugin.getLogger().info("Copying world from " + templateFolder.getName() + " to " + instanceFolder.getName());
            this.copyWorldFolder(templateFolder.toPath(), instanceFolder.toPath());
            CompletableFuture worldFuture = new CompletableFuture();
            this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> {
                try {
                    WorldCreator creator = new WorldCreator(instanceWorldName);
                    creator.environment(templateWorld.getEnvironment());
                    creator.type(templateWorld.getWorldType());
                    creator.generateStructures(false);
                    World instanceWorld = creator.createWorld();
                    if (instanceWorld != null) {
                        instanceWorld.setDifficulty(templateWorld.getDifficulty());
                        instanceWorld.setSpawnFlags(false, false);
                        instanceWorld.setKeepSpawnInMemory(false);
                        this.plugin.getLogger().info("Created instance world: " + instanceWorldName);
                    }
                    worldFuture.complete(instanceWorld);
                }
                catch (Exception e) {
                    this.plugin.getLogger().severe("Failed to create instance world: " + e.getMessage());
                    worldFuture.complete(null);
                }
            });
            return (World)worldFuture.get(30L, TimeUnit.SECONDS);
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("Failed to create instance world: " + e.getMessage());
            return null;
        }
    }

    private void copyWorldFolder(Path source, Path target) throws IOException {
        if (!Files.exists(source, new LinkOption[0])) {
            throw new IOException("Source world folder does not exist: " + String.valueOf(source));
        }
        Files.walk(source, new FileVisitOption[0]).forEach(sourcePath -> {
            try {
                Path targetPath = target.resolve(source.relativize((Path)sourcePath));
                String fileName = sourcePath.getFileName().toString();
                if (fileName.equals("session.lock") || fileName.equals("uid.dat")) {
                    return;
                }
                if (Files.isDirectory(sourcePath, new LinkOption[0])) {
                    Files.createDirectories(targetPath, new FileAttribute[0]);
                } else {
                    Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
                }
            }
            catch (IOException e) {
                this.plugin.getLogger().warning("Failed to copy file: " + String.valueOf(sourcePath) + " - " + e.getMessage());
            }
        });
    }

    private void registerInstance(DungeonInstanceData instanceData) {
        String templateName = instanceData.getTemplateName();
        this.activeInstances.computeIfAbsent(templateName, k -> new ArrayList()).add(instanceData);
        for (Player player : instanceData.getTeam()) {
            this.playerInstances.put(player.getUniqueId(), instanceData);
        }
        this.plugin.getLogger().info("Registered dungeon instance: " + instanceData.getInstanceId() + " (Total instances for " + templateName + ": " + this.activeInstances.get(templateName).size() + ")");
    }

    public DungeonInstanceData getPlayerInstance(Player player) {
        return this.playerInstances.get(player.getUniqueId());
    }

    public DungeonInstanceData getOrCreatePartyInstance(String templateName, Player leader) {
        // Check if leader already has an instance
        DungeonInstanceData existing = this.getPlayerInstance(leader);
        if (existing != null) {
            return existing;
        }

        // Check if any party member has an instance
        if (this.plugin.getPartyManager() != null) {
            for (DungeonInstanceData instance : this.playerInstances.values()) {
                for (Player teamMember : instance.getTeam()) {
                    if (this.plugin.getPartyManager().areInSameParty(leader, teamMember)) {
                        // Add leader to existing party instance
                        instance.addPlayer(leader);
                        this.playerInstances.put(leader.getUniqueId(), instance);
                        return instance;
                    }
                }
            }
        }

        // No existing party instance, create new one
        List<Player> team = new ArrayList<>();
        team.add(leader);
        try {
            return this.createInstance(templateName, team).get();
        } catch (Exception e) {
            this.plugin.getLogger().severe("Failed to create party instance: " + e.getMessage());
            return null;
        }
    }

    public void removePlayerFromInstance(Player player) {
        DungeonInstanceData instance = this.playerInstances.remove(player.getUniqueId());
        if (instance != null) {
            instance.removePlayer(player);
            if (instance.getTeam().isEmpty()) {
                this.cleanupInstance(instance);
            }
        }
    }

    private void cleanupInstance(DungeonInstanceData instanceData) {
        World instanceWorld;
        String templateName = instanceData.getTemplateName();
        String instanceId = instanceData.getInstanceId();
        List<DungeonInstanceData> instances = this.activeInstances.get(templateName);
        if (instances != null) {
            instances.remove(instanceData);
            if (instances.isEmpty()) {
                this.activeInstances.remove(templateName);
            }
        }
        if ((instanceWorld = instanceData.getInstanceWorld()) != null) {
            this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> {
                for (Player player : instanceWorld.getPlayers()) {
                    Location mainSpawn = ((World)Bukkit.getWorlds().get(0)).getSpawnLocation();
                    player.teleport(mainSpawn);
                    player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Instance closed - returned to main world.");
                }
                boolean unloaded = Bukkit.unloadWorld((World)instanceWorld, (boolean)false);
                if (unloaded) {
                    this.plugin.getLogger().info("Unloaded instance world: " + instanceWorld.getName());
                    this.plugin.getServer().getScheduler().runTaskAsynchronously((Plugin)this.plugin, () -> {
                        try {
                            File worldFolder = instanceWorld.getWorldFolder();
                            this.deleteWorldFolder(worldFolder);
                            this.plugin.getLogger().info("Deleted instance world folder: " + worldFolder.getName());
                        }
                        catch (Exception e) {
                            this.plugin.getLogger().warning("Failed to delete instance world folder: " + e.getMessage());
                        }
                    });
                } else {
                    this.plugin.getLogger().warning("Failed to unload instance world: " + instanceWorld.getName());
                }
            });
        }
        this.plugin.getLogger().info("Cleaned up dungeon instance: " + instanceId);
    }

    private void deleteWorldFolder(File folder) throws IOException {
        if (!folder.exists()) {
            return;
        }
        Files.walk(folder.toPath(), new FileVisitOption[0]).sorted(Comparator.reverseOrder()).map(Path::toFile).forEach(File::delete);
    }

    public List<DungeonInstanceData> getActiveInstances(String templateName) {
        return this.activeInstances.getOrDefault(templateName, new ArrayList());
    }

    public int getTotalActiveInstances() {
        return this.activeInstances.values().stream().mapToInt(List::size).sum();
    }

    public void shutdown() {
        this.plugin.getLogger().info("Shutting down " + this.getTotalActiveInstances() + " active instances...");
        for (List<DungeonInstanceData> instances : this.activeInstances.values()) {
            for (DungeonInstanceData instance : new ArrayList<DungeonInstanceData>(instances)) {
                this.cleanupInstance(instance);
            }
        }
        this.activeInstances.clear();
        this.playerInstances.clear();
        this.instanceCounters.clear();
    }
}

