/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.command.CommandSender
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.ClickType
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.gui.AdminGUI;
import com.apexdungeons.gui.DungeonOptionsGUI;
import com.apexdungeons.gui.EnhancedMainGUI;
import com.apexdungeons.gui.HelpGUI;
import com.apexdungeons.gui.PresetGUI;
import java.lang.invoke.CallSite;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class DungeonManagementGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_BLUE) + "\u2694 Dungeon Management";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MMM dd, yyyy HH:mm");

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        DungeonManagementGUI.fillBackground(inv);
        DungeonManagementGUI.createDungeonList(inv, player, plugin);
        DungeonManagementGUI.createManagementButtons(inv, player, plugin);
        DungeonManagementGUI.createNavigationButtons(inv);
        player.openInventory(inv);
        DungeonManagementGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.BLUE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createDungeonList(Inventory inv, Player player, ApexDungeons plugin) {
        Map<String, DungeonInstance> dungeons = plugin.getDungeonManager().getDungeons();
        if (dungeons.isEmpty()) {
            ItemStack noDungeons = new ItemStack(Material.BARRIER);
            ItemMeta noMeta = noDungeons.getItemMeta();
            noMeta.setDisplayName(String.valueOf(ChatColor.RED) + "No Active Dungeons");
            ArrayList<Object> noLore = new ArrayList<Object>();
            noLore.add(String.valueOf(ChatColor.GRAY) + "You haven't created any dungeons yet!");
            noLore.add("");
            noLore.add(String.valueOf(ChatColor.YELLOW) + "Create your first dungeon to");
            noLore.add(String.valueOf(ChatColor.YELLOW) + "start managing your creations.");
            noMeta.setLore(noLore);
            noDungeons.setItemMeta(noMeta);
            inv.setItem(22, noDungeons);
            return;
        }
        int[] dungeonSlots = new int[]{9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44};
        int index = 0;
        for (Map.Entry<String, DungeonInstance> entry : dungeons.entrySet()) {
            if (index >= dungeonSlots.length) break;
            DungeonInstance dungeon = entry.getValue();
            ItemStack dungeonItem = DungeonManagementGUI.createDungeonItem(dungeon, player);
            inv.setItem(dungeonSlots[index], dungeonItem);
            ++index;
        }
    }

    private static ItemStack createDungeonItem(DungeonInstance dungeon, Player player) {
        Material material = dungeon.isGenerating() ? Material.YELLOW_CONCRETE : (dungeon.getCreator().equals(player.getName()) ? Material.GREEN_CONCRETE : Material.LIGHT_BLUE_CONCRETE);
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83c\udff0 " + dungeon.getDisplayName());
        ArrayList<CallSite> lore = new ArrayList<CallSite>();
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501")));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Creator: " + String.valueOf(ChatColor.WHITE) + dungeon.getCreator())));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "World: " + String.valueOf(ChatColor.WHITE) + dungeon.getWorld().getName())));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Rooms: " + String.valueOf(ChatColor.WHITE) + dungeon.getRoomCount())));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Created: " + String.valueOf(ChatColor.WHITE) + DATE_FORMAT.format(new Date(dungeon.getCreationTime())))));
        if (dungeon.isGenerating()) {
            lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Status: " + String.valueOf(ChatColor.GOLD) + "\u26a0 Generating...")));
        } else {
            lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Status: " + String.valueOf(ChatColor.GREEN) + "\u2713 Ready")));
        }
        int playerCount = dungeon.getPlayers().size();
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Players: " + String.valueOf(ChatColor.WHITE) + playerCount + " online")));
        lore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501\u2501")));
        if (dungeon.getCreator().equals(player.getName()) || player.hasPermission("apexdungeons.admin")) {
            lore.add((CallSite)((Object)(String.valueOf(ChatColor.GREEN) + "\u25b6 Left Click: " + String.valueOf(ChatColor.WHITE) + "Teleport")));
            lore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "\u25b6 Right Click: " + String.valueOf(ChatColor.WHITE) + "Management Options")));
            if (player.hasPermission("apexdungeons.admin")) {
                lore.add((CallSite)((Object)(String.valueOf(ChatColor.RED) + "\u25b6 Shift+Right Click: " + String.valueOf(ChatColor.WHITE) + "Force Delete")));
            }
        } else {
            lore.add((CallSite)((Object)(String.valueOf(ChatColor.GREEN) + "\u25b6 Left Click: " + String.valueOf(ChatColor.WHITE) + "Teleport")));
            lore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "\u25b6 Right Click: " + String.valueOf(ChatColor.GRAY) + "View Details")));
        }
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private static void createManagementButtons(Inventory inv, Player player, ApexDungeons plugin) {
        ItemStack create = new ItemStack(Material.NETHER_STAR);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u271a Create New Dungeon");
        ArrayList<CallSite> createLore = new ArrayList<CallSite>();
        createLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Start creating a new dungeon")));
        createLore.add((CallSite)((Object)(String.valueOf(ChatColor.GREEN) + "Click to open preset selection!")));
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(1, create);
        ItemStack refresh = new ItemStack(Material.LIME_DYE);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udd04 Refresh List");
        ArrayList<CallSite> refreshLore = new ArrayList<CallSite>();
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Update the dungeon list")));
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "with the latest information")));
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(3, refresh);
        ItemStack sort = new ItemStack(Material.HOPPER);
        ItemMeta sortMeta = sort.getItemMeta();
        sortMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccb Sort Options");
        ArrayList<Object> sortLore = new ArrayList<Object>();
        sortLore.add(String.valueOf(ChatColor.GRAY) + "Change how dungeons are sorted");
        sortLore.add("");
        sortLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 By Creation Date");
        sortLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 By Name (A-Z)");
        sortLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 By Creator");
        sortLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 By Status");
        sortMeta.setLore(sortLore);
        sort.setItemMeta(sortMeta);
        inv.setItem(5, sort);
        if (player.hasPermission("apexdungeons.admin")) {
            ItemStack admin = new ItemStack(Material.COMMAND_BLOCK);
            ItemMeta adminMeta = admin.getItemMeta();
            adminMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\ud83d\udd27 Admin Tools");
            ArrayList<Object> adminLore = new ArrayList<Object>();
            adminLore.add(String.valueOf(ChatColor.GRAY) + "Administrative functions");
            adminLore.add("");
            adminLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Force delete any dungeon");
            adminLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Teleport to any dungeon");
            adminLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 View detailed statistics");
            adminLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Manage server resources");
            adminMeta.setLore(adminLore);
            admin.setItemMeta(adminMeta);
            inv.setItem(7, admin);
        }
    }

    private static void createNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2190 Back to Main Menu");
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\u2753 Management Help");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.GRAY) + "Learn about dungeon management");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "Tips:");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Left click to teleport");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Right click for options");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Green = Your dungeons");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Blue = Other players'");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Yellow = Generating");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    String displayName;
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 1: {
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                            return;
                        }
                        case 3: {
                            clicker.closeInventory();
                            DungeonManagementGUI.open(clicker, plugin);
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Dungeon list refreshed!");
                            return;
                        }
                        case 5: {
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Sort options coming soon!");
                            DungeonManagementGUI.open(clicker, plugin);
                            return;
                        }
                        case 7: {
                            if (clicker.hasPermission("apexdungeons.admin")) {
                                clicker.closeInventory();
                                AdminGUI.open(clicker, plugin);
                            }
                            return;
                        }
                        case 45: {
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            return;
                        }
                        case 53: {
                            clicker.closeInventory();
                            HelpGUI.open(clicker, plugin);
                            return;
                        }
                    }
                    ItemStack clickedItem = e.getCurrentItem();
                    if (clickedItem != null && clickedItem.hasItemMeta() && clickedItem.getItemMeta().hasDisplayName() && (displayName = clickedItem.getItemMeta().getDisplayName()).contains("\ud83c\udff0")) {
                        String dungeonDisplayName = ChatColor.stripColor((String)displayName).replace("\ud83c\udff0 ", "");
                        DungeonManagementGUI.handleDungeonClick(clicker, plugin, dungeonDisplayName, e.getClick());
                    }
                }
            }
        }, (Plugin)plugin);
    }

    private static void handleDungeonClick(Player player, ApexDungeons plugin, String dungeonDisplayName, ClickType clickType) {
        DungeonInstance dungeon = null;
        for (DungeonInstance d : plugin.getDungeonManager().getDungeons().values()) {
            if (!d.getDisplayName().equals(dungeonDisplayName)) continue;
            dungeon = d;
            break;
        }
        if (dungeon == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon not found!");
            return;
        }
        switch (clickType) {
            case LEFT: {
                if (dungeon.isGenerating()) {
                    player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon is still generating. Please wait...");
                    return;
                }
                Location spawnLoc = plugin.getWorldManager().getDungeonSpawnLocation(dungeon.getName());
                if (spawnLoc != null) {
                    plugin.getEffectsManager().playDungeonEntryEffects(player, dungeon);
                    player.teleport(spawnLoc);
                    dungeon.addPlayer(player);
                    break;
                }
                player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to find dungeon spawn location!");
                break;
            }
            case RIGHT: {
                player.closeInventory();
                DungeonOptionsGUI.open(player, plugin, dungeon);
                break;
            }
            case SHIFT_RIGHT: {
                if (player.hasPermission("apexdungeons.admin")) {
                    player.closeInventory();
                    plugin.getDungeonManager().removeDungeon(dungeon.getName(), (CommandSender)player);
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon '" + dungeon.getDisplayName() + "' has been force deleted!");
                    break;
                }
                player.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to force delete dungeons!");
            }
        }
    }
}

