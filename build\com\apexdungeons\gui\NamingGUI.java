/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.event.player.AsyncPlayerChatEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.MainGUI;
import com.apexdungeons.gui.PresetGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class NamingGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_GRAY) + "Name Your Dungeon";
    private static final Map<UUID, String> awaitingInput = new HashMap<UUID, String>();
    private static final Map<UUID, String> selectedPresets = new HashMap<UUID, String>();

    public static void open(Player player, ApexDungeons plugin, String presetName) {
        selectedPresets.put(player.getUniqueId(), presetName);
        NamingGUI.openGUI(player, plugin);
    }

    public static void open(Player player, ApexDungeons plugin) {
        selectedPresets.put(player.getUniqueId(), "default");
        NamingGUI.openGUI(player, plugin);
    }

    private static void openGUI(Player player, final ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)27, (String)GUI_NAME);
        ItemStack nameInput = new ItemStack(Material.NAME_TAG);
        ItemMeta nameMeta = nameInput.getItemMeta();
        nameMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "Enter Custom Name");
        ArrayList<CallSite> nameLore = new ArrayList<CallSite>();
        nameLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Click to enter a custom name")));
        nameLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "in chat for your dungeon")));
        nameLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Names must be 3-32 characters")));
        nameLore.add((CallSite)((Object)(String.valueOf(ChatColor.YELLOW) + "Letters, numbers, spaces, - and _ only")));
        nameMeta.setLore(nameLore);
        nameInput.setItemMeta(nameMeta);
        inv.setItem(11, nameInput);
        ItemStack autoName = new ItemStack(Material.PAPER);
        ItemMeta autoMeta = autoName.getItemMeta();
        autoMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "Auto-Generate Name");
        ArrayList<CallSite> autoLore = new ArrayList<CallSite>();
        autoLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Let the system create a")));
        autoLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "unique name for your dungeon")));
        autoMeta.setLore(autoLore);
        autoName.setItemMeta(autoMeta);
        inv.setItem(13, autoName);
        String currentPreset = selectedPresets.get(player.getUniqueId());
        ItemStack presetInfo = new ItemStack(Material.BOOK);
        ItemMeta presetMeta = presetInfo.getItemMeta();
        presetMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "Dungeon Type: " + (currentPreset != null ? currentPreset.toUpperCase() : "DEFAULT"));
        ArrayList<CallSite> presetLore = new ArrayList<CallSite>();
        presetLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Creating a flat dungeon world")));
        presetLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "perfect for custom building")));
        presetMeta.setLore(presetLore);
        presetInfo.setItemMeta(presetMeta);
        inv.setItem(15, presetInfo);
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.RED) + "Back");
        back.setItemMeta(backMeta);
        inv.setItem(22, back);
        player.openInventory(inv);
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 11: {
                            clicker.closeInventory();
                            awaitingInput.put(clicker.getUniqueId(), selectedPresets.get(clicker.getUniqueId()));
                            clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Enter your dungeon name in chat:");
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Type 'cancel' to cancel naming");
                            break;
                        }
                        case 13: {
                            clicker.closeInventory();
                            String preset = selectedPresets.get(clicker.getUniqueId());
                            plugin.getDungeonManager().createDungeonWithName(null, preset, clicker);
                            selectedPresets.remove(clicker.getUniqueId());
                            break;
                        }
                        case 15: {
                            clicker.closeInventory();
                            PresetGUI.open(clicker, plugin);
                            selectedPresets.remove(clicker.getUniqueId());
                            break;
                        }
                        case 22: {
                            clicker.closeInventory();
                            MainGUI.open(clicker, plugin);
                            selectedPresets.remove(clicker.getUniqueId());
                        }
                    }
                }
            }

            @EventHandler
            public void onChat(AsyncPlayerChatEvent e) {
                Player player = e.getPlayer();
                if (awaitingInput.containsKey(player.getUniqueId())) {
                    e.setCancelled(true);
                    String message = e.getMessage().trim();
                    String preset = awaitingInput.remove(player.getUniqueId());
                    if (message.equalsIgnoreCase("cancel")) {
                        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon naming cancelled.");
                        Bukkit.getScheduler().runTask((Plugin)plugin, () -> MainGUI.open(player, plugin));
                        return;
                    }
                    Bukkit.getScheduler().runTask((Plugin)plugin, () -> {
                        if (plugin.getDungeonManager().isValidDungeonName(message)) {
                            plugin.getDungeonManager().createDungeonWithName(message, preset, player);
                        } else {
                            player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid name! Please try again or use /dgn to return to the menu.");
                            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Names must be 3-32 characters, alphanumeric with spaces, hyphens, and underscores only.");
                        }
                    });
                }
            }
        }, (Plugin)plugin);
    }

    public static boolean isAwaitingInput(Player player) {
        return awaitingInput.containsKey(player.getUniqueId());
    }

    public static void cancelInput(Player player) {
        awaitingInput.remove(player.getUniqueId());
        selectedPresets.remove(player.getUniqueId());
    }
}

