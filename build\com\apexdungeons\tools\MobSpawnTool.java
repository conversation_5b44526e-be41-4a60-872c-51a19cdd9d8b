/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.entity.Player
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import java.util.ArrayList;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class MobSpawnTool {
    private final ApexDungeons plugin;
    private final NamespacedKey toolKey;

    public MobSpawnTool(ApexDungeons plugin) {
        this.plugin = plugin;
        this.toolKey = new NamespacedKey((Plugin)plugin, "mob_spawn_tool");
    }

    public ItemStack createMobSpawnTool() {
        ItemStack tool = new ItemStack(Material.ZOMBIE_HEAD);
        ItemMeta meta = tool.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.RED) + "Mob Spawn Tool");
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + "Right-click blocks to set mob spawn points");
        lore.add(String.valueOf(ChatColor.GRAY) + "Use /dgn mobspawn set <mob_name> to configure");
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccd How to use:");
        lore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Right-click a block to mark spawn point");
        lore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Use /dgn mobspawn set <mob_name>");
        lore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Players trigger spawns by proximity");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\ud83d\udca1 Supports MythicMobs if installed!");
        meta.setLore(lore);
        meta.getPersistentDataContainer().set(this.toolKey, PersistentDataType.BYTE, (Object)1);
        tool.setItemMeta(meta);
        return tool;
    }

    public ItemStack createBossSpawnTool() {
        ItemStack tool = new ItemStack(Material.WITHER_SKELETON_SKULL);
        ItemMeta meta = tool.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.DARK_RED) + "Boss Spawn Tool");
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + "Right-click blocks to set boss spawn points");
        lore.add(String.valueOf(ChatColor.GRAY) + "Use /dgn bossspawn set <boss_name> to configure");
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccd How to use:");
        lore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Right-click a block to mark boss spawn");
        lore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Use /dgn bossspawn set <boss_name>");
        lore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Players trigger spawns by proximity");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\ud83d\udca1 Supports MythicMobs bosses!");
        meta.setLore(lore);
        meta.getPersistentDataContainer().set(new NamespacedKey((Plugin)this.plugin, "boss_spawn_tool"), PersistentDataType.BYTE, (Object)1);
        tool.setItemMeta(meta);
        return tool;
    }

    public boolean isMobSpawnTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(this.toolKey, PersistentDataType.BYTE);
    }

    public boolean isBossSpawnTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(new NamespacedKey((Plugin)this.plugin, "boss_spawn_tool"), PersistentDataType.BYTE);
    }

    public void giveMobSpawnTool(Player player) {
        player.getInventory().addItem(new ItemStack[]{this.createMobSpawnTool()});
    }

    public void giveBossSpawnTool(Player player) {
        player.getInventory().addItem(new ItemStack[]{this.createBossSpawnTool()});
    }
}

