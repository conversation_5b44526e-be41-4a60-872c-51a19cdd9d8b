<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.apexdungeons</groupId>
    <artifactId>ApexDungeons</artifactId>
    <!-- Keep the version easily discoverable by the plugin at runtime -->
    <version>0.1.0</version>
    <packaging>jar</packaging>

    <name>ApexDungeons</name>
    <description>A procedurally generated dungeon plugin for Spigot/Paper 1.20+</description>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <!-- Spigot/Paper API version to compile against. We use the snapshot for 1.21.4 -->
        <spigot-api.version>1.21.4-R0.1-SNAPSHOT</spigot-api.version>
    </properties>

    <repositories>
        <!-- Spigot snapshots repository. This is required to resolve the Bukkit API for 1.21+. -->
        <repository>
            <id>spigotmc-public</id>
            <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
        </repository>
        <!-- Elmakers mirror hosts snapshots as well. -->
        <repository>
            <id>elmakers-mirror</id>
            <url>https://maven.elmakers.com/repository/</url>
        </repository>
    </repositories>

    <dependencies>
        <!-- Provided scope means the server will provide these classes at runtime. -->
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>${spigot-api.version}</version>
            <scope>provided</scope>
        </dependency>
        <!-- Optional: bStats for metrics. It is shaded in, but the plugin still runs if no internet. -->
        <dependency>
            <groupId>org.bstats</groupId>
            <artifactId>bstats-bukkit</artifactId>
            <version>3.0.2</version>
            <optional>true</optional>
        </dependency>
        <!-- JetBrains annotations for @NotNull and @Nullable -->
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>24.0.1</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- Shade plugin creates a fat jar with relocated dependencies. -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.5.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <!-- Relocate bStats to avoid conflicts with other plugins using the same library. -->
                            <relocations>
                                <relocation>
                                    <pattern>org.bstats</pattern>
                                    <shadedPattern>com.apexdungeons.shaded.bstats</shadedPattern>
                                </relocation>
                            </relocations>
                            <minimizeJar>true</minimizeJar>
                            <filters>
                                <filter>
                                    <artifact>org.spigotmc:spigot-api</artifact>
                                    <excludes>
                                        <exclude>*</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>