/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.World
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.gen;

import com.apexdungeons.ApexDungeons;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;

public class DungeonInstance {
    private final ApexDungeons plugin;
    private final String name;
    private final String displayName;
    private final String creator;
    private final long creationTime;
    private final World world;
    private final Location origin;
    private final int roomCount;
    private boolean generating;
    private final Set<UUID> players = new HashSet<UUID>();

    public DungeonInstance(ApexDungeons plugin, String name, World world, Location origin, int roomCount) {
        this(plugin, name, name, "Unknown", System.currentTimeMillis(), world, origin, roomCount);
    }

    public DungeonInstance(ApexDungeons plugin, String name, String displayName, String creator, long creationTime, World world, Location origin, int roomCount) {
        this.plugin = plugin;
        this.name = name;
        this.displayName = displayName;
        this.creator = creator;
        this.creationTime = creationTime;
        this.world = world;
        this.origin = origin;
        this.roomCount = roomCount;
        this.generating = true;
    }

    public String getName() {
        return this.name;
    }

    public String getDisplayName() {
        return this.displayName;
    }

    public String getCreator() {
        return this.creator;
    }

    public long getCreationTime() {
        return this.creationTime;
    }

    public World getWorld() {
        return this.world;
    }

    public Location getOrigin() {
        return this.origin;
    }

    public int getRoomCount() {
        return this.roomCount;
    }

    public boolean isGenerating() {
        return this.generating;
    }

    public void setGenerating(boolean generating) {
        this.generating = generating;
    }

    public void addPlayer(Player player) {
        this.players.add(player.getUniqueId());
    }

    public void removePlayer(Player player) {
        this.players.remove(player.getUniqueId());
    }

    public boolean containsPlayer(Player player) {
        return this.players.contains(player.getUniqueId());
    }

    public Set<UUID> getPlayers() {
        return new HashSet<UUID>(this.players);
    }

    public void spawnBoss() {
        this.plugin.getLogger().info("Boss spawning not yet implemented for dungeon: " + this.name);
    }
}

