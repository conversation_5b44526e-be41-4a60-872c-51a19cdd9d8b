# Building-Focused Dungeon Preset
# Optimized for custom building without theme restrictions

name: "building"
description: "Custom building preset with no theme restrictions"

# Generation parameters optimized for building
generation:
  min_rooms: 1
  max_rooms: 50
  room_spacing: 5
  corridor_width: 3
  vertical_variation: true
  multi_level: true
  max_levels: 5
  natural_generation: false
  custom_building: true

# All room types available for building
room_types:
  - starter_room
  - corridor
  - boss_room
  - treasure_room
  - mini_boss_room
  - custom_room
  - large_chamber
  - small_chamber
  - junction
  - dead_end
  - secret_room

# No theme restrictions - pure building focus
theme:
  name: "custom"
  allow_any_blocks: true
  no_restrictions: true

# Instance settings for building mode
instance:
  enabled: true
  separate_parties: true
  max_instances_per_player: 3
  building_mode: true
  creative_mode: true

# MythicMobs integration for building
mobs:
  enabled: true
  use_mythic_commands: true
  allow_custom_spawns: true
  building_tools: true

# No default boss - let builders choose
boss:
  enabled: false
  allow_custom: true

# World generation optimized for building
world:
  type: "VOID"
  generate_structures: false
  spawn_height: 100
  allow_flight: true
  building_mode: true
