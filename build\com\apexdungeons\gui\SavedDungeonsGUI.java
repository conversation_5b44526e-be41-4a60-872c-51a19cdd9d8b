/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.EnhancedMainGUI;
import com.apexdungeons.saved.SavedDungeon;
import java.lang.invoke.CallSite;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Map;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class SavedDungeonsGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_PURPLE) + "\ud83d\udcda Saved Dungeons";
    private static boolean listenerRegistered = false;

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory(null, (int)54, (String)GUI_NAME);
        SavedDungeonsGUI.fillBackground(inv);
        SavedDungeonsGUI.addSavedDungeons(inv, plugin);
        SavedDungeonsGUI.addNavigationButtons(inv);
        player.openInventory(inv);
        if (!listenerRegistered) {
            SavedDungeonsGUI.registerEventListener(plugin);
            listenerRegistered = true;
        }
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void addSavedDungeons(Inventory inv, ApexDungeons plugin) {
        Map<String, SavedDungeon> savedDungeons = plugin.getSavedDungeonManager().getSavedDungeons();
        if (savedDungeons.isEmpty()) {
            ItemStack noSaved = new ItemStack(Material.BARRIER);
            ItemMeta noSavedMeta = noSaved.getItemMeta();
            noSavedMeta.setDisplayName(String.valueOf(ChatColor.RED) + "No Saved Dungeons");
            ArrayList<Object> noSavedLore = new ArrayList<Object>();
            noSavedLore.add(String.valueOf(ChatColor.GRAY) + "You haven't saved any dungeons yet.");
            noSavedLore.add("");
            noSavedLore.add(String.valueOf(ChatColor.YELLOW) + "To save a dungeon:");
            noSavedLore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Create or enter a dungeon");
            noSavedLore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Use " + String.valueOf(ChatColor.YELLOW) + "/dgn save <name>");
            noSavedLore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Your dungeon will appear here!");
            noSavedMeta.setLore(noSavedLore);
            noSaved.setItemMeta(noSavedMeta);
            inv.setItem(22, noSaved);
            return;
        }
        int slot = 9;
        SimpleDateFormat dateFormat = new SimpleDateFormat("MMM dd, yyyy");
        for (Map.Entry<String, SavedDungeon> entry : savedDungeons.entrySet()) {
            if (slot >= 45) break;
            SavedDungeon savedDungeon = entry.getValue();
            Material icon = Material.STRUCTURE_BLOCK;
            if (!savedDungeon.getBossSpawns().isEmpty()) {
                icon = Material.WITHER_SKELETON_SKULL;
            } else if (!savedDungeon.getMobSpawns().isEmpty()) {
                icon = Material.ZOMBIE_HEAD;
            } else if (!savedDungeon.getChestSpawns().isEmpty()) {
                icon = Material.CHEST;
            }
            ItemStack dungeonItem = new ItemStack(icon);
            ItemMeta dungeonMeta = dungeonItem.getItemMeta();
            dungeonMeta.setDisplayName(String.valueOf(ChatColor.LIGHT_PURPLE) + "\ud83d\udcda " + savedDungeon.getName());
            ArrayList<Object> lore = new ArrayList<Object>();
            lore.add(String.valueOf(ChatColor.GRAY) + savedDungeon.getDescription());
            lore.add("");
            lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udcca Dungeon Info:");
            lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Creator: " + String.valueOf(ChatColor.GRAY) + savedDungeon.getCreator());
            lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Created: " + String.valueOf(ChatColor.GRAY) + dateFormat.format(new Date(savedDungeon.getCreatedTime())));
            if (savedDungeon.getOriginalName() != null && !savedDungeon.getOriginalName().isEmpty()) {
                lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Original: " + String.valueOf(ChatColor.GRAY) + savedDungeon.getOriginalName());
            }
            lore.add("");
            lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfd7 Structure:");
            lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Start Blocks: " + String.valueOf(ChatColor.GREEN) + savedDungeon.getStartBlocks().size());
            lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "End Blocks: " + String.valueOf(ChatColor.GREEN) + savedDungeon.getEndBlocks().size());
            lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Mob Spawns: " + String.valueOf(ChatColor.GREEN) + savedDungeon.getMobSpawns().size());
            lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Boss Spawns: " + String.valueOf(ChatColor.GREEN) + savedDungeon.getBossSpawns().size());
            lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Chest Spawns: " + String.valueOf(ChatColor.GREEN) + savedDungeon.getChestSpawns().size());
            lore.add("");
            lore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Left-click to load as new dungeon!");
            lore.add(String.valueOf(ChatColor.RED) + "\u25b6 Right-click to delete (Admin only)");
            dungeonMeta.setLore(lore);
            dungeonItem.setItemMeta(dungeonMeta);
            inv.setItem(slot, dungeonItem);
            if (++slot % 9 != 0) continue;
            slot += 2;
        }
    }

    private static void addNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Back to Main Menu");
        ArrayList<CallSite> backLore = new ArrayList<CallSite>();
        backLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Return to the main menu")));
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack refresh = new ItemStack(Material.CLOCK);
        ItemMeta refreshMeta = refresh.getItemMeta();
        refreshMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udd04 Refresh");
        ArrayList<CallSite> refreshLore = new ArrayList<CallSite>();
        refreshLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Refresh the saved dungeons list")));
        refreshMeta.setLore(refreshLore);
        refresh.setItemMeta(refreshMeta);
        inv.setItem(49, refresh);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u2753 Help");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.GRAY) + "Learn about saved dungeons");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "How to use:");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Save: " + String.valueOf(ChatColor.GRAY) + "/dgn save <name>");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Load: " + String.valueOf(ChatColor.GRAY) + "Click a saved dungeon");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Delete: " + String.valueOf(ChatColor.GRAY) + "Right-click (Admin)");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(53, help);
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (!e.getView().getTitle().equals(GUI_NAME) || !(e.getWhoClicked() instanceof Player)) {
                    return;
                }
                e.setCancelled(true);
                Player player = (Player)e.getWhoClicked();
                int slot = e.getRawSlot();
                ItemStack item = e.getCurrentItem();
                if (item == null || item.getType() == Material.AIR) {
                    return;
                }
                switch (slot) {
                    case 45: {
                        player.closeInventory();
                        EnhancedMainGUI.open(player, plugin);
                        break;
                    }
                    case 49: {
                        player.closeInventory();
                        SavedDungeonsGUI.open(player, plugin);
                        break;
                    }
                    case 53: {
                        player.sendMessage("");
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "=== Saved Dungeons Help ===");
                        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Commands:");
                        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "/dgn save <name> " + String.valueOf(ChatColor.GRAY) + "- Save current dungeon");
                        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "/dgn load <saved_name> <new_name> " + String.valueOf(ChatColor.GRAY) + "- Load saved dungeon");
                        player.sendMessage("");
                        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "GUI Actions:");
                        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Left-click " + String.valueOf(ChatColor.GRAY) + "- Load dungeon with auto-generated name");
                        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Right-click " + String.valueOf(ChatColor.GRAY) + "- Delete saved dungeon (Admin only)");
                        break;
                    }
                    default: {
                        String displayName;
                        if (slot < 9 || slot >= 45 || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName() || !(displayName = item.getItemMeta().getDisplayName()).startsWith(String.valueOf(ChatColor.LIGHT_PURPLE) + "\ud83d\udcda ")) break;
                        String savedName = ChatColor.stripColor((String)displayName).substring(2);
                        if (e.isLeftClick()) {
                            SavedDungeonsGUI.handleLoadSavedDungeon(player, savedName, plugin);
                            break;
                        }
                        if (e.isRightClick() && player.hasPermission("apexdungeons.admin")) {
                            SavedDungeonsGUI.handleDeleteSavedDungeon(player, savedName, plugin);
                            break;
                        }
                        if (!e.isRightClick()) break;
                        player.sendMessage(String.valueOf(ChatColor.RED) + "You need admin permission to delete saved dungeons!");
                    }
                }
            }
        }, (Plugin)plugin);
    }

    private static void handleLoadSavedDungeon(Player player, String savedName, ApexDungeons plugin) {
        String newDungeonName = savedName + "_" + System.currentTimeMillis();
        player.closeInventory();
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Loading saved dungeon '" + savedName + "'...");
        boolean success = plugin.getSavedDungeonManager().loadSavedDungeon(savedName, newDungeonName, player);
        if (!success) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to load saved dungeon. It may not exist or there was an error.");
        }
    }

    private static void handleDeleteSavedDungeon(Player player, String savedName, ApexDungeons plugin) {
        boolean success = plugin.getSavedDungeonManager().deleteSavedDungeon(savedName);
        if (success) {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "Successfully deleted saved dungeon '" + savedName + "'!");
            SavedDungeonsGUI.open(player, plugin);
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to delete saved dungeon '" + savedName + "'!");
        }
    }
}

