/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class ActiveGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_GRAY) + "Active Dungeons";

    public static void open(Player player, final ApexDungeons plugin) {
        final ArrayList<DungeonInstance> dungeons = new ArrayList<DungeonInstance>(plugin.getDungeonManager().getActiveDungeons());
        int rows = Math.max(1, (int)Math.ceil((double)dungeons.size() / 9.0));
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)(rows * 9), (String)GUI_NAME);
        for (int i = 0; i < dungeons.size(); ++i) {
            DungeonInstance inst = (DungeonInstance)dungeons.get(i);
            ItemStack map = new ItemStack(Material.FILLED_MAP);
            ItemMeta meta = map.getItemMeta();
            meta.setDisplayName(String.valueOf(ChatColor.AQUA) + inst.getDisplayName());
            ArrayList<Object> lore = new ArrayList<Object>();
            lore.add(String.valueOf(ChatColor.GRAY) + "Creator: " + inst.getCreator());
            lore.add(String.valueOf(ChatColor.GRAY) + "Rooms: " + inst.getRoomCount());
            lore.add(String.valueOf(ChatColor.GRAY) + "World: " + inst.getWorld().getName());
            if (inst.isGenerating()) {
                lore.add(String.valueOf(ChatColor.YELLOW) + "(Generating)");
            } else {
                lore.add(String.valueOf(ChatColor.GREEN) + "(Ready)");
            }
            lore.add("");
            lore.add(String.valueOf(ChatColor.YELLOW) + "Click to teleport");
            meta.setLore(lore);
            map.setItemMeta(meta);
            inv.setItem(i, map);
        }
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    if (slot >= 0 && slot < dungeons.size()) {
                        DungeonInstance inst = (DungeonInstance)dungeons.get(slot);
                        e.getWhoClicked().closeInventory();
                        plugin.getDungeonManager().tpToDungeon(inst.getName(), (Player)e.getWhoClicked());
                    }
                }
            }
        }, (Plugin)pl);
        player.openInventory(inv);
    }
}

