/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.configuration.InvalidConfigurationException
 *  org.bukkit.configuration.file.YamlConfiguration
 */
package com.apexdungeons.gen;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.configuration.InvalidConfigurationException;
import org.bukkit.configuration.file.YamlConfiguration;

public class DungeonPreset {
    private final int maxRooms;
    private final double branchChance;
    private final List<String> themes;
    private final boolean buildingMode;
    private final boolean instanceIsolated;

    public DungeonPreset(int maxRooms, double branchChance, List<String> themes) {
        this.maxRooms = maxRooms;
        this.branchChance = branchChance;
        this.themes = themes != null ? themes : new ArrayList<>();
        this.buildingMode = true; // Default to building mode
        this.instanceIsolated = true; // Default to instance isolation
    }

    public DungeonPreset(int maxRooms, double branchChance, List<String> themes, boolean buildingMode, boolean instanceIsolated) {
        this.maxRooms = maxRooms;
        this.branchChance = branchChance;
        this.themes = themes != null ? themes : new ArrayList<>();
        this.buildingMode = buildingMode;
        this.instanceIsolated = instanceIsolated;
    }

    public int getMaxRooms() {
        return this.maxRooms;
    }

    public double getBranchChance() {
        return this.branchChance;
    }

    public List<String> getThemes() {
        return this.themes;
    }

    public boolean isBuildingMode() {
        return this.buildingMode;
    }

    public boolean isInstanceIsolated() {
        return this.instanceIsolated;
    }

    public static DungeonPreset load(File file) {
        double branchChance;
        int maxRooms;
        if (file == null || !file.exists()) {
            return null;
        }
        YamlConfiguration conf = new YamlConfiguration();
        try {
            conf.load(file);
        }
        catch (IOException | InvalidConfigurationException e) {
            e.printStackTrace();
            return null;
        }
        ArrayList<String> themes = new ArrayList<String>();
        if (conf.isConfigurationSection("generation")) {
            maxRooms = conf.getInt("generation.max_rooms", 15);
            branchChance = 0.3;
        } else {
            maxRooms = conf.getInt("maxRooms", conf.getInt("max_rooms", 10));
            branchChance = conf.getDouble("branchChance", conf.getDouble("branch_chance", 0.2));
        }
        // Simplified theme handling - building mode doesn't require themes
        if (conf.contains("theme.name")) {
            themes.add(conf.getString("theme.name"));
        } else if (conf.contains("themes")) {
            themes.addAll(conf.getStringList("themes"));
        } else {
            // Default to building mode with no theme restrictions
            themes.add("building");
        }

        // Check for building mode and instance settings
        boolean buildingMode = conf.getBoolean("instance.building_mode", true);
        boolean instanceIsolated = conf.getBoolean("instance.enabled", true);

        return new DungeonPreset(maxRooms, branchChance, themes, buildingMode, instanceIsolated);
    }
}

