/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.BuildingToolsGUI;
import com.apexdungeons.mobs.CustomMobConfig;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class CustomMobSelectionGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_RED) + "\u2694 Custom Dungeon Mobs";
    private static final Map<UUID, String> playerSelections = new HashMap<UUID, String>();

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        CustomMobSelectionGUI.fillBackground(inv);
        CustomMobSelectionGUI.createMobItems(inv, plugin);
        CustomMobSelectionGUI.createNavigationItems(inv, plugin);
        player.openInventory(inv);
        CustomMobSelectionGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.RED_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createMobItems(Inventory inv, ApexDungeons plugin) {
        ItemStack header = new ItemStack(Material.ZOMBIE_SPAWN_EGG);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\u2694 Custom Dungeon Mobs");
        ArrayList<Object> headerLore = new ArrayList<Object>();
        headerLore.add(String.valueOf(ChatColor.GRAY) + "Select a custom mob to place spawn points");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.GREEN) + "\u2728 Features:");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Visual indicators during building");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Silent spawning during gameplay");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Custom commands and attributes");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Configurable spawn rates");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.YELLOW) + "Total Custom Mobs: " + String.valueOf(ChatColor.WHITE) + plugin.getCustomMobManager().getCustomMobCount());
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(4, header);
        ArrayList<CustomMobConfig> customMobs = new ArrayList<CustomMobConfig>(plugin.getCustomMobManager().getAllCustomMobs());
        int[] mobSlots = new int[]{10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34};
        for (int i = 0; i < Math.min(customMobs.size(), mobSlots.length); ++i) {
            CustomMobConfig mobConfig = (CustomMobConfig)customMobs.get(i);
            ItemStack mobItem = CustomMobSelectionGUI.createMobItem(mobConfig);
            inv.setItem(mobSlots[i], mobItem);
        }
        if (customMobs.size() > mobSlots.length) {
            ItemStack more = new ItemStack(Material.BOOK);
            ItemMeta moreMeta = more.getItemMeta();
            moreMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\ud83d\udcda More Mobs Available");
            ArrayList<CallSite> moreLore = new ArrayList<CallSite>();
            moreLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "There are " + (customMobs.size() - mobSlots.length) + " more custom mobs")));
            moreLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Use /dgn admin for full management")));
            moreMeta.setLore(moreLore);
            more.setItemMeta(moreMeta);
            inv.setItem(43, more);
        }
    }

    private static ItemStack createMobItem(CustomMobConfig config) {
        ItemStack item = new ItemStack(config.getIcon());
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(config.getDifficultyColor() + config.getDisplayName());
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + config.getDescription());
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "Category: " + config.getCategoryColor() + config.getCategory().substring(0, 1).toUpperCase() + config.getCategory().substring(1));
        lore.add(String.valueOf(ChatColor.YELLOW) + "Difficulty: " + config.getDifficultyColor() + config.getDifficulty().substring(0, 1).toUpperCase() + config.getDifficulty().substring(1));
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\u2699 Spawn Configuration:");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Base Type: " + config.getMobType());
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Spawn Radius: " + config.getSpawnRadius() + " blocks");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Cooldown: " + config.getMinCooldown() + "-" + config.getMaxCooldown() + " seconds");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Max Concurrent: " + config.getMaxConcurrent());
        if (config.isBoss()) {
            lore.add(String.valueOf(ChatColor.DARK_RED) + "\u2022 " + String.valueOf(ChatColor.RED) + "BOSS LEVEL ENEMY");
        }
        lore.add("");
        if (!config.getBuilderInfo().isEmpty()) {
            lore.add(String.valueOf(ChatColor.GREEN) + "\ud83d\udca1 Builder Info:");
            for (String info : config.getBuilderInfo()) {
                lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + info);
            }
            lore.add("");
        }
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfaf Click to select this mob!");
        lore.add(String.valueOf(ChatColor.GRAY) + "Then use mob spawn tools to place spawn points");
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    private static void createNavigationItems(Inventory inv, ApexDungeons plugin) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Back to Building Tools");
        ArrayList<CallSite> backLore = new ArrayList<CallSite>();
        backLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Return to the building tools menu")));
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udcd6 Custom Mob Help");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.GRAY) + "Learn about custom mob system");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.GREEN) + "How it works:");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Select a custom mob here");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Use mob spawn tools to place points");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "During building: see visual indicators");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "During gameplay: silent spawning");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 Custom mobs use advanced commands");
        helpLore.add(String.valueOf(ChatColor.GRAY) + "for enhanced attributes and abilities!");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(49, help);
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2716 Close Menu");
        ArrayList<CallSite> closeLore = new ArrayList<CallSite>();
        closeLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Close this interface")));
        closeMeta.setLore(closeLore);
        close.setItemMeta(closeMeta);
        inv.setItem(53, close);
        ItemStack selection = new ItemStack(Material.COMPASS);
        ItemMeta selectionMeta = selection.getItemMeta();
        selectionMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf Current Selection");
        ArrayList<Object> selectionLore = new ArrayList<Object>();
        selectionLore.add(String.valueOf(ChatColor.GRAY) + "Shows your currently selected custom mob");
        selectionLore.add("");
        selectionLore.add(String.valueOf(ChatColor.YELLOW) + "Selected: " + String.valueOf(ChatColor.WHITE) + "None");
        selectionLore.add(String.valueOf(ChatColor.GRAY) + "Click a mob above to select it");
        selectionMeta.setLore(selectionLore);
        selection.setItemMeta(selectionMeta);
        inv.setItem(40, selection);
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    int[] mobSlots;
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player)e.getWhoClicked();
                    for (int mobSlot : mobSlots = new int[]{10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25, 28, 29, 30, 31, 32, 33, 34}) {
                        ItemStack clickedItem;
                        if (slot != mobSlot || (clickedItem = e.getCurrentItem()) == null || clickedItem.getType() == Material.AIR || !clickedItem.hasItemMeta()) continue;
                        CustomMobSelectionGUI.handleMobSelection(clicker, clickedItem, plugin);
                        return;
                    }
                    switch (slot) {
                        case 45: {
                            clicker.closeInventory();
                            BuildingToolsGUI.open(clicker, plugin);
                            break;
                        }
                        case 49: {
                            clicker.closeInventory();
                            CustomMobSelectionGUI.showCustomMobHelp(clicker);
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                        }
                    }
                }
            }
        }, (Plugin)pl);
    }

    private static void handleMobSelection(Player player, ItemStack mobItem, ApexDungeons plugin) {
        String displayName = mobItem.getItemMeta().getDisplayName();
        for (CustomMobConfig config : plugin.getCustomMobManager().getAllCustomMobs()) {
            if (!displayName.contains(config.getDisplayName())) continue;
            playerSelections.put(player.getUniqueId(), config.getFileName());
            CustomMobSelectionGUI.giveCustomMobSpawnTool(player, config, plugin);
            player.closeInventory();
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Selected custom mob: " + String.valueOf(ChatColor.AQUA) + config.getDisplayName());
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You've been given a custom mob spawn tool!");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Right-click blocks to place spawn points for this mob");
            player.sendMessage("");
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udccb Mob Details:");
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Category: " + String.valueOf(ChatColor.WHITE) + config.getCategory());
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Difficulty: " + String.valueOf(ChatColor.WHITE) + config.getDifficulty());
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Spawn Radius: " + String.valueOf(ChatColor.WHITE) + config.getSpawnRadius() + " blocks");
            player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Cooldown: " + String.valueOf(ChatColor.WHITE) + config.getMinCooldown() + "-" + config.getMaxCooldown() + "s");
            return;
        }
    }

    private static void giveCustomMobSpawnTool(Player player, CustomMobConfig config, ApexDungeons plugin) {
        ItemStack tool = new ItemStack(config.getIcon());
        ItemMeta meta = tool.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "Custom Mob Spawn Tool");
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + "Right-click blocks to place spawn points");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "Selected Mob:");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + config.getDisplayName());
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + config.getDescription());
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "Spawn Configuration:");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Radius: " + String.valueOf(ChatColor.WHITE) + config.getSpawnRadius() + " blocks");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Cooldown: " + String.valueOf(ChatColor.WHITE) + config.getMinCooldown() + "-" + config.getMaxCooldown() + "s");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 Max Concurrent: " + String.valueOf(ChatColor.WHITE) + config.getMaxConcurrent());
        if (config.isBoss()) {
            lore.add(String.valueOf(ChatColor.DARK_RED) + "\u2022 BOSS LEVEL ENEMY");
        }
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "Made by Vexy");
        meta.setLore(lore);
        meta.getPersistentDataContainer().set(new NamespacedKey((Plugin)plugin, "custom_mob_tool"), PersistentDataType.STRING, (Object)config.getFileName());
        tool.setItemMeta(meta);
        player.getInventory().addItem(new ItemStack[]{tool});
    }

    private static void showCustomMobHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "      \u2694 CUSTOM MOB SYSTEM GUIDE \u2694");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf HOW IT WORKS:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Select a custom mob from this GUI");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Receive a custom mob spawn tool");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Right-click blocks to place spawn points");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Spawn points trigger when players get close");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd27 BUILDER MODE vs GAMEPLAY:");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Builder Mode:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "See visual indicators (particles, text)");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Get spawn notifications in chat");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "View spawn point status and cooldowns");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Gameplay Mode:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "No visual indicators or chat spam");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Silent, seamless mob spawning");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Natural dungeon experience");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u26a1 CUSTOM MOB FEATURES:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Enhanced attributes and equipment");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Custom spawn commands and effects");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Configurable spawn rates and limits");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Boss-level enemies with special abilities");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 PRO TIPS:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Mix different difficulty mobs for varied challenges");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Place boss mobs in final rooms for epic encounters");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Use spawn radius to control encounter areas");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Test your spawn points before releasing to players");
    }

    public static String getPlayerSelection(Player player) {
        return playerSelections.get(player.getUniqueId());
    }

    public static void clearPlayerSelection(Player player) {
        playerSelections.remove(player.getUniqueId());
    }
}

