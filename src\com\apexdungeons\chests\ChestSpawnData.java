/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.configuration.file.YamlConfiguration
 */
package com.apexdungeons.chests;

import com.apexdungeons.ApexDungeons;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.configuration.file.YamlConfiguration;

public class ChestSpawnData {
    private final ApexDungeons plugin;
    private final Map<UUID, String> playerLootTableSelections = new HashMap<UUID, String>();
    private final Map<UUID, Double> playerRadiusSettings = new HashMap<UUID, Double>();

    public ChestSpawnData(ApexDungeons plugin) {
        this.plugin = plugin;
        this.loadData();
    }

    public void setPlayerLootTableSelection(UUID playerId, String lootTable) {
        this.playerLootTableSelections.put(playerId, lootTable);
        this.saveData();
    }

    public String getPlayerLootTableSelection(UUID playerId) {
        return this.playerLootTableSelections.get(playerId);
    }

    public void setPlayerRadius(UUID playerId, double radius) {
        this.playerRadiusSettings.put(playerId, radius);
        this.saveData();
    }

    public double getPlayerRadius(UUID playerId, double defaultRadius) {
        return this.playerRadiusSettings.getOrDefault(playerId, defaultRadius);
    }

    public void clearPlayerLootTableSelection(UUID playerId) {
        this.playerLootTableSelections.remove(playerId);
        this.saveData();
    }

    private void loadData() {
        UUID playerId;
        File file = new File(this.plugin.getDataFolder(), "chest_spawn_data.yml");
        if (!file.exists()) {
            return;
        }
        YamlConfiguration config = YamlConfiguration.loadConfiguration((File)file);
        if (config.contains("loot_table_selections")) {
            for (String key : config.getConfigurationSection("loot_table_selections").getKeys(false)) {
                try {
                    playerId = UUID.fromString(key);
                    String lootTable = config.getString("loot_table_selections." + key);
                    this.playerLootTableSelections.put(playerId, lootTable);
                }
                catch (Exception e) {
                    this.plugin.getLogger().warning("Failed to load loot table selection for " + key + ": " + e.getMessage());
                }
            }
        }
        if (config.contains("radius_settings")) {
            for (String key : config.getConfigurationSection("radius_settings").getKeys(false)) {
                try {
                    playerId = UUID.fromString(key);
                    double radius = config.getDouble("radius_settings." + key);
                    this.playerRadiusSettings.put(playerId, radius);
                }
                catch (Exception e) {
                    this.plugin.getLogger().warning("Failed to load radius setting for " + key + ": " + e.getMessage());
                }
            }
        }
        this.plugin.getLogger().info("Loaded chest spawn data for " + this.playerLootTableSelections.size() + " loot table selections, " + this.playerRadiusSettings.size() + " radius settings");
    }

    private void saveData() {
        File file = new File(this.plugin.getDataFolder(), "chest_spawn_data.yml");
        YamlConfiguration config = new YamlConfiguration();
        for (Map.Entry<UUID, String> entry : this.playerLootTableSelections.entrySet()) {
            config.set("loot_table_selections." + entry.getKey().toString(), (Object)entry.getValue());
        }
        for (Map.Entry<UUID, Object> entry : this.playerRadiusSettings.entrySet()) {
            config.set("radius_settings." + entry.getKey().toString(), entry.getValue());
        }
        try {
            config.save(file);
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save chest spawn data: " + e.getMessage());
        }
    }

    public void shutdown() {
        this.saveData();
        this.playerLootTableSelections.clear();
        this.playerRadiusSettings.clear();
    }
}

