/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 */
package com.apexdungeons.schematics;

import com.apexdungeons.schematics.SchematicData;
import java.io.DataInputStream;
import java.io.EOFException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.zip.GZIPInputStream;
import org.bukkit.Material;

public class NBTSchematicReader {
    public static SchematicData readSchematic(File file) throws IOException {
        String fileName = file.getName().toLowerCase();
        if (fileName.endsWith(".schem")) {
            return NBTSchematicReader.readSpongeSchematic(file);
        }
        if (fileName.endsWith(".schematic")) {
            return NBTSchematicReader.readLegacySchematic(file);
        }
        if (fileName.endsWith(".nbt")) {
            return NBTSchematicReader.readNBTStructure(file);
        }
        throw new IOException("Unsupported file format: " + fileName);
    }

    /*
     * Enabled aggressive exception aggregation
     */
    private static SchematicData readSpongeSchematic(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);){
            SchematicData schematicData;
            try (GZIPInputStream gzis = new GZIPInputStream(fis);){
                NBTCompound root = NBTSchematicReader.readNBTCompound(gzis);
                int width = root.getShort("Width");
                int height = root.getShort("Height");
                int length = root.getShort("Length");
                NBTCompound palette = root.getCompound("Palette");
                Map<Integer, Material> paletteMap = NBTSchematicReader.parsePalette(palette);
                byte[] blockData = root.getByteArray("BlockData");
                Material[][][] blocks = new Material[height][length][width];
                int index = 0;
                for (int y = 0; y < height; ++y) {
                    for (int z = 0; z < length; ++z) {
                        for (int x = 0; x < width; ++x) {
                            Material material;
                            int paletteId = NBTSchematicReader.readVarInt(blockData, index);
                            blocks[y][z][x] = material = paletteMap.getOrDefault(paletteId, Material.AIR);
                            index += NBTSchematicReader.getVarIntSize(paletteId);
                        }
                    }
                }
                String name = file.getName().replaceFirst("[.][^.]+$", "");
                schematicData = new SchematicData(name, width, height, length, blocks);
            }
            return schematicData;
        }
        catch (Exception e) {
            throw new IOException("Failed to read Sponge schematic: " + e.getMessage(), e);
        }
    }

    /*
     * Enabled aggressive exception aggregation
     */
    private static SchematicData readLegacySchematic(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);){
            SchematicData schematicData;
            try (GZIPInputStream gzis = new GZIPInputStream(fis);){
                NBTCompound root = NBTSchematicReader.readNBTCompound(gzis);
                short width = root.getShort("Width");
                short height = root.getShort("Height");
                short length = root.getShort("Length");
                byte[] blockIds = root.getByteArray("Blocks");
                byte[] blockData = root.getByteArray("Data");
                Material[][][] blocks = new Material[height][length][width];
                for (int index = 0; index < blockIds.length; ++index) {
                    Material material;
                    int x = index % width;
                    int z = index / width % length;
                    int y = index / (width * length);
                    int blockId = blockIds[index] & 0xFF;
                    int data = blockData != null && index < blockData.length ? blockData[index] & 0xFF : 0;
                    blocks[y][z][x] = material = NBTSchematicReader.legacyIdToMaterial(blockId, data);
                }
                String name = file.getName().replaceFirst("[.][^.]+$", "");
                schematicData = new SchematicData(name, width, height, length, blocks);
            }
            return schematicData;
        }
        catch (Exception e) {
            throw new IOException("Failed to read legacy schematic: " + e.getMessage(), e);
        }
    }

    /*
     * Enabled aggressive exception aggregation
     */
    private static SchematicData readNBTStructure(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);){
            NBTCompound root;
            GZIPInputStream gzis;
            block14: {
                SchematicData schematicData;
                gzis = new GZIPInputStream(fis);
                try {
                    root = NBTSchematicReader.readNBTCompound(gzis);
                    if (!root.hasKey("size")) break block14;
                    schematicData = NBTSchematicReader.readMinecraftStructure(root, file.getName());
                }
                catch (Throwable throwable) {
                    try {
                        gzis.close();
                    }
                    catch (Throwable throwable2) {
                        throwable.addSuppressed(throwable2);
                    }
                    throw throwable;
                }
                gzis.close();
                return schematicData;
            }
            SchematicData schematicData = NBTSchematicReader.readSimpleNBT(root, file.getName());
            gzis.close();
            return schematicData;
        }
        catch (Exception e) {
            throw new IOException("Failed to read NBT structure: " + e.getMessage(), e);
        }
    }

    private static Map<Integer, Material> parsePalette(NBTCompound palette) {
        HashMap<Integer, Material> paletteMap = new HashMap<Integer, Material>();
        for (String blockName : palette.getKeys()) {
            int id = palette.getInt(blockName);
            Material material = NBTSchematicReader.parseMaterial(blockName);
            paletteMap.put(id, material);
        }
        return paletteMap;
    }

    private static Material parseMaterial(String blockName) {
        if (blockName.contains(":")) {
            blockName = blockName.substring(blockName.indexOf(":") + 1);
        }
        String materialName = blockName.toUpperCase().replace(".", "_");
        try {
            return Material.valueOf((String)materialName);
        }
        catch (IllegalArgumentException e) {
            switch (materialName) {
                case "STONE": {
                    return Material.STONE;
                }
                case "GRASS": {
                    return Material.GRASS_BLOCK;
                }
                case "DIRT": {
                    return Material.DIRT;
                }
                case "COBBLESTONE": {
                    return Material.COBBLESTONE;
                }
                case "PLANKS": {
                    return Material.OAK_PLANKS;
                }
                case "LOG": {
                    return Material.OAK_LOG;
                }
                case "LEAVES": {
                    return Material.OAK_LEAVES;
                }
            }
            return Material.AIR;
        }
    }

    private static Material legacyIdToMaterial(int blockId, int data) {
        switch (blockId) {
            case 0: {
                return Material.AIR;
            }
            case 1: {
                return Material.STONE;
            }
            case 2: {
                return Material.GRASS_BLOCK;
            }
            case 3: {
                return Material.DIRT;
            }
            case 4: {
                return Material.COBBLESTONE;
            }
            case 5: {
                return Material.OAK_PLANKS;
            }
            case 6: {
                return Material.OAK_SAPLING;
            }
            case 7: {
                return Material.BEDROCK;
            }
            case 8: 
            case 9: {
                return Material.WATER;
            }
            case 10: 
            case 11: {
                return Material.LAVA;
            }
            case 12: {
                return Material.SAND;
            }
            case 13: {
                return Material.GRAVEL;
            }
            case 14: {
                return Material.GOLD_ORE;
            }
            case 15: {
                return Material.IRON_ORE;
            }
            case 16: {
                return Material.COAL_ORE;
            }
            case 17: {
                return Material.OAK_LOG;
            }
            case 18: {
                return Material.OAK_LEAVES;
            }
            case 19: {
                return Material.SPONGE;
            }
            case 20: {
                return Material.GLASS;
            }
        }
        return Material.STONE;
    }

    private static int readVarInt(byte[] data, int offset) {
        int value = 0;
        int position = 0;
        while (offset < data.length) {
            byte currentByte = data[offset++];
            value |= (currentByte & 0x7F) << position;
            if ((currentByte & 0x80) == 0) break;
            if ((position += 7) < 32) continue;
            throw new RuntimeException("VarInt is too big");
        }
        return value;
    }

    private static int getVarIntSize(int value) {
        int size = 0;
        do {
            ++size;
        } while ((value >>>= 7) != 0);
        return size;
    }

    private static SchematicData readMinecraftStructure(NBTCompound root, String fileName) {
        String name = fileName.replaceFirst("[.][^.]+$", "");
        Material[][][] blocks = new Material[5][5][5];
        for (int y = 0; y < 5; ++y) {
            for (int z = 0; z < 5; ++z) {
                for (int x = 0; x < 5; ++x) {
                    blocks[y][z][x] = y == 0 ? Material.STONE_BRICKS : (y == 4 || x == 0 || x == 4 || z == 0 || z == 4 ? Material.STONE_BRICKS : Material.AIR);
                }
            }
        }
        return new SchematicData(name, 5, 5, 5, blocks);
    }

    private static SchematicData readSimpleNBT(NBTCompound root, String fileName) {
        String name = fileName.replaceFirst("[.][^.]+$", "");
        Material[][][] blocks = new Material[4][7][7];
        for (int y = 0; y < 4; ++y) {
            for (int z = 0; z < 7; ++z) {
                for (int x = 0; x < 7; ++x) {
                    blocks[y][z][x] = y == 0 || y == 3 ? Material.STONE_BRICKS : (x == 0 || x == 6 || z == 0 || z == 6 ? Material.STONE_BRICKS : Material.AIR);
                }
            }
        }
        return new SchematicData(name, 7, 4, 7, blocks);
    }

    private static NBTCompound readNBTCompound(InputStream input) throws IOException {
        NBTCompound compound = new NBTCompound();
        try {
            DataInputStream dis = new DataInputStream(input);
            byte type = dis.readByte();
            if (type != 10) {
                throw new IOException("Expected TAG_Compound, got " + type);
            }
            short nameLength = dis.readShort();
            if (nameLength > 0) {
                byte[] nameBytes = new byte[nameLength];
                dis.readFully(nameBytes);
            }
            NBTSchematicReader.readCompoundContents(dis, compound);
        }
        catch (EOFException eOFException) {
        }
        catch (Exception exception) {
            // empty catch block
        }
        return compound;
    }

    private static void readCompoundContents(DataInputStream dis, NBTCompound compound) throws IOException {
        byte type;
        block14: while ((type = dis.readByte()) != 0) {
            short nameLength = dis.readShort();
            byte[] nameBytes = new byte[nameLength];
            dis.readFully(nameBytes);
            String name = new String(nameBytes, "UTF-8");
            switch (type) {
                case 1: {
                    compound.data.put(name, dis.readByte());
                    continue block14;
                }
                case 2: {
                    compound.data.put(name, dis.readShort());
                    continue block14;
                }
                case 3: {
                    compound.data.put(name, dis.readInt());
                    continue block14;
                }
                case 4: {
                    compound.data.put(name, dis.readLong());
                    continue block14;
                }
                case 5: {
                    compound.data.put(name, Float.valueOf(dis.readFloat()));
                    continue block14;
                }
                case 6: {
                    compound.data.put(name, dis.readDouble());
                    continue block14;
                }
                case 7: {
                    int byteArrayLength = dis.readInt();
                    byte[] byteArray = new byte[byteArrayLength];
                    dis.readFully(byteArray);
                    compound.data.put(name, byteArray);
                    continue block14;
                }
                case 8: {
                    short stringLength = dis.readShort();
                    byte[] stringBytes = new byte[stringLength];
                    dis.readFully(stringBytes);
                    compound.data.put(name, new String(stringBytes, "UTF-8"));
                    continue block14;
                }
                case 9: {
                    NBTSchematicReader.skipTag(dis, type);
                    continue block14;
                }
                case 10: {
                    NBTCompound subCompound = new NBTCompound();
                    NBTSchematicReader.readCompoundContents(dis, subCompound);
                    compound.data.put(name, subCompound);
                    continue block14;
                }
                case 11: {
                    int intArrayLength = dis.readInt();
                    int[] intArray = new int[intArrayLength];
                    for (int i = 0; i < intArrayLength; ++i) {
                        intArray[i] = dis.readInt();
                    }
                    compound.data.put(name, intArray);
                    continue block14;
                }
                case 12: {
                    int longArrayLength = dis.readInt();
                    long[] longArray = new long[longArrayLength];
                    for (int i = 0; i < longArrayLength; ++i) {
                        longArray[i] = dis.readLong();
                    }
                    compound.data.put(name, longArray);
                    continue block14;
                }
            }
            NBTSchematicReader.skipTag(dis, type);
        }
    }

    private static void skipTag(DataInputStream dis, byte type) throws IOException {
        try {
            switch (type) {
                case 9: {
                    byte listType = dis.readByte();
                    int listLength = dis.readInt();
                    for (int i = 0; i < Math.min(listLength, 1000); ++i) {
                        NBTSchematicReader.skipTag(dis, listType);
                    }
                    break;
                }
                default: {
                    dis.skipBytes(4);
                }
            }
        }
        catch (Exception exception) {
            // empty catch block
        }
    }

    private static class NBTCompound {
        private Map<String, Object> data = new HashMap<String, Object>();

        private NBTCompound() {
        }

        public short getShort(String key) {
            return (Short)this.data.getOrDefault(key, (short)0);
        }

        public int getInt(String key) {
            return (Integer)this.data.getOrDefault(key, 0);
        }

        public byte[] getByteArray(String key) {
            return (byte[])this.data.getOrDefault(key, new byte[0]);
        }

        public NBTCompound getCompound(String key) {
            return (NBTCompound)this.data.getOrDefault(key, new NBTCompound());
        }

        public boolean hasKey(String key) {
            return this.data.containsKey(key);
        }

        public Set<String> getKeys() {
            return this.data.keySet();
        }
    }
}

