/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.block.Block
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.SchematicSelectionGUI;
import com.apexdungeons.schematics.SchematicData;
import com.apexdungeons.schematics.SchematicPreview;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;

public class MasterBuilderWand
implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey wandKey;
    private final Map<UUID, String> selectedSchematics = new HashMap<UUID, String>();
    private final Map<UUID, SchematicPreview> activePreviews = new HashMap<UUID, SchematicPreview>();

    public MasterBuilderWand(ApexDungeons plugin) {
        this.plugin = plugin;
        this.wandKey = new NamespacedKey((Plugin)plugin, "master_builder_wand");
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public ItemStack createMasterBuilderWand() {
        ItemStack wand = new ItemStack(Material.BRUSH);
        ItemMeta meta = wand.getItemMeta();
        meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\u26a1 Master Builder Wand");
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + "The ultimate unified building tool!");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\u2728 Features:");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "All schematics from folder");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Favorites system");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "3D wireframe preview");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "WASD movement controls");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Rotation & confirmation");
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfaf Right-click: Open schematic selection");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfaf Left-click: Place selected schematic");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "Made by Vexy");
        meta.setLore(lore);
        meta.getPersistentDataContainer().set(this.wandKey, PersistentDataType.STRING, (Object)"master_builder_wand");
        wand.setItemMeta(meta);
        return wand;
    }

    public boolean isMasterBuilderWand(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        return item.getItemMeta().getPersistentDataContainer().has(this.wandKey, PersistentDataType.STRING);
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Block clickedBlock;
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        if (!this.isMasterBuilderWand(item)) {
            return;
        }
        event.setCancelled(true);
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            this.openSchematicSelection(player);
        } else if (event.getAction() == Action.LEFT_CLICK_BLOCK && (clickedBlock = event.getClickedBlock()) != null) {
            this.placeSelectedSchematic(player, clickedBlock.getLocation().add(0.0, 1.0, 0.0));
        }
    }

    private void openSchematicSelection(Player player) {
        SchematicSelectionGUI.open(player, this.plugin);
    }

    public void setSelectedSchematic(Player player, String schematicName) {
        this.selectedSchematics.put(player.getUniqueId(), schematicName);
        ItemStack wand = player.getInventory().getItemInMainHand();
        if (this.isMasterBuilderWand(wand)) {
            this.updateWandLore(wand, schematicName);
        }
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Selected schematic: " + String.valueOf(ChatColor.AQUA) + schematicName);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Left-click on a block to place it!");
    }

    private void updateWandLore(ItemStack wand, String schematicName) {
        ItemMeta meta = wand.getItemMeta();
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add(String.valueOf(ChatColor.GRAY) + "The ultimate unified building tool!");
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\u2728 Currently Selected:");
        lore.add(String.valueOf(ChatColor.AQUA) + "\ud83d\udcd0 " + String.valueOf(ChatColor.WHITE) + schematicName);
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "\u2728 Features:");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "All schematics from folder");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Favorites system");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "3D wireframe preview");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "WASD movement controls");
        lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Rotation & confirmation");
        lore.add("");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfaf Right-click: Change schematic");
        lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfaf Left-click: Place " + schematicName);
        lore.add("");
        lore.add(String.valueOf(ChatColor.GREEN) + "Made by Vexy");
        meta.setLore(lore);
        wand.setItemMeta(meta);
    }

    private void placeSelectedSchematic(Player player, Location targetLocation) {
        String schematicName = this.selectedSchematics.get(player.getUniqueId());
        if (schematicName == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "No schematic selected! Right-click to choose one.");
            return;
        }
        SchematicData schematic = this.plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Schematic not found: " + schematicName);
            return;
        }
        this.startPreviewMode(player, schematic, targetLocation);
    }

    private void startPreviewMode(Player player, SchematicData schematic, Location location) {
        this.cancelPreview(player);
        SchematicPreview preview = new SchematicPreview(this.plugin, player, schematic, location);
        this.activePreviews.put(player.getUniqueId(), preview);
        preview.startPreview();
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2728 3D Preview Mode Activated!");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfae Controls:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 WASD " + String.valueOf(ChatColor.WHITE) + "- Move schematic");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 R " + String.valueOf(ChatColor.WHITE) + "- Rotate 90\u00b0");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Enter " + String.valueOf(ChatColor.WHITE) + "- Confirm placement");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 Esc " + String.valueOf(ChatColor.WHITE) + "- Cancel");
    }

    public void cancelPreview(Player player) {
        SchematicPreview preview = this.activePreviews.remove(player.getUniqueId());
        if (preview != null) {
            preview.stopPreview();
        }
    }

    public String getSelectedSchematic(Player player) {
        return this.selectedSchematics.get(player.getUniqueId());
    }

    public boolean hasActivePreview(Player player) {
        return this.activePreviews.containsKey(player.getUniqueId());
    }

    public SchematicPreview getActivePreview(Player player) {
        return this.activePreviews.get(player.getUniqueId());
    }
}

