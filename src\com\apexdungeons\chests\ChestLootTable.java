/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 *  org.bukkit.inventory.ItemStack
 */
package com.apexdungeons.chests;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

public class ChestLootTable {
    private final String name;
    private final List<WeightedItem> items = new ArrayList<WeightedItem>();
    private final Random random = new Random();

    public ChestLootTable(String name) {
        this.name = name;
    }

    public void addItem(ItemStack item, int weight) {
        this.items.add(new WeightedItem(item.clone(), weight));
    }

    public boolean removeItem(Material material) {
        return this.items.removeIf(weightedItem -> weightedItem.getItem().getType() == material);
    }

    public List<ItemStack> generateLoot() {
        ArrayList<ItemStack> loot = new ArrayList<ItemStack>();
        if (this.items.isEmpty()) {
            return loot;
        }
        int totalWeight = this.items.stream().mapToInt(WeightedItem::getWeight).sum();
        int itemCount = 1 + this.random.nextInt(4);
        for (int i = 0; i < itemCount; ++i) {
            WeightedItem selectedItem = this.selectRandomItem(totalWeight);
            if (selectedItem == null) continue;
            loot.add(selectedItem.getItem().clone());
        }
        return loot;
    }

    private WeightedItem selectRandomItem(int totalWeight) {
        if (totalWeight <= 0) {
            return null;
        }
        int randomValue = this.random.nextInt(totalWeight);
        int currentWeight = 0;
        for (WeightedItem item : this.items) {
            if (randomValue >= (currentWeight += item.getWeight())) continue;
            return item;
        }
        return this.items.isEmpty() ? null : this.items.get(this.items.size() - 1);
    }

    public List<WeightedItem> getItems() {
        return new ArrayList<WeightedItem>(this.items);
    }

    public String getName() {
        return this.name;
    }

    public boolean isEmpty() {
        return this.items.isEmpty();
    }

    public int getTotalWeight() {
        return this.items.stream().mapToInt(WeightedItem::getWeight).sum();
    }

    public static class WeightedItem {
        private final ItemStack item;
        private final int weight;

        public WeightedItem(ItemStack item, int weight) {
            this.item = item.clone();
            this.weight = Math.max(1, weight);
        }

        public ItemStack getItem() {
            return this.item.clone();
        }

        public int getWeight() {
            return this.weight;
        }

        public double getProbability(int totalWeight) {
            return totalWeight > 0 ? (double)this.weight / (double)totalWeight * 100.0 : 0.0;
        }

        public String toString() {
            return String.format("WeightedItem{item=%s x%d, weight=%d}", this.item.getType().name(), this.item.getAmount(), this.weight);
        }
    }
}

