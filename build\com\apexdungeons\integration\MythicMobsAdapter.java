/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.entity.EntityType
 *  org.bukkit.entity.LivingEntity
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.integration;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.integration.MobAdapter;
import com.apexdungeons.integration.VanillaAdapter;
import com.apexdungeons.mobs.CustomMobConfig;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Optional;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;

public class MythicMobsAdapter
implements MobAdapter {
    private final ApexDungeons plugin;
    private final VanillaAdapter fallback;
    private boolean mythicMobsAvailable = false;

    public MythicMobsAdapter(ApexDungeons plugin) {
        this.plugin = plugin;
        this.fallback = new VanillaAdapter();
        this.mythicMobsAvailable = this.checkMythicMobsAvailability();
        if (this.mythicMobsAvailable) {
            plugin.getLogger().info("[MythicMobsAdapter] MythicMobs integration enabled successfully");
        } else {
            plugin.getLogger().info("[MythicMobsAdapter] MythicMobs not available, using fallback mode");
        }
    }

    private boolean checkMythicMobsAvailability() {
        try {
            return Bukkit.getPluginManager().getPlugin("MythicMobs") != null && Bukkit.getPluginManager().isPluginEnabled("MythicMobs");
        }
        catch (Exception e) {
            this.plugin.getLogger().warning("[MythicMobsAdapter] Error checking MythicMobs availability: " + e.getMessage());
            return false;
        }
    }

    @Override
    public LivingEntity spawnMob(String mobName, Location location) {
        this.plugin.getLogger().info("[MythicMobsAdapter] Attempting to spawn mob: " + mobName + " at " + String.valueOf(location));
        if (mobName == null || location == null) {
            this.plugin.getLogger().warning("[MythicMobsAdapter] Invalid parameters: mobName=" + mobName + ", location=" + String.valueOf(location));
            return null;
        }

        // Enhanced MythicMobs integration with level support
        if (mobName.contains(":")) {
            String[] parts = mobName.split(":");
            if (parts.length >= 2) {
                String actualMobName = parts[1];
                int level = 1;
                if (parts.length >= 3) {
                    try {
                        level = Integer.parseInt(parts[2]);
                    } catch (NumberFormatException e) {
                        level = 1;
                    }
                }
                return this.spawnMythicMobWithLevel(actualMobName, location, level);
            }
        }

        if (mobName.startsWith("custom:")) {
            return this.spawnCustomMob(mobName.substring(7), location, false);
        }

        LivingEntity mythicEntity = this.spawnMythicMobWithCommand(mobName, location, 1);
        if (mythicEntity != null) {
            this.plugin.getLogger().info("[MythicMobsAdapter] Successfully spawned MythicMob: " + mobName);
            return mythicEntity;
        }

        this.plugin.getLogger().info("[MythicMobsAdapter] Falling back to vanilla spawn for: " + mobName);
        return this.spawnFallbackMob(mobName, location, false);
    }

    @Override
    public LivingEntity spawnBoss(String bossName, Location location) {
        this.plugin.getLogger().info("[MythicMobsAdapter] Attempting to spawn boss: " + bossName + " at " + String.valueOf(location));
        if (bossName == null || location == null) {
            this.plugin.getLogger().warning("[MythicMobsAdapter] Invalid parameters: bossName=" + bossName + ", location=" + String.valueOf(location));
            return null;
        }

        // Enhanced boss spawning with level support
        if (bossName.contains(":")) {
            String[] parts = bossName.split(":");
            if (parts.length >= 2) {
                String actualBossName = parts[1];
                int level = 5; // Default boss level
                if (parts.length >= 3) {
                    try {
                        level = Integer.parseInt(parts[2]);
                    } catch (NumberFormatException e) {
                        level = 5;
                    }
                }
                return this.spawnMythicMobWithLevel(actualBossName, location, level);
            }
        }

        if (bossName.startsWith("custom:")) {
            return this.spawnCustomMob(bossName.substring(7), location, true);
        }

        // Use command-based spawning for bosses with higher level
        LivingEntity mythicEntity = this.spawnMythicMobWithCommand(bossName, location, 8);
        if (mythicEntity != null) {
            this.plugin.getLogger().info("[MythicMobsAdapter] Successfully spawned MythicMobs boss: " + bossName);
            return mythicEntity;
        }

        this.plugin.getLogger().info("[MythicMobsAdapter] Falling back to vanilla boss spawn for: " + bossName);
        return this.spawnFallbackMob(bossName, location, true);
    }

    private LivingEntity spawnMythicMobWithLevel(String mobName, Location location, int level) {
        // Try command-based spawning first for better integration
        LivingEntity commandEntity = this.spawnMythicMobWithCommand(mobName, location, level);
        if (commandEntity != null) {
            return commandEntity;
        }

        // Fallback to API spawning
        return this.spawnMythicMob(mobName, location);
    }

    private LivingEntity spawnMythicMobWithCommand(String mobName, Location location, int level) {
        try {
            if (!this.mythicMobsAvailable) {
                return null;
            }

            this.plugin.getLogger().info("[MythicMobsAdapter] Using MythicMobs command spawn: " + mobName + " level " + level);

            // Execute MythicMobs spawn command
            String command = String.format("mm mobs spawn %s %s,%s,%s,%s %d",
                mobName,
                location.getWorld().getName(),
                location.getBlockX(),
                location.getBlockY(),
                location.getBlockZ(),
                level);

            this.plugin.getLogger().info("[MythicMobsAdapter] Executing command: " + command);

            // Execute command as console
            boolean success = Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);

            if (success) {
                // Find the spawned entity (this is a bit hacky but works)
                for (LivingEntity entity : location.getWorld().getLivingEntities()) {
                    if (entity.getLocation().distance(location) < 2.0) {
                        this.plugin.getLogger().info("[MythicMobsAdapter] Found spawned entity: " + entity.getType());
                        return entity;
                    }
                }
            }

        } catch (Exception e) {
            this.plugin.getLogger().warning("[MythicMobsAdapter] Command spawn failed: " + e.getMessage());
        }

        return null;
    }

    private LivingEntity spawnMythicMob(String mobName, Location location) {
        block10: {
            try {
                this.plugin.getLogger().info("[MythicMobsAdapter] Attempting MythicMobs spawn: " + mobName);
                Plugin mm = Bukkit.getPluginManager().getPlugin("MythicMobs");
                if (mm == null) {
                    this.plugin.getLogger().warning("[MythicMobsAdapter] MythicMobs plugin not found");
                    return null;
                }
                try {
                    Class<?> mythicClass = Class.forName("io.lumine.mythic.bukkit.MythicBukkit");
                    Object mythic = mythicClass.getMethod("inst", new Class[0]).invoke(null, new Object[0]);
                    Object mobManager = mythicClass.getMethod("getMobManager", new Class[0]).invoke(mythic, new Object[0]);
                    Optional mythicMob = (Optional)mobManager.getClass().getMethod("getMythicMob", String.class).invoke(mobManager, mobName);
                    if (mythicMob.isPresent()) {
                        Object bukkitEntity;
                        Object entity;
                        Object mmob = mythicMob.get();
                        Class<?> adapterClass = Class.forName("io.lumine.mythic.bukkit.adapter.BukkitAdapter");
                        Object spawnLoc = adapterClass.getMethod("adapt", Location.class).invoke(null, location);
                        Object spawnedMob = mmob.getClass().getMethod("spawn", Class.forName("io.lumine.mythic.api.world.MythicLocation"), Integer.TYPE).invoke(mmob, spawnLoc, 1);
                        if (spawnedMob != null && (entity = spawnedMob.getClass().getMethod("getEntity", new Class[0]).invoke(spawnedMob, new Object[0])) != null && (bukkitEntity = entity.getClass().getMethod("getBukkitEntity", new Class[0]).invoke(entity, new Object[0])) instanceof LivingEntity) {
                            this.plugin.getLogger().info("[MythicMobsAdapter] MythicMob spawned successfully: " + mobName);
                            return (LivingEntity)bukkitEntity;
                        }
                        break block10;
                    }
                    this.plugin.getLogger().info("[MythicMobsAdapter] MythicMob not found in registry: " + mobName);
                }
                catch (ClassNotFoundException e) {
                    this.plugin.getLogger().info("[MythicMobsAdapter] Trying older MythicMobs API...");
                    try {
                        Object entity;
                        Object spawnedMob;
                        Class<?> mythicClass = Class.forName("io.lumine.xikage.mythicmobs.MythicMobs");
                        Object mythic = mythicClass.getMethod("inst", new Class[0]).invoke(null, new Object[0]);
                        Object mobManager = mythicClass.getMethod("getMobManager", new Class[0]).invoke(mythic, new Object[0]);
                        Object mythicMob = mobManager.getClass().getMethod("getMythicMob", String.class).invoke(mobManager, mobName);
                        if (mythicMob != null && (spawnedMob = mythicMob.getClass().getMethod("spawn", Location.class, Integer.TYPE).invoke(mythicMob, location, 1)) != null && (entity = spawnedMob.getClass().getMethod("getEntity", new Class[0]).invoke(spawnedMob, new Object[0])) instanceof LivingEntity) {
                            this.plugin.getLogger().info("[MythicMobsAdapter] MythicMob spawned with older API: " + mobName);
                            return (LivingEntity)entity;
                        }
                    }
                    catch (Exception oldApiException) {
                        this.plugin.getLogger().warning("[MythicMobsAdapter] Both new and old MythicMobs APIs failed: " + oldApiException.getMessage());
                    }
                }
            }
            catch (Exception e) {
                this.plugin.getLogger().severe("[MythicMobsAdapter] Error spawning MythicMob '" + mobName + "': " + e.getMessage());
                e.printStackTrace();
            }
        }
        return null;
    }

    private LivingEntity spawnCustomMob(String customMobName, Location location, boolean isBoss) {
        try {
            this.plugin.getLogger().info("[MythicMobsAdapter] Attempting to spawn custom mob: " + customMobName);
            if (this.plugin.getCustomMobManager() != null) {
                boolean success = this.plugin.getCustomMobManager().spawnCustomMob(customMobName, location, false);
                if (success) {
                    this.plugin.getLogger().info("[MythicMobsAdapter] Successfully spawned custom mob: " + customMobName);
                    return this.spawnFallbackMob(customMobName, location, isBoss);
                }
                this.plugin.getLogger().warning("[MythicMobsAdapter] Custom mob spawn failed: " + customMobName);
            } else {
                this.plugin.getLogger().warning("[MythicMobsAdapter] CustomMobManager is null");
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[MythicMobsAdapter] Error spawning custom mob '" + customMobName + "': " + e.getMessage());
            e.printStackTrace();
        }
        return this.spawnFallbackMob(customMobName, location, isBoss);
    }

    private LivingEntity spawnFallbackMob(String mobName, Location location, boolean isBoss) {
        try {
            EntityType entityType;
            this.plugin.getLogger().info("[MythicMobsAdapter] Attempting fallback spawn for: " + mobName + " (boss: " + isBoss + ")");
            LivingEntity vanillaEntity = this.fallback.spawnMob(mobName, location);
            if (vanillaEntity != null) {
                if (isBoss) {
                    vanillaEntity.setCustomName(String.valueOf(ChatColor.DARK_RED) + "BOSS " + String.valueOf(ChatColor.RED) + mobName);
                    vanillaEntity.setCustomNameVisible(true);
                    try {
                        double maxHealth = vanillaEntity.getMaxHealth();
                        double newMaxHealth = Math.max(maxHealth * 3.0, 150.0);
                        vanillaEntity.setMaxHealth(newMaxHealth);
                        vanillaEntity.setHealth(newMaxHealth);
                        this.plugin.getLogger().info("[MythicMobsAdapter] Enhanced boss health to: " + newMaxHealth);
                    }
                    catch (Exception healthException) {
                        this.plugin.getLogger().info("[MythicMobsAdapter] Could not enhance boss health: " + healthException.getMessage());
                    }
                } else {
                    vanillaEntity.setCustomName(String.valueOf(ChatColor.RED) + mobName);
                    vanillaEntity.setCustomNameVisible(true);
                }
                this.plugin.getLogger().info("[MythicMobsAdapter] Fallback spawn successful: " + String.valueOf(vanillaEntity.getType()));
                return vanillaEntity;
            }
            EntityType entityType2 = entityType = isBoss ? EntityType.WITHER_SKELETON : EntityType.ZOMBIE;
            if (entityType.isSpawnable() && entityType.isAlive()) {
                LivingEntity entity = (LivingEntity)location.getWorld().spawnEntity(location, entityType);
                entity.setCustomName(String.valueOf(ChatColor.RED) + (isBoss ? "BOSS " : "") + mobName);
                entity.setCustomNameVisible(true);
                this.plugin.getLogger().info("[MythicMobsAdapter] Last resort spawn successful: " + String.valueOf(entityType));
                return entity;
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[MythicMobsAdapter] Fallback spawn failed: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public boolean isAvailable() {
        return this.mythicMobsAvailable;
    }

    @Override
    public String getAdapterName() {
        if (this.mythicMobsAvailable) {
            try {
                Plugin mm = Bukkit.getPluginManager().getPlugin("MythicMobs");
                if (mm != null) {
                    return "MythicMobs v" + mm.getClass().getMethod("getDescription", new Class[0]).invoke((Object)mm, new Object[0]).toString();
                }
            }
            catch (Exception exception) {
                // empty catch block
            }
            return "MythicMobs (Active)";
        }
        return "MythicMobs (Fallback Mode)";
    }

    @Override
    public String[] getAvailableMobs() {
        ArrayList<Object> workingMobs = new ArrayList<Object>();
        try {
            if (this.plugin.getCustomMobManager() != null) {
                for (CustomMobConfig config : this.plugin.getCustomMobManager().getAllCustomMobs()) {
                    workingMobs.add("custom:" + config.getFileName());
                }
                this.plugin.getLogger().info("[MythicMobsAdapter] Added " + this.plugin.getCustomMobManager().getCustomMobCount() + " custom mobs");
            }
            String[] basicMobs = new String[]{"ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "ENDERMAN", "WITCH", "VINDICATOR", "PILLAGER", "ZOMBIE_VILLAGER"};
            workingMobs.addAll(Arrays.asList(basicMobs));
            if (this.mythicMobsAvailable) {
                try {
                    String[] commonMythicMobs;
                    for (String mobName : commonMythicMobs = new String[]{"SkeletonKing", "ZombieKnight", "SpiderQueen", "FireElemental", "IceGolem", "ShadowWarrior"}) {
                        if (!this.verifyMythicMobExists(mobName)) continue;
                        workingMobs.add(mobName);
                    }
                }
                catch (Exception e) {
                    this.plugin.getLogger().info("[MythicMobsAdapter] Could not verify MythicMobs: " + e.getMessage());
                }
            }
            this.plugin.getLogger().info("[MythicMobsAdapter] Total working mobs: " + workingMobs.size());
            return workingMobs.toArray(new String[0]);
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[MythicMobsAdapter] Error getting available mobs: " + e.getMessage());
            return this.fallback.getAvailableMobs();
        }
    }

    private boolean verifyMythicMobExists(String mobName) {
        try {
            Plugin mm = Bukkit.getPluginManager().getPlugin("MythicMobs");
            if (mm == null) {
                return false;
            }
            try {
                Class<?> mythicClass = Class.forName("io.lumine.mythic.bukkit.MythicBukkit");
                Object mythic = mythicClass.getMethod("inst", new Class[0]).invoke(null, new Object[0]);
                Object mobManager = mythicClass.getMethod("getMobManager", new Class[0]).invoke(mythic, new Object[0]);
                Optional mythicMob = (Optional)mobManager.getClass().getMethod("getMythicMob", String.class).invoke(mobManager, mobName);
                return mythicMob.isPresent();
            }
            catch (ClassNotFoundException e) {
                Class<?> mythicClass = Class.forName("io.lumine.xikage.mythicmobs.MythicMobs");
                Object mythic = mythicClass.getMethod("inst", new Class[0]).invoke(null, new Object[0]);
                Object mobManager = mythicClass.getMethod("getMobManager", new Class[0]).invoke(mythic, new Object[0]);
                Object mythicMob = mobManager.getClass().getMethod("getMythicMob", String.class).invoke(mobManager, mobName);
                return mythicMob != null;
            }
        }
        catch (Exception e) {
            return false;
        }
    }

    @Override
    public String[] getAvailableBosses() {
        ArrayList<Object> bosses = new ArrayList<Object>();
        try {
            if (this.plugin.getCustomMobManager() != null) {
                for (CustomMobConfig config : this.plugin.getCustomMobManager().getAllCustomMobs()) {
                    if (!config.isBoss()) continue;
                    bosses.add("custom:" + config.getFileName());
                }
                this.plugin.getLogger().info("[MythicMobsAdapter] Added " + bosses.size() + " custom bosses");
            }
            if (this.mythicMobsAvailable) {
                String[] allMobs;
                for (String mob : allMobs = this.getAvailableMobs()) {
                    String name;
                    if (mob.startsWith("custom:") || !(name = mob.toLowerCase()).contains("boss") && !name.contains("elite") && !name.contains("champion") && !name.contains("lord") && !name.contains("king") && !name.contains("queen") && !name.contains("wither") && !name.contains("dragon") && !name.contains("guardian")) continue;
                    bosses.add(mob);
                }
                if (bosses.size() <= (this.plugin.getCustomMobManager() != null ? this.plugin.getCustomMobManager().getCustomMobCount() : 0)) {
                    for (String mob : allMobs) {
                        if (mob.startsWith("custom:")) continue;
                        bosses.add(mob);
                        if (bosses.size() >= 15) break;
                    }
                }
            }
            String[] vanillaBosses = this.fallback.getAvailableBosses();
            bosses.addAll(Arrays.asList(vanillaBosses));
            this.plugin.getLogger().info("[MythicMobsAdapter] Total available bosses: " + bosses.size());
            return bosses.toArray(new String[0]);
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[MythicMobsAdapter] Error getting available bosses: " + e.getMessage());
            return this.fallback.getAvailableBosses();
        }
    }
}

