/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.ActiveGUI;
import com.apexdungeons.gui.AdminGUI;
import com.apexdungeons.gui.BuildingToolsGUI;
import com.apexdungeons.gui.DungeonCreationGUI;
import com.apexdungeons.gui.HelpGUI;
import com.apexdungeons.gui.ProceduralDungeonGUI;
import com.apexdungeons.gui.SavedDungeonsGUI;
import com.apexdungeons.gui.StatisticsGUI;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class EnhancedMainGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.GOLD) + "Soaps Dungeons " + String.valueOf(ChatColor.GRAY) + "- Made by Vexy";

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        EnhancedMainGUI.fillBackground(inv);
        EnhancedMainGUI.createMainButtons(inv, player, plugin);
        EnhancedMainGUI.createUtilityButtons(inv, player, plugin);
        player.openInventory(inv);
        EnhancedMainGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] accentSlots;
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        ItemStack accent = new ItemStack(Material.BLUE_STAINED_GLASS_PANE);
        ItemMeta accentMeta = accent.getItemMeta();
        accentMeta.setDisplayName(" ");
        accent.setItemMeta(accentMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
        for (int slot : accentSlots = new int[]{1, 7, 46, 52}) {
            inv.setItem(slot, accent);
        }
    }

    private static void createMainButtons(Inventory inv, Player player, ApexDungeons plugin) {
        ItemStack create = new ItemStack(Material.DIAMOND_PICKAXE);
        ItemMeta createMeta = create.getItemMeta();
        createMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u271a Create New Dungeon");
        ArrayList<Object> createLore = new ArrayList<Object>();
        createLore.add(String.valueOf(ChatColor.GRAY) + "Create a new flat dungeon world");
        createLore.add(String.valueOf(ChatColor.GRAY) + "instantly with just a name!");
        createLore.add("");
        createLore.add(String.valueOf(ChatColor.YELLOW) + "\u26a1 Super Fast Process:");
        createLore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Click this button");
        createLore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Enter dungeon name in chat");
        createLore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Done! World created instantly");
        createLore.add("");
        createLore.add(String.valueOf(ChatColor.YELLOW) + "\u2728 You get:");
        createLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Completely flat superflat world");
        createLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Optimized for fast generation");
        createLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Ready for building immediately");
        createLore.add("");
        createLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click for instant creation!");
        createMeta.setLore(createLore);
        create.setItemMeta(createMeta);
        inv.setItem(20, create);
        ItemStack active = new ItemStack(Material.FILLED_MAP);
        ItemMeta activeMeta = active.getItemMeta();
        activeMeta.setDisplayName(String.valueOf(ChatColor.BLUE) + "\u2694 Active Dungeons");
        ArrayList<Object> activeLore = new ArrayList<Object>();
        activeLore.add(String.valueOf(ChatColor.GRAY) + "View and manage your");
        activeLore.add(String.valueOf(ChatColor.GRAY) + "currently active dungeons");
        activeLore.add("");
        int dungeonCount = plugin.getDungeonManager().getDungeons().size();
        activeLore.add(String.valueOf(ChatColor.YELLOW) + "Active: " + String.valueOf(ChatColor.WHITE) + dungeonCount + String.valueOf(ChatColor.GRAY) + " dungeons");
        if (dungeonCount > 0) {
            activeLore.add("");
            activeLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to view dungeons!");
        } else {
            activeLore.add("");
            activeLore.add(String.valueOf(ChatColor.GRAY) + "No active dungeons");
        }
        activeMeta.setLore(activeLore);
        active.setItemMeta(activeMeta);
        inv.setItem(22, active);
        ItemStack procedural = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta proceduralMeta = procedural.getItemMeta();
        proceduralMeta.setDisplayName(String.valueOf(ChatColor.DARK_PURPLE) + "\ud83c\udfb2 Procedural Dungeon");
        ArrayList<Object> proceduralLore = new ArrayList<Object>();
        proceduralLore.add(String.valueOf(ChatColor.GRAY) + "Generate a completely randomized");
        proceduralLore.add(String.valueOf(ChatColor.GRAY) + "dungeon with unique layouts!");
        proceduralLore.add("");
        proceduralLore.add(String.valueOf(ChatColor.YELLOW) + "\u2728 Amazing Features:");
        proceduralLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "5-12 randomly generated rooms");
        proceduralLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Rooms randomize each time players start");
        proceduralLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Connector blocks link rooms perfectly");
        proceduralLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Never breaks - always perfect layout");
        proceduralLore.add("");
        proceduralLore.add(String.valueOf(ChatColor.GOLD) + "\ud83c\udfaf Perfect for:");
        proceduralLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Replayable dungeons");
        proceduralLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Unique experiences every time");
        proceduralLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Challenging unpredictable layouts");
        proceduralLore.add("");
        proceduralLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to generate!");
        proceduralMeta.setLore(proceduralLore);
        procedural.setItemMeta(proceduralMeta);
        inv.setItem(24, procedural);
        ItemStack buildingTools = new ItemStack(Material.GOLDEN_PICKAXE);
        ItemMeta toolsMeta = buildingTools.getItemMeta();
        toolsMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udd28 Building Tools");
        ArrayList<Object> toolsLore = new ArrayList<Object>();
        toolsLore.add(String.valueOf(ChatColor.GRAY) + "Professional dungeon building");
        toolsLore.add(String.valueOf(ChatColor.GRAY) + "tools and utilities");
        toolsLore.add("");
        toolsLore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udee0\ufe0f Essential Tools:");
        toolsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Dungeon Start & End Blocks");
        toolsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Advanced Schematic Tools");
        toolsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Room Connection System");
        toolsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Mob Spawn Management");
        toolsLore.add("");
        toolsLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to access building tools!");
        toolsMeta.setLore(toolsLore);
        buildingTools.setItemMeta(toolsMeta);
        inv.setItem(25, buildingTools);
    }

    private static void createUtilityButtons(Inventory inv, Player player, ApexDungeons plugin) {
        ItemStack stats = new ItemStack(Material.CLOCK);
        ItemMeta statsMeta = stats.getItemMeta();
        statsMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udcca Statistics");
        ArrayList<Object> statsLore = new ArrayList<Object>();
        statsLore.add(String.valueOf(ChatColor.GRAY) + "View your dungeon");
        statsLore.add(String.valueOf(ChatColor.GRAY) + "creation statistics");
        statsLore.add("");
        statsLore.add(String.valueOf(ChatColor.YELLOW) + "Your Stats:");
        statsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Dungeons Created: " + String.valueOf(ChatColor.YELLOW) + "0");
        statsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Total Playtime: " + String.valueOf(ChatColor.YELLOW) + "0h 0m");
        statsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Favorite Theme: " + String.valueOf(ChatColor.YELLOW) + "None");
        statsLore.add("");
        statsLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to view details!");
        statsMeta.setLore(statsLore);
        stats.setItemMeta(statsMeta);
        inv.setItem(30, stats);
        ItemStack savedDungeons = new ItemStack(Material.BOOKSHELF);
        ItemMeta savedMeta = savedDungeons.getItemMeta();
        savedMeta.setDisplayName(String.valueOf(ChatColor.LIGHT_PURPLE) + "\ud83d\udcda Saved Dungeons");
        ArrayList<Object> savedLore = new ArrayList<Object>();
        savedLore.add(String.valueOf(ChatColor.GRAY) + "Browse and load your");
        savedLore.add(String.valueOf(ChatColor.GRAY) + "saved dungeon templates");
        savedLore.add("");
        int savedCount = plugin.getSavedDungeonManager().getSavedDungeons().size();
        savedLore.add(String.valueOf(ChatColor.YELLOW) + "Saved: " + String.valueOf(ChatColor.WHITE) + savedCount + String.valueOf(ChatColor.GRAY) + " dungeons");
        savedLore.add("");
        savedLore.add(String.valueOf(ChatColor.YELLOW) + "Features:");
        savedLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Load saved dungeons instantly");
        savedLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "View dungeon details");
        savedLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Manage your templates");
        savedLore.add("");
        savedLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to browse!");
        savedMeta.setLore(savedLore);
        savedDungeons.setItemMeta(savedMeta);
        inv.setItem(32, savedDungeons);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\u2753 Help & Guide");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.GRAY) + "Learn how to use");
        helpLore.add(String.valueOf(ChatColor.GRAY) + "Apex Dungeons effectively");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "Topics:");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Getting Started");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Dungeon Creation");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Portal System");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Commands & Permissions");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click for help!");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(42, help);
        if (player.hasPermission("apexdungeons.admin")) {
            ItemStack admin = new ItemStack(Material.COMMAND_BLOCK);
            ItemMeta adminMeta = admin.getItemMeta();
            adminMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\ud83d\udd27 Admin Tools");
            ArrayList<Object> adminLore = new ArrayList<Object>();
            adminLore.add(String.valueOf(ChatColor.GRAY) + "Administrative functions");
            adminLore.add(String.valueOf(ChatColor.GRAY) + "and server management");
            adminLore.add("");
            adminLore.add(String.valueOf(ChatColor.YELLOW) + "Admin Features:");
            adminLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Force Delete Dungeons");
            adminLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Server Statistics");
            adminLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Plugin Configuration");
            adminLore.add("");
            adminLore.add(String.valueOf(ChatColor.RED) + "\u26a0 Admin Access Required");
            adminMeta.setLore(adminLore);
            admin.setItemMeta(adminMeta);
            inv.setItem(40, admin);
        }
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    Player clicker = (Player)e.getWhoClicked();
                    int slot = e.getRawSlot();
                    switch (slot) {
                        case 20: {
                            clicker.closeInventory();
                            DungeonCreationGUI.open(clicker, plugin);
                            break;
                        }
                        case 22: {
                            clicker.closeInventory();
                            ActiveGUI.open(clicker, plugin);
                            break;
                        }
                        case 24: {
                            clicker.closeInventory();
                            ProceduralDungeonGUI.open(clicker, plugin);
                            break;
                        }
                        case 25: {
                            clicker.closeInventory();
                            BuildingToolsGUI.open(clicker, plugin);
                            break;
                        }
                        case 30: {
                            clicker.closeInventory();
                            StatisticsGUI.open(clicker, plugin);
                            break;
                        }
                        case 32: {
                            clicker.closeInventory();
                            SavedDungeonsGUI.open(clicker, plugin);
                            break;
                        }
                        case 42: {
                            clicker.closeInventory();
                            HelpGUI.open(clicker, plugin);
                            break;
                        }
                        case 40: {
                            if (clicker.hasPermission("apexdungeons.admin")) {
                                clicker.closeInventory();
                                AdminGUI.open(clicker, plugin);
                                break;
                            }
                            clicker.sendMessage(String.valueOf(ChatColor.RED) + "You don't have permission to access admin tools!");
                        }
                    }
                }
            }
        }, (Plugin)plugin);
    }
}

