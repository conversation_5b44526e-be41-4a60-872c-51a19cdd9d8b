/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.NamespacedKey
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.block.Action
 *  org.bukkit.event.player.AsyncPlayerChatEvent
 *  org.bukkit.event.player.PlayerInteractEvent
 *  org.bukkit.event.player.PlayerQuitEvent
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.persistence.PersistentDataType
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.scheduler.BukkitRunnable
 *  org.bukkit.scheduler.BukkitTask
 */
package com.apexdungeons.tools;

import com.apexdungeons.ApexDungeons;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

public class RoomConnector
implements Listener {
    private final ApexDungeons plugin;
    private final NamespacedKey connectorToolKey;
    private final Map<UUID, ConnectionSession> activeSessions = new HashMap<UUID, ConnectionSession>();
    private final Map<UUID, BukkitTask> previewTasks = new HashMap<UUID, BukkitTask>();

    public RoomConnector(ApexDungeons plugin) {
        this.plugin = plugin;
        this.connectorToolKey = new NamespacedKey((Plugin)plugin, "room_connector_tool");
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public ItemStack createConnectorTool() {
        ItemStack tool = new ItemStack(Material.BLAZE_ROD);
        ItemMeta meta = tool.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "Room Connector Tool");
            meta.setLore(Arrays.asList(String.valueOf(ChatColor.GRAY) + "Connect rooms with doorways and passages", "", String.valueOf(ChatColor.GREEN) + "Left-click: " + String.valueOf(ChatColor.WHITE) + "Select first connection point", String.valueOf(ChatColor.GREEN) + "Right-click: " + String.valueOf(ChatColor.WHITE) + "Select second connection point", String.valueOf(ChatColor.GREEN) + "Shift+Right-click: " + String.valueOf(ChatColor.WHITE) + "Cancel connection", "", String.valueOf(ChatColor.YELLOW) + "Connection Types:", String.valueOf(ChatColor.GRAY) + "\u2022 Simple doorway (2x3 opening)", String.valueOf(ChatColor.GRAY) + "\u2022 Wide passage (3x3 opening)", String.valueOf(ChatColor.GRAY) + "\u2022 Corridor (full tunnel between points)"));
            meta.getPersistentDataContainer().set(this.connectorToolKey, PersistentDataType.BYTE, (Object)1);
            tool.setItemMeta(meta);
        }
        return tool;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();
        if (!this.isConnectorTool(item)) {
            return;
        }
        event.setCancelled(true);
        Block targetBlock = player.getTargetBlockExact(10);
        if (targetBlock == null || targetBlock.getType() == Material.AIR) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Look at a block to select connection point!");
            return;
        }
        UUID playerId = player.getUniqueId();
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            if (player.isSneaking()) {
                this.cancelConnection(player);
            } else {
                this.handleSecondPointSelection(player, targetBlock.getLocation());
            }
        } else if (event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK) {
            this.handleFirstPointSelection(player, targetBlock.getLocation());
        }
    }

    private void handleFirstPointSelection(Player player, Location location) {
        UUID playerId = player.getUniqueId();
        this.cancelConnection(player);
        ConnectionSession session = new ConnectionSession(location);
        this.activeSessions.put(playerId, session);
        this.startPreviewTask(player, session);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "First connection point selected!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Right-click to select the second point.");
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);
    }

    private void handleSecondPointSelection(Player player, Location location) {
        UUID playerId = player.getUniqueId();
        ConnectionSession session = this.activeSessions.get(playerId);
        if (session == null) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Select the first connection point first (left-click)!");
            return;
        }
        if (session.getSecondPoint() != null) {
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Connection already has two points! Left-click to start a new connection.");
            return;
        }
        session.setSecondPoint(location);
        this.showConnectionOptions(player, session);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Second connection point selected!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Choose connection type in chat:");
        player.playSound(location, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.4f);
    }

    private void showConnectionOptions(Player player, ConnectionSession session) {
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Connection Options ===");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "1. " + String.valueOf(ChatColor.WHITE) + "Simple Doorway (2x3 opening)");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "2. " + String.valueOf(ChatColor.WHITE) + "Wide Passage (3x3 opening)");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "3. " + String.valueOf(ChatColor.WHITE) + "Full Corridor (tunnel between points)");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Type the number (1, 2, or 3) in chat to create the connection.");
        session.setWaitingForInput(true);
    }

    public void createConnection(Player player, ConnectionType type) {
        UUID playerId = player.getUniqueId();
        ConnectionSession session = this.activeSessions.get(playerId);
        if (session == null || session.getSecondPoint() == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "No valid connection points selected!");
            return;
        }
        Location point1 = session.getFirstPoint();
        Location point2 = session.getSecondPoint();
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Creating " + type.getDisplayName() + "...");
        switch (type.ordinal()) {
            case 0: {
                this.createSimpleDoorway(point1, point2);
                break;
            }
            case 1: {
                this.createWidePassage(point1, point2);
                break;
            }
            case 2: {
                this.createFullCorridor(point1, point2);
            }
        }
        this.cancelConnection(player);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Connection created successfully!");
        player.playSound(point1, Sound.BLOCK_ANVIL_USE, 1.0f, 1.0f);
        player.playSound(point2, Sound.BLOCK_ANVIL_USE, 1.0f, 1.0f);
    }

    private void createSimpleDoorway(Location point1, Location point2) {
        this.createOpening(point1, 2, 3);
        this.createOpening(point2, 2, 3);
        this.addDoorwayDecoration(point1);
        this.addDoorwayDecoration(point2);
    }

    private void createWidePassage(Location point1, Location point2) {
        this.createOpening(point1, 3, 3);
        this.createOpening(point2, 3, 3);
        this.addPassageDecoration(point1);
        this.addPassageDecoration(point2);
    }

    private void createFullCorridor(Location point1, Location point2) {
        this.createOpening(point1, 3, 3);
        this.createOpening(point2, 3, 3);
        this.createTunnel(point1, point2);
    }

    private void createOpening(Location center, int width, int height) {
        World world = center.getWorld();
        if (world == null) {
            return;
        }
        int halfWidth = width / 2;
        int halfHeight = height / 2;
        for (int x = -halfWidth; x <= halfWidth; ++x) {
            for (int y = -halfHeight; y <= halfHeight; ++y) {
                Location blockLoc = center.clone().add((double)x, (double)y, 0.0);
                Block block = world.getBlockAt(blockLoc);
                block.setType(Material.AIR);
            }
        }
    }

    private void addDoorwayDecoration(Location center) {
        Location[] framePositions;
        World world = center.getWorld();
        if (world == null) {
            return;
        }
        for (Location pos : framePositions = new Location[]{center.clone().add(-1.0, -2.0, 0.0), center.clone().add(1.0, -2.0, 0.0), center.clone().add(-1.0, 2.0, 0.0), center.clone().add(1.0, 2.0, 0.0), center.clone().add(0.0, 2.0, 0.0)}) {
            Block block = world.getBlockAt(pos);
            if (block.getType() != Material.AIR && !block.getType().name().contains("STONE")) continue;
            block.setType(Material.STONE_BRICKS);
        }
    }

    private void addPassageDecoration(Location center) {
        World world = center.getWorld();
        if (world == null) {
            return;
        }
        for (int y = -1; y <= 2; ++y) {
            Block leftPillar = world.getBlockAt(center.clone().add(-2.0, (double)y, 0.0));
            Block rightPillar = world.getBlockAt(center.clone().add(2.0, (double)y, 0.0));
            if (leftPillar.getType() == Material.AIR || leftPillar.getType().name().contains("STONE")) {
                leftPillar.setType(Material.STONE_BRICK_STAIRS);
            }
            if (rightPillar.getType() != Material.AIR && !rightPillar.getType().name().contains("STONE")) continue;
            rightPillar.setType(Material.STONE_BRICK_STAIRS);
        }
    }

    private void createTunnel(Location start, Location end) {
        World world = start.getWorld();
        if (world == null || !world.equals((Object)end.getWorld())) {
            return;
        }
        double distance = start.distance(end);
        int steps = (int)Math.ceil(distance);
        for (int i = 0; i <= steps; ++i) {
            double ratio = (double)i / (double)steps;
            Location tunnelPoint = start.clone().add((end.getX() - start.getX()) * ratio, (end.getY() - start.getY()) * ratio, (end.getZ() - start.getZ()) * ratio);
            for (int x = -1; x <= 1; ++x) {
                for (int y = -1; y <= 1; ++y) {
                    for (int z = -1; z <= 1; ++z) {
                        Location blockLoc = tunnelPoint.clone().add((double)x, (double)y, (double)z);
                        Block block = world.getBlockAt(blockLoc);
                        if (x == 0 && z == 0 && y >= -1 && y <= 1) {
                            block.setType(Material.AIR);
                            continue;
                        }
                        if (y == -2) {
                            block.setType(Material.STONE_BRICKS);
                            continue;
                        }
                        if (y == 2) {
                            block.setType(Material.STONE_BRICKS);
                            continue;
                        }
                        if (Math.abs(x) != 1 && Math.abs(z) != 1 || block.getType() != Material.AIR) continue;
                        block.setType(Material.STONE_BRICKS);
                    }
                }
            }
        }
    }

    private void startPreviewTask(final Player player, final ConnectionSession session) {
        final UUID playerId = player.getUniqueId();
        BukkitTask existingTask = this.previewTasks.remove(playerId);
        if (existingTask != null) {
            existingTask.cancel();
        }
        BukkitTask task = new BukkitRunnable(){

            public void run() {
                if (!RoomConnector.this.activeSessions.containsKey(playerId)) {
                    this.cancel();
                    return;
                }
                RoomConnector.this.showConnectionPreview(player, session);
            }
        }.runTaskTimer((Plugin)this.plugin, 0L, 20L);
        this.previewTasks.put(playerId, task);
    }

    private void showConnectionPreview(Player player, ConnectionSession session) {
        World world = session.getFirstPoint().getWorld();
        if (world == null) {
            return;
        }
        Location point1 = session.getFirstPoint().clone().add(0.5, 0.5, 0.5);
        world.spawnParticle(Particle.END_ROD, point1, 5, 0.2, 0.2, 0.2, 0.0);
        if (session.getSecondPoint() != null) {
            Location point2 = session.getSecondPoint().clone().add(0.5, 0.5, 0.5);
            world.spawnParticle(Particle.ENCHANT, point2, 5, 0.2, 0.2, 0.2, 0.0);
            this.showConnectionLine(point1, point2);
        }
    }

    private void showConnectionLine(Location start, Location end) {
        World world = start.getWorld();
        if (world == null) {
            return;
        }
        double distance = start.distance(end);
        int particles = Math.min(20, (int)distance * 2);
        for (int i = 0; i <= particles; ++i) {
            double ratio = (double)i / (double)particles;
            Location linePoint = start.clone().add((end.getX() - start.getX()) * ratio, (end.getY() - start.getY()) * ratio, (end.getZ() - start.getZ()) * ratio);
            world.spawnParticle(Particle.HAPPY_VILLAGER, linePoint, 1, 0.0, 0.0, 0.0, 0.0);
        }
    }

    private void cancelConnection(Player player) {
        UUID playerId = player.getUniqueId();
        this.activeSessions.remove(playerId);
        BukkitTask task = this.previewTasks.remove(playerId);
        if (task != null) {
            task.cancel();
        }
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Connection cancelled.");
    }

    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();
        ConnectionSession session = this.activeSessions.get(playerId);
        if (session != null && session.isWaitingForInput()) {
            String message = event.getMessage().trim();
            try {
                int choice = Integer.parseInt(message);
                ConnectionType type = ConnectionType.fromNumber(choice);
                if (type != null) {
                    event.setCancelled(true);
                    session.setWaitingForInput(false);
                    this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> this.createConnection(player, type));
                } else {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid choice! Please type 1, 2, or 3.");
                }
            }
            catch (NumberFormatException numberFormatException) {
                // empty catch block
            }
        }
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        this.cancelConnection(event.getPlayer());
    }

    private boolean isConnectorTool(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        ItemMeta meta = item.getItemMeta();
        return meta.getPersistentDataContainer().has(this.connectorToolKey, PersistentDataType.BYTE);
    }

    public ConnectionSession getConnectionSession(Player player) {
        return this.activeSessions.get(player.getUniqueId());
    }

    public void shutdown() {
        for (BukkitTask task : this.previewTasks.values()) {
            task.cancel();
        }
        this.previewTasks.clear();
        this.activeSessions.clear();
        this.plugin.getLogger().info("RoomConnector shutdown complete.");
    }

    public static class ConnectionSession {
        private final Location firstPoint;
        private Location secondPoint;
        private boolean waitingForInput = false;

        public ConnectionSession(Location firstPoint) {
            this.firstPoint = firstPoint.clone();
        }

        public Location getFirstPoint() {
            return this.firstPoint.clone();
        }

        public Location getSecondPoint() {
            return this.secondPoint != null ? this.secondPoint.clone() : null;
        }

        public void setSecondPoint(Location secondPoint) {
            this.secondPoint = secondPoint.clone();
        }

        public boolean isWaitingForInput() {
            return this.waitingForInput;
        }

        public void setWaitingForInput(boolean waitingForInput) {
            this.waitingForInput = waitingForInput;
        }
    }

    public static enum ConnectionType {
        SIMPLE_DOORWAY("Simple Doorway"),
        WIDE_PASSAGE("Wide Passage"),
        FULL_CORRIDOR("Full Corridor");

        private final String displayName;

        private ConnectionType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return this.displayName;
        }

        public static ConnectionType fromNumber(int number) {
            switch (number) {
                case 1: {
                    return SIMPLE_DOORWAY;
                }
                case 2: {
                    return WIDE_PASSAGE;
                }
                case 3: {
                    return FULL_CORRIDOR;
                }
            }
            return null;
        }
    }
}

