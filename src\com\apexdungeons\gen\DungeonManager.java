/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.Particle
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.command.CommandSender
 *  org.bukkit.entity.Player
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.scheduler.BukkitRunnable
 */
package com.apexdungeons.gen;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.Blueprint;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.gen.DungeonPreset;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

public class DungeonManager {
    private final ApexDungeons plugin;
    private final Map<String, DungeonInstance> dungeons = new ConcurrentHashMap<String, DungeonInstance>();
    private final Map<String, Blueprint> blueprints = new HashMap<String, Blueprint>();
    private final Map<String, DungeonPreset> presets = new HashMap<String, DungeonPreset>();

    public DungeonManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.loadBlueprints();
        this.loadPresets();
    }

    public void loadBlueprints() {
        File[] files;
        this.blueprints.clear();
        File roomsDir = new File(this.plugin.getDataFolder(), "rooms");
        if (!roomsDir.exists()) {
            roomsDir.mkdirs();
        }
        if ((files = roomsDir.listFiles((dir, name) -> name.endsWith(".yml") || name.endsWith(".yaml"))) != null) {
            for (File file : files) {
                Blueprint bp = Blueprint.load(file);
                if (bp == null) continue;
                this.blueprints.put(bp.getName(), bp);
            }
        }
        this.plugin.getLogger().info("Loaded " + this.blueprints.size() + " room blueprints.");
    }

    public void loadPresets() {
        File[] files;
        this.presets.clear();
        File presetsDir = new File(this.plugin.getDataFolder(), "presets");
        if (!presetsDir.exists()) {
            presetsDir.mkdirs();
        }
        if ((files = presetsDir.listFiles((dir, name) -> name.endsWith(".yml") || name.endsWith(".yaml"))) != null) {
            for (File file : files) {
                DungeonPreset preset = DungeonPreset.load(file);
                if (preset == null) continue;
                String key = file.getName().replace(".yml", "").replace(".yaml", "");
                this.presets.put(key, preset);
            }
        }
        this.plugin.getLogger().info("Loaded " + this.presets.size() + " presets.");
    }

    public void createDungeon(String name, Player player) {
        this.plugin.getLogger().info("[DungeonManager] createDungeon called: name=" + name + ", player=" + player.getName());
        if (this.dungeons.containsKey(name)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "A dungeon with that name already exists!");
            this.plugin.getLogger().warning("[DungeonManager] Dungeon already exists: " + name);
            return;
        }
        if (!this.isValidDungeonName(name)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid dungeon name! Names must be 3-32 characters, alphanumeric with hyphens and underscores only.");
            this.plugin.getLogger().warning("[DungeonManager] Invalid dungeon name: " + name);
            return;
        }
        int maxActiveDungeons = 10;
        if (this.dungeons.size() >= maxActiveDungeons) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Maximum number of active dungeons reached (" + maxActiveDungeons + ")!");
            this.plugin.getLogger().warning("[DungeonManager] Max dungeons reached: " + this.dungeons.size());
            return;
        }
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Creating dungeon world: " + String.valueOf(ChatColor.AQUA) + name);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "This may take a moment...");
        this.plugin.getLogger().info("[DungeonManager] Starting world creation for dungeon: " + name);
        ((CompletableFuture)this.plugin.getWorldManager().createDungeonWorld(name).thenAccept(world -> {
            this.plugin.getLogger().info("[DungeonManager] World creation callback received for: " + name + ", world=" + (world != null ? world.getName() : "null"));
            Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
                this.plugin.getLogger().info("[DungeonManager] Processing on main thread: " + Bukkit.isPrimaryThread());
                if (world != null) {
                    try {
                        this.plugin.getLogger().info("[DungeonManager] World created successfully: " + world.getName());
                        World verifyWorld = Bukkit.getWorld((String)world.getName());
                        if (verifyWorld == null) {
                            player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 World created but not accessible!");
                            this.plugin.getLogger().severe("[DungeonManager] World created but not found in Bukkit: " + world.getName());
                            return;
                        }
                        this.plugin.getLogger().info("[DungeonManager] World verification passed: " + verifyWorld.getName());
                        world.getChunkAt(0, 0).load(true);
                        this.plugin.getLogger().info("[DungeonManager] Spawn chunk loaded");
                        Location spawnLocation = new Location(world, 0.5, 64.0, 0.5);
                        this.plugin.getLogger().info("[DungeonManager] Creating dungeon instance...");
                        DungeonInstance dungeon = new DungeonInstance(this.plugin, name, name, player.getName(), System.currentTimeMillis(), (World)world, spawnLocation, 1);
                        this.plugin.getLogger().info("[DungeonManager] Dungeon instance created, storing in map...");
                        Map<String, DungeonInstance> map = this.dungeons;
                        synchronized (map) {
                            this.dungeons.put(name, dungeon);
                            this.plugin.getLogger().info("[DungeonManager] Dungeon stored in map. Total dungeons: " + this.dungeons.size());
                            this.plugin.getLogger().info("[DungeonManager] Dungeon keys: " + String.valueOf(this.dungeons.keySet()));
                        }
                        DungeonInstance verifyDungeon = this.dungeons.get(name);
                        if (verifyDungeon == null) {
                            this.plugin.getLogger().severe("[DungeonManager] CRITICAL ERROR: Dungeon storage failed immediately!");
                            player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Critical error: Dungeon storage failed!");
                            return;
                        }
                        this.plugin.getLogger().info("[DungeonManager] \u2713 Dungeon storage verified successfully");
                        this.plugin.getWorldManager().registerDungeonWorld(name, (World)world);
                        this.plugin.getLogger().info("[DungeonManager] World mapping registered: " + name + " -> " + world.getName());
                        this.prepareSafeSpawnArea(spawnLocation);
                        this.plugin.getLogger().info("[DungeonManager] Safe spawn area prepared");
                        world.setSpawnLocation(spawnLocation.getBlockX(), spawnLocation.getBlockY(), spawnLocation.getBlockZ());
                        this.plugin.getLogger().info("[DungeonManager] World spawn location set");
                        dungeon.setGenerating(false);
                        this.plugin.getLogger().info("[DungeonManager] Dungeon marked as ready");
                        this.plugin.getEffectsManager().playDungeonCreationEffects(player, name);
                        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Dungeon '" + String.valueOf(ChatColor.AQUA) + name + String.valueOf(ChatColor.GREEN) + "' created successfully!");
                        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.AQUA) + "/dgn tp " + name + String.valueOf(ChatColor.YELLOW) + " to visit your dungeon");
                        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Open the Building Tools GUI to start designing your dungeon!");
                        player.sendMessage(String.valueOf(ChatColor.DARK_GRAY) + "World: " + world.getName());
                        this.plugin.getLogger().info("[DungeonManager] \u2713 Successfully completed dungeon creation: " + name + " by " + player.getName() + " in world: " + world.getName());
                        DungeonInstance finalCheck = this.getDungeon(name);
                        if (finalCheck != null) {
                            this.plugin.getLogger().info("[DungeonManager] \u2713 Final verification passed - dungeon is accessible via getDungeon()");
                        }
                        this.plugin.getLogger().severe("[DungeonManager] \u2717 Final verification FAILED - dungeon not accessible via getDungeon()!");
                    }
                    catch (Exception e) {
                        player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Error during dungeon setup: " + e.getMessage());
                        this.plugin.getLogger().severe("[DungeonManager] Error during dungeon setup for " + name + ": " + e.getMessage());
                        e.printStackTrace();
                        Map<String, DungeonInstance> map = this.dungeons;
                        synchronized (map) {
                            this.dungeons.remove(name);
                        }
                    }
                } else {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Failed to create dungeon world!");
                    player.sendMessage(String.valueOf(ChatColor.YELLOW) + "This may be due to:");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Server configuration or permissions");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Insufficient disk space");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Bukkit/Spigot version compatibility");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "Check the console for detailed error information.");
                    this.plugin.getLogger().severe("[DungeonManager] World creation failed for player: " + player.getName() + ", dungeon: " + name);
                }
            });
        })).exceptionally(throwable -> {
            this.plugin.getLogger().severe("[DungeonManager] Exception in world creation future for " + name + ": " + throwable.getMessage());
            throwable.printStackTrace();
            Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
                player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Critical error during dungeon creation!");
                player.sendMessage(String.valueOf(ChatColor.GRAY) + "Error: " + throwable.getMessage());
                this.plugin.getLogger().severe("[DungeonManager] Critical error during dungeon creation for " + name + ": " + throwable.getMessage());
            });
            return null;
        });
    }

    public void createDungeonWithName(String displayName, String presetName, Player player) {
        if (displayName == null || displayName.trim().isEmpty()) {
            displayName = this.generateAutoName(player.getName());
        }
        if (!this.isValidDungeonName(displayName)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Invalid dungeon name! Names must be 3-32 characters, alphanumeric with spaces, hyphens, and underscores only.");
            return;
        }
        String internalName = this.generateInternalName(displayName);
        if (this.dungeons.containsKey(internalName)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "A dungeon with that name already exists!");
            return;
        }
        this.createDungeonFromPresetWithDisplayName(internalName, displayName, presetName, player);
    }

    public void createProceduralDungeon(String name, Player player) {
        if (this.dungeons.containsKey(name)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "A dungeon with that name already exists.");
            return;
        }
        int maxActive = this.plugin.getConfig().getInt("generation.maxActive", 4);
        if (this.dungeons.size() >= maxActive) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Maximum active dungeons reached. Please remove one before creating another.");
            return;
        }
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Generating procedural dungeon: " + String.valueOf(ChatColor.AQUA) + name);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "This may take a moment...");
        this.plugin.getWorldManager().createDungeonWorld(name).thenAccept(world -> Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
            if (world != null) {
                Location spawnLocation = new Location(world, 0.5, 64.0, 0.5);
                DungeonInstance dungeon = new DungeonInstance(this.plugin, name, name, player.getName(), System.currentTimeMillis(), (World)world, spawnLocation, 1);
                this.dungeons.put(name, dungeon);
                this.plugin.getWorldManager().registerDungeonWorld(name, (World)world);
                this.generateProceduralRooms(dungeon, spawnLocation);
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Procedural dungeon '" + String.valueOf(ChatColor.AQUA) + name + String.valueOf(ChatColor.GREEN) + "' created successfully!");
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Failed to create dungeon world!");
            }
        }));
    }

    private void generateProceduralRooms(DungeonInstance dungeon, Location origin) {
        Random random = new Random();
        ArrayList<RoomPlacement> placements = new ArrayList<RoomPlacement>();
        ArrayList<String> availableRooms = new ArrayList<String>(this.blueprints.keySet());
        if (availableRooms.isEmpty()) {
            this.plugin.getLogger().warning("No room blueprints available for procedural generation");
            return;
        }
        int roomCount = random.nextInt(8) + 5;
        placements.add(new RoomPlacement("starter_room", origin.clone()));
        int gridSize = (int)Math.ceil(Math.sqrt(roomCount));
        int roomSpacing = 30;
        for (int i = 1; i < roomCount; ++i) {
            String roomType = (String)availableRooms.get(random.nextInt(availableRooms.size()));
            int gridX = i % gridSize;
            int gridZ = i / gridSize;
            int offsetX = random.nextInt(10) - 5;
            int offsetZ = random.nextInt(10) - 5;
            int offsetY = random.nextInt(6) - 3;
            Location roomLocation = origin.clone().add((double)(gridX * roomSpacing + offsetX), (double)offsetY, (double)(gridZ * roomSpacing + offsetZ));
            placements.add(new RoomPlacement(roomType, roomLocation));
        }
        if (placements.size() > 1) {
            RoomPlacement lastRoom = (RoomPlacement)placements.get(placements.size() - 1);
            lastRoom.roomType = "boss_room";
        }
        this.placeConnectorBlocks(placements, origin.getWorld());
        this.placeRoomsSequentially(placements, 0, () -> {
            dungeon.setGenerating(false);
            Player creator = this.plugin.getServer().getPlayer(dungeon.getCreator());
            if (creator != null) {
                this.plugin.getEffectsManager().playDungeonCompletionEffects(creator, dungeon);
                creator.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Procedural dungeon generated with " + roomCount + " rooms!");
                creator.sendMessage(String.valueOf(ChatColor.GRAY) + "Each time a player starts this dungeon, rooms will be randomized!");
            }
        });
    }

    private void placeConnectorBlocks(List<RoomPlacement> placements, World world) {
        for (int i = 0; i < placements.size() - 1; ++i) {
            RoomPlacement current = placements.get(i);
            RoomPlacement next = placements.get(i + 1);
            Location midpoint = current.location.clone().add(next.location).multiply(0.5);
            midpoint.setY(current.location.getY() + 1.0);
            world.getBlockAt(midpoint).setType(Material.EMERALD_BLOCK);
            Location signLoc = midpoint.clone().add(0.0, 1.0, 0.0);
            world.getBlockAt(signLoc).setType(Material.OAK_SIGN);
            world.spawnParticle(Particle.HAPPY_VILLAGER, midpoint.add(0.5, 1.0, 0.5), 5, 0.5, 0.5, 0.5, 0.0);
        }
    }

    public void createDungeonFromPresetWithDisplayName(String internalName, String displayName, String presetName, Player player) {
        if (this.dungeons.containsKey(internalName)) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "A dungeon with that name already exists.");
            return;
        }
        int maxActive = this.plugin.getConfig().getInt("generation.maxActive", 4);
        if (this.dungeons.size() >= maxActive) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Maximum active dungeons reached. Please remove one before creating another.");
            return;
        }
        this.plugin.getLogger().info("Starting dungeon creation: " + displayName + " (internal: " + internalName + ") with preset: " + presetName + " for player: " + player.getName());
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Creating isolated world for dungeon \"" + displayName + "\"...");
        ((CompletableFuture)this.plugin.getWorldManager().createDungeonWorld(internalName).thenAccept(world -> {
            this.plugin.getLogger().info("[DungeonManager] Callback received for dungeon: " + internalName + ", world=" + (world != null ? world.getName() : "null") + ", primaryThread=" + Bukkit.isPrimaryThread());
            Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
                this.plugin.getLogger().info("[DungeonManager] Processing callback on main thread: " + Bukkit.isPrimaryThread());
                if (world == null) {
                    this.plugin.getLogger().severe("[DungeonManager] World creation failed for dungeon: " + internalName);
                    player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Failed to create dungeon world!");
                    player.sendMessage(String.valueOf(ChatColor.YELLOW) + "This may be due to server configuration or permissions.");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "Check the console for detailed error information.");
                    this.plugin.getLogger().warning("Dungeon creation failed for player: " + player.getName() + ", dungeon: " + internalName);
                    return;
                }
                this.plugin.getLogger().info("[DungeonManager] World created successfully for dungeon: " + internalName + " -> " + world.getName());
                Location origin = this.findSafeSpawnLocation((World)world);
                this.plugin.getLogger().info("[DungeonManager] Safe spawn location found at: " + origin.getBlockX() + ", " + origin.getBlockY() + ", " + origin.getBlockZ());
                DungeonInstance instance = new DungeonInstance(this.plugin, internalName, displayName, player.getName(), System.currentTimeMillis(), (World)world, origin, 1);
                this.dungeons.put(internalName, instance);
                this.plugin.getLogger().info("[DungeonManager] Dungeon instance created and stored: " + internalName);
                DungeonInstance verifyInstance = this.dungeons.get(internalName);
                if (verifyInstance == null) {
                    this.plugin.getLogger().severe("[DungeonManager] CRITICAL ERROR: Dungeon storage failed immediately after put()!");
                    player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Critical error: Dungeon storage failed!");
                    return;
                }
                this.plugin.getLogger().info("[DungeonManager] \u2713 Dungeon storage verified successfully");
                this.plugin.getLogger().info("[DungeonManager] Total dungeons in map: " + this.dungeons.size());
                this.plugin.getLogger().info("[DungeonManager] All dungeon keys: " + String.valueOf(this.dungeons.keySet()));
                this.plugin.getEffectsManager().playDungeonCreationEffects(player, displayName);
                if (presetName.equalsIgnoreCase("normal")) {
                    instance.setGenerating(false);
                    this.prepareSafeSpawnArea(origin);
                    this.plugin.getEffectsManager().playDungeonCompletionEffects(player, instance);
                    player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Dungeon '" + String.valueOf(ChatColor.AQUA) + displayName + String.valueOf(ChatColor.GREEN) + "' created successfully!");
                    player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.AQUA) + "/dgn tp " + internalName + String.valueOf(ChatColor.YELLOW) + " to visit your dungeon");
                    player.sendMessage(String.valueOf(ChatColor.GRAY) + "You can now build your custom dungeon using the Building Tools!");
                    player.sendMessage(String.valueOf(ChatColor.DARK_GRAY) + "Internal name: " + internalName + " | World: " + world.getName());
                    this.setDungeonSpawn(instance, origin);
                    player.teleport(origin);
                    this.plugin.getLogger().info("[DungeonManager] \u2713 Successfully completed dungeon creation for: " + internalName);
                } else {
                    Blueprint startBlueprint = this.blueprints.get("starter_room");
                    if (startBlueprint == null) {
                        player.sendMessage(String.valueOf(ChatColor.RED) + "Starter room blueprint not found.");
                        return;
                    }
                    this.generateDungeonFromPreset(instance, presetName, origin, () -> {
                        instance.setGenerating(false);
                        this.plugin.getEffectsManager().playDungeonCompletionEffects(player, instance);
                        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Dungeon created successfully!");
                        player.sendMessage(String.valueOf(ChatColor.GRAY) + "You can now place mob spawns, boss spawns, and chest spawns using the Building Tools!");
                        this.setDungeonSpawn(instance, origin);
                        player.teleport(origin);
                    });
                }
            });
        })).exceptionally(throwable -> {
            Bukkit.getScheduler().runTask((Plugin)this.plugin, () -> {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to create dungeon world: " + throwable.getMessage());
                this.plugin.getLogger().severe("Error creating dungeon world: " + throwable.getMessage());
            });
            return null;
        });
    }

    private void placeBlueprintGradually(Blueprint bp, Location origin, final Runnable onComplete) {
        final int maxPerTick = this.plugin.getConfig().getInt("performance.blocksPerTick", 8000);
        final ArrayList<BlockPlacement> blocks = new ArrayList<BlockPlacement>();
        for (int y = 0; y < bp.getHeight(); ++y) {
            for (int z = 0; z < bp.getDepth(); ++z) {
                for (int x = 0; x < bp.getWidth(); ++x) {
                    int id = bp.getLayout()[y][z][x];
                    Material mat = bp.getPalette().getOrDefault(id, Material.AIR);
                    if (mat == Material.AIR) continue;
                    Location loc = origin.clone().add((double)x, (double)y, (double)z);
                    blocks.add(new BlockPlacement(loc, mat));
                }
            }
        }
        new BukkitRunnable(this){
            int index = 0;

            public void run() {
                for (int count = 0; this.index < blocks.size() && count < maxPerTick; ++count) {
                    BlockPlacement bpSet = (BlockPlacement)blocks.get(this.index++);
                    Block block = bpSet.location.getBlock();
                    block.setType(bpSet.material, false);
                }
                if (this.index >= blocks.size()) {
                    this.cancel();
                    if (onComplete != null) {
                        onComplete.run();
                    }
                }
            }
        }.runTaskTimer((Plugin)this.plugin, 1L, 1L);
    }

    public void removeDungeon(String name, CommandSender sender) {
        DungeonInstance inst = this.dungeons.remove(name);
        if (inst != null) {
            Location mainSpawn = ((World)this.plugin.getServer().getWorlds().get(0)).getSpawnLocation();
            for (UUID playerId : inst.getPlayers()) {
                Player player = this.plugin.getServer().getPlayer(playerId);
                if (player == null) continue;
                player.teleport(mainSpawn);
                player.sendMessage(String.valueOf(ChatColor.YELLOW) + "The dungeon \"" + inst.getDisplayName() + "\" has been removed.");
            }
            this.plugin.getWorldManager().deleteDungeonWorld(name).thenAccept(success -> {
                if (success.booleanValue()) {
                    sender.sendMessage(String.valueOf(ChatColor.GREEN) + "Removed dungeon \"" + inst.getDisplayName() + "\" and cleaned up its world.");
                    this.plugin.getLogger().info("Successfully cleaned up world for dungeon: " + name);
                } else {
                    sender.sendMessage(String.valueOf(ChatColor.YELLOW) + "Removed dungeon \"" + inst.getDisplayName() + "\" but failed to clean up world.");
                    this.plugin.getLogger().warning("Failed to clean up world for dungeon: " + name);
                }
            });
        } else {
            sender.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon " + name + " not found.");
        }
    }

    public void purgeDungeons() {
        int count = this.dungeons.size();
        this.plugin.getLogger().warning("[DungeonManager] PURGING ALL DUNGEONS! Count: " + count);
        this.plugin.getLogger().warning("[DungeonManager] Dungeons being purged: " + String.valueOf(this.dungeons.keySet()));
        this.dungeons.clear();
        this.plugin.getLogger().warning("[DungeonManager] All dungeons purged! Cleared " + count + " dungeons.");
    }

    public void tpToDungeon(String name, Player player) {
        DungeonInstance inst = this.dungeons.get(name);
        if (inst == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon not found: " + name);
            return;
        }
        player.teleport(inst.getOrigin().clone().add(0.5, 1.0, 0.5));
        inst.addPlayer(player);
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Teleported to dungeon " + name);
    }

    public List<DungeonInstance> getActiveDungeons() {
        return new ArrayList<DungeonInstance>(this.dungeons.values());
    }

    public DungeonInstance getDungeonByPlayer(Player player) {
        for (DungeonInstance inst : this.dungeons.values()) {
            if (!inst.containsPlayer(player)) continue;
            return inst;
        }
        return null;
    }

    public DungeonPreset loadPreset(String name) {
        return this.presets.get(name);
    }

    private void generateDungeonFromPreset(DungeonInstance instance, String presetName, Location origin, Runnable onComplete) {
        DungeonPreset preset = this.presets.get(presetName);
        if (preset == null) {
            this.plugin.getLogger().warning("Preset not found: " + presetName + ", using default generation");
            Blueprint startBlueprint = this.blueprints.get("starter_room");
            if (startBlueprint != null) {
                this.placeBlueprintGradually(startBlueprint, origin, onComplete);
            } else {
                onComplete.run();
            }
            return;
        }
        this.generateMultiRoomDungeon(instance, preset, origin, onComplete);
    }

    private void generateMultiRoomDungeon(DungeonInstance instance, DungeonPreset preset, Location origin, Runnable onComplete) {
        ArrayList<String> availableRooms = new ArrayList<String>();
        block12: for (String theme : preset.getThemes()) {
            switch (theme.toLowerCase()) {
                case "crypt": {
                    availableRooms.add("starter_room");
                    availableRooms.add("boss_room");
                    availableRooms.add("corridor");
                    continue block12;
                }
                case "castle": {
                    availableRooms.add("castle_entrance");
                    availableRooms.add("boss_room");
                    availableRooms.add("corridor");
                    continue block12;
                }
                case "cave": {
                    availableRooms.add("starter_room");
                    availableRooms.add("corridor");
                    continue block12;
                }
                case "temple": {
                    availableRooms.add("ancient_temple");
                    availableRooms.add("boss_room");
                    continue block12;
                }
            }
            availableRooms.add("starter_room");
        }
        if (availableRooms.isEmpty()) {
            availableRooms.add("starter_room");
        }
        List<RoomPlacement> roomPlacements = this.generateRoomLayout(preset, origin, availableRooms);
        this.placeRoomsSequentially(roomPlacements, 0, onComplete);
    }

    private List<RoomPlacement> generateRoomLayout(DungeonPreset preset, Location origin, List<String> availableRooms) {
        ArrayList<RoomPlacement> placements = new ArrayList<RoomPlacement>();
        Random random = new Random();
        int maxRooms = preset.getMaxRooms();
        placements.add(new RoomPlacement("starter_room", origin.clone()));
        int gridSize = (int)Math.ceil(Math.sqrt(maxRooms));
        int roomSpacing = 50;
        for (int i = 1; i < maxRooms; ++i) {
            String roomType = availableRooms.get(random.nextInt(availableRooms.size()));
            int gridX = i % gridSize;
            int gridZ = i / gridSize;
            int offsetX = random.nextInt(20) - 10;
            int offsetZ = random.nextInt(20) - 10;
            int offsetY = random.nextInt(10) - 5;
            int x = gridX * roomSpacing + offsetX;
            int z = gridZ * roomSpacing + offsetZ;
            int y = offsetY;
            Location roomLocation = origin.clone().add((double)x, (double)y, (double)z);
            placements.add(new RoomPlacement(roomType, roomLocation));
        }
        if (maxRooms > 1) {
            int midPoint;
            RoomPlacement lastRoom = (RoomPlacement)placements.get(placements.size() - 1);
            lastRoom.roomType = "boss_room";
            if (maxRooms > 20 && (midPoint = maxRooms / 2) < placements.size()) {
                ((RoomPlacement)placements.get((int)midPoint)).roomType = "boss_room";
            }
            if (maxRooms > 30) {
                for (int i = 10; i < maxRooms; i += 15) {
                    if (i >= placements.size()) continue;
                    ((RoomPlacement)placements.get((int)i)).roomType = "boss_room";
                }
            }
        }
        return placements;
    }

    private void placeRoomsSequentially(List<RoomPlacement> placements, int index, Runnable onComplete) {
        if (index >= placements.size()) {
            onComplete.run();
            return;
        }
        RoomPlacement placement = placements.get(index);
        Blueprint blueprint = this.blueprints.get(placement.roomType);
        if (blueprint != null) {
            this.placeBlueprintGradually(blueprint, placement.location, () -> {
                long delay = Math.max(2L, 10L - (long)(placements.size() / 10));
                this.plugin.getServer().getScheduler().runTaskLater((Plugin)this.plugin, () -> this.placeRoomsSequentially(placements, index + 1, onComplete), delay);
            });
        } else {
            this.placeRoomsSequentially(placements, index + 1, onComplete);
        }
    }

    public void placeRoomAt(Player player, String roomName) {
        Blueprint bp = this.blueprints.get(roomName);
        if (bp == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Room not found: " + roomName);
            return;
        }
        Location target = player.getTargetBlockExact(5) != null ? player.getTargetBlockExact(5).getLocation().add(0.0, 1.0, 0.0) : player.getLocation().add(0.0, 1.0, 0.0);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Placing room " + roomName + "...");
        this.placeBlueprintGradually(bp, target, () -> player.sendMessage(String.valueOf(ChatColor.GREEN) + "Room placed!"));
    }

    public List<String> listRoomNames() {
        return new ArrayList<String>(this.blueprints.keySet());
    }

    public boolean isValidDungeonName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        String trimmed = name.trim();
        if (trimmed.length() < 3 || trimmed.length() > 32) {
            return false;
        }
        return trimmed.matches("^[a-zA-Z0-9 _-]+$");
    }

    public String generateInternalName(String displayName) {
        String base = displayName.toLowerCase().replaceAll("[^a-z0-9_-]", "_").replaceAll("_{2,}", "_").replaceAll("^_+|_+$", "");
        if (base.isEmpty()) {
            base = "dungeon";
        }
        Object candidate = base;
        int counter = 1;
        while (this.dungeons.containsKey(candidate)) {
            candidate = base + "_" + counter;
            ++counter;
        }
        return candidate;
    }

    private String generateAutoName(String playerName) {
        String[] adjectives = new String[]{"Epic", "Mystic", "Ancient", "Dark", "Crystal", "Golden", "Shadow", "Frozen", "Burning", "Sacred"};
        String[] nouns = new String[]{"Dungeon", "Crypt", "Temple", "Fortress", "Cavern", "Sanctum", "Labyrinth", "Vault", "Chamber", "Ruins"};
        String adjective = adjectives[(int)(Math.random() * (double)adjectives.length)];
        String noun = nouns[(int)(Math.random() * (double)nouns.length)];
        return adjective + " " + noun;
    }

    public Map<String, DungeonInstance> getDungeons() {
        return new HashMap<String, DungeonInstance>(this.dungeons);
    }

    public DungeonInstance getDungeon(String name) {
        DungeonInstance result = this.dungeons.get(name);
        this.plugin.getLogger().info("[DungeonManager] getDungeon(" + name + ") = " + (result != null ? "found" : "null"));
        this.plugin.getLogger().info("[DungeonManager] Available dungeons: " + String.valueOf(this.dungeons.keySet()));
        return result;
    }

    public List<String> listDungeonNames() {
        return new ArrayList<String>(this.dungeons.keySet());
    }

    public void addDungeon(DungeonInstance dungeon) {
        this.dungeons.put(dungeon.getName(), dungeon);
        this.plugin.getLogger().info("Added dungeon: " + dungeon.getName());
    }

    private Location findSafeSpawnLocation(World world) {
        Location above2;
        Location above;
        world.getChunkAt(0, 0);
        Location spawn = new Location(world, 0.0, 64.0, 0.0);
        Location ground = new Location(world, 0.0, 63.0, 0.0);
        if (!ground.getBlock().getType().isSolid()) {
            ground.getBlock().setType(Material.GRASS_BLOCK);
        }
        if (!(above = spawn.clone().add(0.0, 0.0, 0.0)).getBlock().getType().isAir()) {
            above.getBlock().setType(Material.AIR);
        }
        if (!(above2 = spawn.clone().add(0.0, 1.0, 0.0)).getBlock().getType().isAir()) {
            above2.getBlock().setType(Material.AIR);
        }
        return spawn.add(0.5, 0.0, 0.5);
    }

    private void prepareSafeSpawnArea(Location center) {
        World world = center.getWorld();
        int centerX = center.getBlockX();
        int centerZ = center.getBlockZ();
        int spawnY = 64;
        for (int x = centerX - 2; x <= centerX + 2; ++x) {
            for (int z = centerZ - 2; z <= centerZ + 2; ++z) {
                Location surface = new Location(world, (double)x, (double)spawnY, (double)z);
                surface.getBlock().setType(Material.GRASS_BLOCK);
                Location air1 = new Location(world, (double)x, (double)(spawnY + 1), (double)z);
                Location air2 = new Location(world, (double)x, (double)(spawnY + 2), (double)z);
                air1.getBlock().setType(Material.AIR);
                air2.getBlock().setType(Material.AIR);
                Location dirt = new Location(world, (double)x, (double)(spawnY - 1), (double)z);
                dirt.getBlock().setType(Material.DIRT);
                Location stone = new Location(world, (double)x, (double)(spawnY - 2), (double)z);
                stone.getBlock().setType(Material.STONE);
            }
        }
        this.plugin.getLogger().info("Prepared safe spawn area at " + centerX + ", " + spawnY + ", " + centerZ);
    }

    private void setDungeonSpawn(DungeonInstance instance, Location spawn) {
        World world = instance.getWorld();
        world.setSpawnLocation(spawn.getBlockX(), spawn.getBlockY(), spawn.getBlockZ());
        this.plugin.getLogger().info("Set spawn location for dungeon " + instance.getName() + " at " + spawn.getBlockX() + ", " + spawn.getBlockY() + ", " + spawn.getBlockZ());
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    public boolean recoverDungeonFromWorld(String dungeonName, Player contextPlayer) {
        try {
            World world = this.plugin.getWorldManager().getDungeonWorld(dungeonName);
            if (world == null) {
                for (World w : Bukkit.getWorlds()) {
                    if (!w.getName().startsWith("dungeon_" + dungeonName)) continue;
                    world = w;
                    this.plugin.getWorldManager().registerDungeonWorld(dungeonName, w);
                    this.plugin.getLogger().warning("[DungeonManager] Re-registered world mapping for '" + dungeonName + "' -> " + w.getName());
                    break;
                }
            }
            if (world == null) {
                this.plugin.getLogger().warning("[DungeonManager] recoverDungeonFromWorld: No world found for '" + dungeonName + "'");
                return false;
            }
            if (this.dungeons.containsKey(dungeonName)) {
                return true;
            }
            Location origin = this.findSafeSpawnLocation(world);
            String creator = contextPlayer != null ? contextPlayer.getName() : "recovered";
            DungeonInstance instance = new DungeonInstance(this.plugin, dungeonName, dungeonName, creator, System.currentTimeMillis(), world, origin, 1);
            instance.setGenerating(false);
            this.setDungeonSpawn(instance, origin);
            Map<String, DungeonInstance> map = this.dungeons;
            synchronized (map) {
                this.dungeons.put(dungeonName, instance);
            }
            this.plugin.getLogger().info("[DungeonManager] Recovered dungeon '" + dungeonName + "' from world '" + world.getName() + "'");
            return true;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[DungeonManager] Failed to recover dungeon '" + dungeonName + "': " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public void shutdown() {
        this.dungeons.clear();
    }

    private static class RoomPlacement {
        String roomType;
        Location location;

        RoomPlacement(String roomType, Location location) {
            this.roomType = roomType;
            this.location = location;
        }
    }

    private static class BlockPlacement {
        final Location location;
        final Material material;

        BlockPlacement(Location loc, Material mat) {
            this.location = loc;
            this.material = mat;
        }
    }
}

