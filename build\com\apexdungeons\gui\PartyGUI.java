/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.OfflinePlayer
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.inventory.meta.SkullMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.party.Party;
import java.util.ArrayList;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.Plugin;

public class PartyGUI
implements Listener {
    private final ApexDungeons plugin;
    private static final String GUI_TITLE = String.valueOf(ChatColor.DARK_PURPLE) + "Party Management";

    public PartyGUI(ApexDungeons plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
    }

    public void openPartyGUI(Player player) {
        Party party = this.plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "You are not in a party! Use /dgn party create to create one.");
            return;
        }
        Inventory inv = Bukkit.createInventory(null, (int)27, (String)GUI_TITLE);
        this.fillBackground(inv);
        this.addPartyMembers(inv, party, player);
        this.addActionButtons(inv, party, player);
        player.openInventory(inv);
    }

    private void fillBackground(Inventory inv) {
        ItemStack glass = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        for (int i = 0; i < inv.getSize(); ++i) {
            inv.setItem(i, glass);
        }
    }

    private void addPartyMembers(Inventory inv, Party party, Player viewer) {
        List<Player> members = party.getMembers();
        int[] memberSlots = new int[]{10, 11, 12, 13};
        for (int i = 0; i < Math.min(members.size(), 4); ++i) {
            Player member = members.get(i);
            ItemStack head = this.createPlayerHead(member, party, viewer);
            inv.setItem(memberSlots[i], head);
        }
    }

    private ItemStack createPlayerHead(Player player, Party party, Player viewer) {
        ItemStack head = new ItemStack(Material.PLAYER_HEAD);
        SkullMeta meta = (SkullMeta)head.getItemMeta();
        meta.setOwningPlayer((OfflinePlayer)player);
        if (party.isLeader(player)) {
            meta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udc51 " + player.getName() + " (Leader)");
        } else {
            meta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udc64 " + player.getName());
        }
        ArrayList<Object> lore = new ArrayList<Object>();
        lore.add("");
        lore.add(String.valueOf(ChatColor.GRAY) + "Status: " + (player.isOnline() ? String.valueOf(ChatColor.GREEN) + "Online" : String.valueOf(ChatColor.RED) + "Offline"));
        if (player.isOnline()) {
            lore.add(String.valueOf(ChatColor.GRAY) + "Health: " + String.valueOf(ChatColor.RED) + "\u2764 " + (int)player.getHealth() + "/20");
            lore.add(String.valueOf(ChatColor.GRAY) + "Level: " + String.valueOf(ChatColor.AQUA) + player.getLevel());
            if (player.getWorld() != null) {
                lore.add(String.valueOf(ChatColor.GRAY) + "World: " + String.valueOf(ChatColor.YELLOW) + player.getWorld().getName());
            }
        }
        lore.add("");
        if (party.isLeader(viewer) && !player.equals((Object)viewer)) {
            lore.add(String.valueOf(ChatColor.YELLOW) + "\ud83d\udd27 Leader Actions:");
            lore.add(String.valueOf(ChatColor.RED) + "\u2022 Right-click to kick");
            lore.add(String.valueOf(ChatColor.BLUE) + "\u2022 Shift+Right-click for transfer leadership");
        } else if (player.equals((Object)viewer)) {
            lore.add(String.valueOf(ChatColor.AQUA) + "This is you!");
        }
        meta.setLore(lore);
        head.setItemMeta((ItemMeta)meta);
        return head;
    }

    private void addActionButtons(Inventory inv, Party party, Player viewer) {
        ItemStack infoButton = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = infoButton.getItemMeta();
        infoMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udccb Party Information");
        ArrayList<Object> infoLore = new ArrayList<Object>();
        infoLore.add("");
        infoLore.add(String.valueOf(ChatColor.GRAY) + "Party Size: " + String.valueOf(ChatColor.WHITE) + party.getSize() + "/" + party.getMaxSize());
        infoLore.add(String.valueOf(ChatColor.GRAY) + "Available Slots: " + String.valueOf(ChatColor.WHITE) + party.getAvailableSlots());
        infoLore.add(String.valueOf(ChatColor.GRAY) + "Leader: " + String.valueOf(ChatColor.GOLD) + party.getLeader().getName());
        infoLore.add("");
        long createdMinutesAgo = (System.currentTimeMillis() - party.getCreatedTime()) / 60000L;
        infoLore.add(String.valueOf(ChatColor.GRAY) + "Created: " + String.valueOf(ChatColor.WHITE) + createdMinutesAgo + " minutes ago");
        infoMeta.setLore(infoLore);
        infoButton.setItemMeta(infoMeta);
        inv.setItem(4, infoButton);
        if (party.isLeader(viewer) && !party.isFull()) {
            ItemStack inviteButton = new ItemStack(Material.EMERALD);
            ItemMeta inviteMeta = inviteButton.getItemMeta();
            inviteMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u2795 Invite Player");
            ArrayList<Object> inviteLore = new ArrayList<Object>();
            inviteLore.add("");
            inviteLore.add(String.valueOf(ChatColor.GRAY) + "Click to close GUI and use:");
            inviteLore.add(String.valueOf(ChatColor.YELLOW) + "/dgn party invite <player>");
            inviteLore.add("");
            inviteLore.add(String.valueOf(ChatColor.AQUA) + "Available slots: " + party.getAvailableSlots());
            inviteMeta.setLore(inviteLore);
            inviteButton.setItemMeta(inviteMeta);
            inv.setItem(19, inviteButton);
        }
        ItemStack leaveButton = new ItemStack(Material.BARRIER);
        ItemMeta leaveMeta = leaveButton.getItemMeta();
        if (party.isLeader(viewer)) {
            leaveMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\ud83d\udeaa Disband Party");
            leaveLore = new ArrayList<Object>();
            leaveLore.add("");
            leaveLore.add(String.valueOf(ChatColor.GRAY) + "As the leader, leaving will");
            leaveLore.add(String.valueOf(ChatColor.GRAY) + "disband the entire party.");
            leaveLore.add("");
            leaveLore.add(String.valueOf(ChatColor.RED) + "\u26a0 This action cannot be undone!");
            leaveMeta.setLore(leaveLore);
        } else {
            leaveMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\ud83d\udeaa Leave Party");
            leaveLore = new ArrayList();
            leaveLore.add("");
            leaveLore.add(String.valueOf(ChatColor.GRAY) + "Leave the current party.");
            leaveLore.add(String.valueOf(ChatColor.GRAY) + "You can be invited back later.");
            leaveMeta.setLore(leaveLore);
        }
        leaveButton.setItemMeta(leaveMeta);
        inv.setItem(22, leaveButton);
        if (party.isLeader(viewer)) {
            ItemStack startButton = new ItemStack(Material.DIAMOND_SWORD);
            ItemMeta startMeta = startButton.getItemMeta();
            startMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\u2694 Start Dungeon");
            ArrayList<Object> startLore = new ArrayList<Object>();
            startLore.add("");
            startLore.add(String.valueOf(ChatColor.GRAY) + "Start a dungeon run with your party.");
            startLore.add(String.valueOf(ChatColor.GRAY) + "All members will be teleported together.");
            startLore.add("");
            startLore.add(String.valueOf(ChatColor.YELLOW) + "Click to see available dungeons!");
            startMeta.setLore(startLore);
            startButton.setItemMeta(startMeta);
            inv.setItem(25, startButton);
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!event.getView().getTitle().equals(GUI_TITLE)) {
            return;
        }
        event.setCancelled(true);
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }
        Player player = (Player)event.getWhoClicked();
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) {
            return;
        }
        Party party = this.plugin.getPartyManager().getPlayerParty(player);
        if (party == null) {
            player.closeInventory();
            player.sendMessage(String.valueOf(ChatColor.RED) + "You are no longer in a party!");
            return;
        }
        int slot = event.getSlot();
        if (slot >= 10 && slot <= 13) {
            this.handleMemberClick(player, party, slot, event.isRightClick(), event.isShiftClick());
        } else if (slot == 19) {
            if (party.isLeader(player) && !party.isFull()) {
                player.closeInventory();
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "Use " + String.valueOf(ChatColor.YELLOW) + "/dgn party invite <player>" + String.valueOf(ChatColor.GREEN) + " to invite someone to your party!");
            }
        } else if (slot == 22) {
            player.closeInventory();
            if (!this.plugin.getPartyManager().leaveParty(player)) {
                player.sendMessage(String.valueOf(ChatColor.RED) + "Failed to leave party!");
            }
        } else if (slot == 25 && party.isLeader(player)) {
            player.closeInventory();
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "Use " + String.valueOf(ChatColor.YELLOW) + "/dgn start <dungeon>" + String.valueOf(ChatColor.GREEN) + " to start a dungeon with your party!");
        }
    }

    private void handleMemberClick(Player clicker, Party party, int slot, boolean rightClick, boolean shiftClick) {
        int memberIndex = slot - 10;
        List<Player> members = party.getMembers();
        if (memberIndex >= members.size()) {
            return;
        }
        Player target = members.get(memberIndex);
        if (!party.isLeader(clicker) || target.equals((Object)clicker)) {
            return;
        }
        if (rightClick && shiftClick) {
            clicker.closeInventory();
            if (party.transferLeadership(target)) {
                clicker.sendMessage(String.valueOf(ChatColor.GREEN) + "Transferred party leadership to " + target.getName() + "!");
                target.sendMessage(String.valueOf(ChatColor.GOLD) + "You are now the party leader!");
                for (Player member : party.getMembers()) {
                    if (member.equals((Object)clicker) || member.equals((Object)target)) continue;
                    member.sendMessage(String.valueOf(ChatColor.YELLOW) + clicker.getName() + " transferred leadership to " + target.getName());
                }
            } else {
                clicker.sendMessage(String.valueOf(ChatColor.RED) + "Failed to transfer leadership!");
            }
        } else if (rightClick) {
            clicker.closeInventory();
            if (!this.plugin.getPartyManager().kickPlayer(clicker, target)) {
                clicker.sendMessage(String.valueOf(ChatColor.RED) + "Failed to kick player!");
            }
        }
    }
}

