/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.event.player.AsyncPlayerChatEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gui.EnhancedMainGUI;
import java.lang.invoke.CallSite;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class ProceduralDungeonGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_PURPLE) + "\ud83c\udfb2 Procedural Dungeon Generator";
    private static final Map<UUID, Boolean> awaitingNameInput = new HashMap<UUID, Boolean>();

    public static void open(Player player, ApexDungeons plugin) {
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)54, (String)GUI_NAME);
        ProceduralDungeonGUI.fillBackground(inv);
        ProceduralDungeonGUI.createMainContent(inv, plugin);
        ProceduralDungeonGUI.createNavigationButtons(inv);
        player.openInventory(inv);
        ProceduralDungeonGUI.registerEventListener(plugin);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 26, 27, 35, 36, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53}) {
            inv.setItem(slot, background);
        }
    }

    private static void createMainContent(Inventory inv, ApexDungeons plugin) {
        ItemStack header = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta headerMeta = header.getItemMeta();
        headerMeta.setDisplayName(String.valueOf(ChatColor.DARK_PURPLE) + "\ud83c\udfb2 Procedural Dungeon Generator");
        ArrayList<Object> headerLore = new ArrayList<Object>();
        headerLore.add(String.valueOf(ChatColor.GRAY) + "Create dungeons that randomize every time!");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.YELLOW) + "\u2728 How it works:");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Generate a dungeon with random rooms");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Rooms are connected with special blocks");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Each time a player starts, rooms randomize");
        headerLore.add(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Layout is always perfect - never breaks");
        headerLore.add("");
        headerLore.add(String.valueOf(ChatColor.GOLD) + "Made by Vexy");
        headerMeta.setLore(headerLore);
        header.setItemMeta(headerMeta);
        inv.setItem(4, header);
        ItemStack generate = new ItemStack(Material.NETHER_STAR);
        ItemMeta generateMeta = generate.getItemMeta();
        generateMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u2728 Generate Procedural Dungeon");
        ArrayList<Object> generateLore = new ArrayList<Object>();
        generateLore.add(String.valueOf(ChatColor.GRAY) + "Click to start creating your");
        generateLore.add(String.valueOf(ChatColor.GRAY) + "procedural dungeon!");
        generateLore.add("");
        generateLore.add(String.valueOf(ChatColor.YELLOW) + "\ud83c\udfaf What you'll get:");
        generateLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "5-12 randomly placed rooms");
        generateLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Emerald connector blocks between rooms");
        generateLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Perfect layout every time");
        generateLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Rooms randomize on each dungeon start");
        generateLore.add("");
        generateLore.add(String.valueOf(ChatColor.GREEN) + "\u25b6 Click to generate!");
        generateMeta.setLore(generateLore);
        generate.setItemMeta(generateMeta);
        inv.setItem(22, generate);
        ProceduralDungeonGUI.createFeatureShowcase(inv);
        ProceduralDungeonGUI.createExamples(inv);
    }

    private static void createFeatureShowcase(Inventory inv) {
        ItemStack randomRooms = new ItemStack(Material.STRUCTURE_BLOCK);
        ItemMeta randomMeta = randomRooms.getItemMeta();
        randomMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83c\udfd7\ufe0f Random Room Generation");
        ArrayList<Object> randomLore = new ArrayList<Object>();
        randomLore.add(String.valueOf(ChatColor.GRAY) + "Rooms are selected randomly from");
        randomLore.add(String.valueOf(ChatColor.GRAY) + "all available blueprints");
        randomLore.add("");
        randomLore.add(String.valueOf(ChatColor.YELLOW) + "Features:");
        randomLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Uses all room types");
        randomLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Smart grid placement");
        randomLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "No room overlaps");
        randomLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Always ends with boss room");
        randomMeta.setLore(randomLore);
        randomRooms.setItemMeta(randomMeta);
        inv.setItem(19, randomRooms);
        ItemStack connectors = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta connectorMeta = connectors.getItemMeta();
        connectorMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udd17 Smart Connectors");
        ArrayList<Object> connectorLore = new ArrayList<Object>();
        connectorLore.add(String.valueOf(ChatColor.GRAY) + "Special emerald blocks connect");
        connectorLore.add(String.valueOf(ChatColor.GRAY) + "rooms perfectly every time");
        connectorLore.add("");
        connectorLore.add(String.valueOf(ChatColor.YELLOW) + "Benefits:");
        connectorLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Never breaks or fails");
        connectorLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Visual connection indicators");
        connectorLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Particle effects for guidance");
        connectorLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Automatic pathfinding");
        connectorMeta.setLore(connectorLore);
        connectors.setItemMeta(connectorMeta);
        inv.setItem(21, connectors);
        ItemStack randomize = new ItemStack(Material.ENDER_PEARL);
        ItemMeta randomizeMeta = randomize.getItemMeta();
        randomizeMeta.setDisplayName(String.valueOf(ChatColor.LIGHT_PURPLE) + "\ud83c\udfb2 Dynamic Randomization");
        ArrayList<Object> randomizeLore = new ArrayList<Object>();
        randomizeLore.add(String.valueOf(ChatColor.GRAY) + "Every time a player starts the");
        randomizeLore.add(String.valueOf(ChatColor.GRAY) + "dungeon, rooms are re-randomized");
        randomizeLore.add("");
        randomizeLore.add(String.valueOf(ChatColor.YELLOW) + "Experience:");
        randomizeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Infinite replayability");
        randomizeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Unique layouts every run");
        randomizeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Challenging unpredictability");
        randomizeLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Never gets boring");
        randomizeMeta.setLore(randomizeLore);
        randomize.setItemMeta(randomizeMeta);
        inv.setItem(23, randomize);
        ItemStack perfect = new ItemStack(Material.DIAMOND);
        ItemMeta perfectMeta = perfect.getItemMeta();
        perfectMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udc8e Perfect Layouts");
        ArrayList<Object> perfectLore = new ArrayList<Object>();
        perfectLore.add(String.valueOf(ChatColor.GRAY) + "Advanced algorithms ensure");
        perfectLore.add(String.valueOf(ChatColor.GRAY) + "layouts never break or fail");
        perfectLore.add("");
        perfectLore.add(String.valueOf(ChatColor.YELLOW) + "Guarantees:");
        perfectLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "All rooms are accessible");
        perfectLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "No dead ends or loops");
        perfectLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Proper spacing between rooms");
        perfectLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Boss room always at the end");
        perfectMeta.setLore(perfectLore);
        perfect.setItemMeta(perfectMeta);
        inv.setItem(25, perfect);
    }

    private static void createExamples(Inventory inv) {
        ItemStack example1 = new ItemStack(Material.MAP);
        ItemMeta example1Meta = example1.getItemMeta();
        example1Meta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccb Example: Adventure Dungeon");
        ArrayList<Object> example1Lore = new ArrayList<Object>();
        example1Lore.add(String.valueOf(ChatColor.GRAY) + "A typical procedural dungeon might have:");
        example1Lore.add("");
        example1Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Starter Room (entrance)");
        example1Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "3-4 Challenge Rooms");
        example1Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "2-3 Corridor Connections");
        example1Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "1 Boss Room (finale)");
        example1Lore.add("");
        example1Lore.add(String.valueOf(ChatColor.GRAY) + "Next run: completely different layout!");
        example1Meta.setLore(example1Lore);
        example1.setItemMeta(example1Meta);
        inv.setItem(37, example1);
        ItemStack example2 = new ItemStack(Material.FILLED_MAP);
        ItemMeta example2Meta = example2.getItemMeta();
        example2Meta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\ud83d\udccb Example: Epic Quest");
        ArrayList<Object> example2Lore = new ArrayList<Object>();
        example2Lore.add(String.valueOf(ChatColor.GRAY) + "A larger procedural dungeon could have:");
        example2Lore.add("");
        example2Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Starter Room");
        example2Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "6-8 Mixed Room Types");
        example2Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Multiple Corridors");
        example2Lore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Epic Boss Room");
        example2Lore.add("");
        example2Lore.add(String.valueOf(ChatColor.GRAY) + "Perfect for long adventures!");
        example2Meta.setLore(example2Lore);
        example2.setItemMeta(example2Meta);
        inv.setItem(39, example2);
        ItemStack tips = new ItemStack(Material.KNOWLEDGE_BOOK);
        ItemMeta tipsMeta = tips.getItemMeta();
        tipsMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\ud83d\udca1 Pro Tips");
        ArrayList<Object> tipsLore = new ArrayList<Object>();
        tipsLore.add(String.valueOf(ChatColor.GRAY) + "Get the most out of procedural dungeons:");
        tipsLore.add("");
        tipsLore.add(String.valueOf(ChatColor.YELLOW) + "\u2728 Tips:");
        tipsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Add mob spawns after generation");
        tipsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Place loot chests in key rooms");
        tipsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Test the dungeon before releasing");
        tipsLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Use building tools for customization");
        tipsLore.add("");
        tipsLore.add(String.valueOf(ChatColor.GRAY) + "Remember: rooms randomize on each start!");
        tipsMeta.setLore(tipsLore);
        tips.setItemMeta(tipsMeta);
        inv.setItem(41, tips);
    }

    private static void createNavigationButtons(Inventory inv) {
        ItemStack back = new ItemStack(Material.ARROW);
        ItemMeta backMeta = back.getItemMeta();
        backMeta.setDisplayName(String.valueOf(ChatColor.YELLOW) + "\u2190 Back to Main Menu");
        ArrayList<CallSite> backLore = new ArrayList<CallSite>();
        backLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Return to the main dungeon menu")));
        backMeta.setLore(backLore);
        back.setItemMeta(backMeta);
        inv.setItem(45, back);
        ItemStack help = new ItemStack(Material.BOOK);
        ItemMeta helpMeta = help.getItemMeta();
        helpMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udcd6 Procedural Help");
        ArrayList<Object> helpLore = new ArrayList<Object>();
        helpLore.add(String.valueOf(ChatColor.GRAY) + "Learn more about procedural dungeons");
        helpLore.add("");
        helpLore.add(String.valueOf(ChatColor.YELLOW) + "Topics covered:");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "How randomization works");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Connector block system");
        helpLore.add(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Best practices");
        helpMeta.setLore(helpLore);
        help.setItemMeta(helpMeta);
        inv.setItem(49, help);
        ItemStack close = new ItemStack(Material.BARRIER);
        ItemMeta closeMeta = close.getItemMeta();
        closeMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2716 Close Menu");
        ArrayList<CallSite> closeLore = new ArrayList<CallSite>();
        closeLore.add((CallSite)((Object)(String.valueOf(ChatColor.GRAY) + "Close this interface")));
        closeMeta.setLore(closeLore);
        close.setItemMeta(closeMeta);
        inv.setItem(53, close);
    }

    private static void registerEventListener(final ApexDungeons plugin) {
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player)e.getWhoClicked();
                    switch (slot) {
                        case 22: {
                            clicker.closeInventory();
                            ProceduralDungeonGUI.startNameInput(clicker, plugin);
                            break;
                        }
                        case 45: {
                            clicker.closeInventory();
                            EnhancedMainGUI.open(clicker, plugin);
                            break;
                        }
                        case 49: {
                            clicker.closeInventory();
                            ProceduralDungeonGUI.showProceduralHelp(clicker);
                            break;
                        }
                        case 53: {
                            clicker.closeInventory();
                        }
                    }
                }
            }

            @EventHandler
            public void onChat(AsyncPlayerChatEvent e) {
                Player player = e.getPlayer();
                if (awaitingNameInput.containsKey(player.getUniqueId())) {
                    e.setCancelled(true);
                    awaitingNameInput.remove(player.getUniqueId());
                    String dungeonName = e.getMessage().trim();
                    if (dungeonName.length() < 3 || dungeonName.length() > 32) {
                        player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon name must be 3-32 characters long!");
                        return;
                    }
                    if (!dungeonName.matches("[a-zA-Z0-9_-]+")) {
                        player.sendMessage(String.valueOf(ChatColor.RED) + "Dungeon name can only contain letters, numbers, hyphens, and underscores!");
                        return;
                    }
                    if (plugin.getDungeonManager().getDungeon(dungeonName) != null) {
                        player.sendMessage(String.valueOf(ChatColor.RED) + "A dungeon with that name already exists!");
                        return;
                    }
                    player.sendMessage(String.valueOf(ChatColor.GREEN) + "Creating procedural dungeon: " + String.valueOf(ChatColor.AQUA) + dungeonName);
                    plugin.getDungeonManager().createProceduralDungeon(dungeonName, player);
                }
            }
        }, (Plugin)pl);
    }

    private static void startNameInput(Player player, ApexDungeons plugin) {
        awaitingNameInput.put(player.getUniqueId(), true);
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "    \ud83c\udfb2 PROCEDURAL DUNGEON CREATION");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Please enter a name for your procedural dungeon:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 3-32 characters");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Letters, numbers, hyphens, underscores only");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Type " + String.valueOf(ChatColor.RED) + "cancel" + String.valueOf(ChatColor.YELLOW) + " to abort");
        player.sendMessage("");
    }

    private static void showProceduralHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "      \ud83c\udfb2 PROCEDURAL DUNGEON GUIDE");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550\u2550");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfaf WHAT ARE PROCEDURAL DUNGEONS?");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "Procedural dungeons are special dungeons that randomize");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "their room layouts every time a player starts them!");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83d\udd27 HOW IT WORKS:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "1. " + String.valueOf(ChatColor.WHITE) + "Generate a dungeon with 5-12 random rooms");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "2. " + String.valueOf(ChatColor.WHITE) + "Rooms are connected with emerald connector blocks");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "3. " + String.valueOf(ChatColor.WHITE) + "Each time a player starts, rooms re-randomize");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "4. " + String.valueOf(ChatColor.WHITE) + "Layout is always perfect and never breaks");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2728 CONNECTOR BLOCKS:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Emerald blocks placed between rooms");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Show particle effects for guidance");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Ensure perfect connections every time");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Never fail or break the dungeon");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udfae PLAYER EXPERIENCE:");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Infinite replayability");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Unique challenge every time");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Unpredictable room sequences");
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "\u2022 " + String.valueOf(ChatColor.WHITE) + "Always ends with a boss room");
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\ud83d\udca1 PRO TIPS:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Add mob spawns and loot after generation");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Test the dungeon before releasing to players");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Use building tools for additional customization");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Remember: rooms randomize on each dungeon start!");
    }

    public static void clearNameInput(Player player) {
        awaitingNameInput.remove(player.getUniqueId());
    }
}

