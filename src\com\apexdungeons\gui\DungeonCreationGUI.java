/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.player.AsyncPlayerChatEvent
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.plugin.Plugin;

public class DungeonCreationGUI {
    private static final Map<UUID, Boolean> awaitingNameInput = new HashMap<UUID, Boolean>();

    public static void open(Player player, ApexDungeons plugin) {
        DungeonCreationGUI.promptForName(player, plugin);
    }

    private static void promptForName(Player player, ApexDungeons plugin) {
        awaitingNameInput.put(player.getUniqueId(), true);
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2728 Create New Dungeon " + String.valueOf(ChatColor.GRAY) + "- Made by Vexy");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Enter a name for your dungeon:");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 3-32 characters");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Letters, numbers, hyphens, underscores only");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "\u2022 Type 'cancel' to abort");
        player.sendMessage("");
        DungeonCreationGUI.registerChatListener(plugin);
    }

    private static void registerChatListener(final ApexDungeons plugin) {
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onChat(AsyncPlayerChatEvent e) {
                Player player = e.getPlayer();
                if (!awaitingNameInput.containsKey(player.getUniqueId())) {
                    return;
                }
                e.setCancelled(true);
                awaitingNameInput.remove(player.getUniqueId());
                String input = e.getMessage().trim();
                if (input.equalsIgnoreCase("cancel")) {
                    player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Dungeon creation cancelled.");
                    return;
                }
                if (input.length() < 3 || input.length() > 32) {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Name must be 3-32 characters long!");
                    Bukkit.getScheduler().runTask((Plugin)plugin, () -> DungeonCreationGUI.promptForName(player, plugin));
                    return;
                }
                if (!input.matches("[a-zA-Z0-9_-]+")) {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "Name can only contain letters, numbers, hyphens, and underscores!");
                    Bukkit.getScheduler().runTask((Plugin)plugin, () -> DungeonCreationGUI.promptForName(player, plugin));
                    return;
                }
                if (plugin.getDungeonManager().getDungeons().containsKey(input)) {
                    player.sendMessage(String.valueOf(ChatColor.RED) + "A dungeon with that name already exists!");
                    Bukkit.getScheduler().runTask((Plugin)plugin, () -> DungeonCreationGUI.promptForName(player, plugin));
                    return;
                }
                Bukkit.getScheduler().runTask((Plugin)plugin, () -> DungeonCreationGUI.createDungeonInstantly(player, plugin, input));
            }
        }, (Plugin)plugin);
    }

    private static void createDungeonInstantly(Player player, ApexDungeons plugin, String name) {
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2728 Creating dungeon: " + String.valueOf(ChatColor.AQUA) + name);
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u23f3 Generating optimized flat world...");
        if (name == null || name.trim().isEmpty()) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Invalid dungeon name!");
            return;
        }
        if (plugin.getDungeonManager().getDungeon(name) != null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 A dungeon with that name already exists!");
            return;
        }
        plugin.getDungeonManager().createDungeon(name, player);
        Bukkit.getScheduler().runTaskLater((Plugin)plugin, () -> {
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udf89 Dungeon created successfully!");
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Use " + String.valueOf(ChatColor.AQUA) + "/dgn tp " + name + String.valueOf(ChatColor.YELLOW) + " to visit your dungeon");
            player.sendMessage(String.valueOf(ChatColor.GRAY) + "Open Building Tools GUI to start building!");
        }, 20L);
    }
}

