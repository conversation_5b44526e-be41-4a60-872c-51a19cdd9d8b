/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 */
package com.apexdungeons.chests;

import org.bukkit.Location;

public class ChestSpawnPoint {
    private final Location location;
    private final String lootTable;
    private final double radius;

    public ChestSpawnPoint(Location location, String lootTable, double radius) {
        this.location = location.clone();
        this.lootTable = lootTable;
        this.radius = radius;
    }

    public Location getLocation() {
        return this.location.clone();
    }

    public String getLootTable() {
        return this.lootTable;
    }

    public double getRadius() {
        return this.radius;
    }

    public String toString() {
        return String.format("ChestSpawnPoint{lootTable=%s, location=%s, radius=%.1f}", this.lootTable, this.location, this.radius);
    }
}

