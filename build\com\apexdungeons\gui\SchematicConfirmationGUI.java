/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.inventory.InventoryClickEvent
 *  org.bukkit.inventory.Inventory
 *  org.bukkit.inventory.InventoryHolder
 *  org.bukkit.inventory.ItemStack
 *  org.bukkit.inventory.meta.ItemMeta
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import com.apexdungeons.schematics.SchematicPreview;
import java.util.ArrayList;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

public class SchematicConfirmationGUI {
    private static final String GUI_NAME = String.valueOf(ChatColor.DARK_RED) + "\u26a0 Confirm Schematic Placement";

    public static void open(Player player, ApexDungeons plugin, String schematicName, Location location, int rotation) {
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic == null) {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Schematic not found: " + schematicName);
            return;
        }
        Inventory inv = Bukkit.createInventory((InventoryHolder)player, (int)27, (String)GUI_NAME);
        SchematicConfirmationGUI.fillBackground(inv);
        SchematicConfirmationGUI.createSchematicInfo(inv, schematic, location, rotation);
        SchematicConfirmationGUI.createConfirmationButtons(inv, schematicName, location, rotation);
        player.openInventory(inv);
        SchematicConfirmationGUI.registerEventListener(plugin, schematicName, location, rotation);
    }

    private static void fillBackground(Inventory inv) {
        int[] borderSlots;
        ItemStack background = new ItemStack(Material.RED_STAINED_GLASS_PANE);
        ItemMeta backgroundMeta = background.getItemMeta();
        backgroundMeta.setDisplayName(" ");
        background.setItemMeta(backgroundMeta);
        for (int slot : borderSlots = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26}) {
            inv.setItem(slot, background);
        }
    }

    private static void createSchematicInfo(Inventory inv, SchematicData schematic, Location location, int rotation) {
        ItemStack info = new ItemStack(Material.WRITTEN_BOOK);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(String.valueOf(ChatColor.GOLD) + "\ud83d\udccb Schematic Information");
        ArrayList<Object> infoLore = new ArrayList<Object>();
        infoLore.add(String.valueOf(ChatColor.GRAY) + "Review the details before placement");
        infoLore.add("");
        infoLore.add(String.valueOf(ChatColor.YELLOW) + "Name: " + String.valueOf(ChatColor.WHITE) + schematic.getName());
        infoLore.add(String.valueOf(ChatColor.YELLOW) + "Dimensions: " + String.valueOf(ChatColor.WHITE) + schematic.getWidth() + "x" + schematic.getHeight() + "x" + schematic.getDepth());
        int totalBlocks = schematic.getWidth() * schematic.getHeight() * schematic.getDepth();
        int nonAirBlocks = SchematicConfirmationGUI.countNonAirBlocks(schematic);
        infoLore.add(String.valueOf(ChatColor.YELLOW) + "Total Blocks: " + String.valueOf(ChatColor.WHITE) + totalBlocks);
        infoLore.add(String.valueOf(ChatColor.YELLOW) + "Solid Blocks: " + String.valueOf(ChatColor.WHITE) + nonAirBlocks);
        infoLore.add(String.valueOf(ChatColor.YELLOW) + "Rotation: " + String.valueOf(ChatColor.WHITE) + rotation + "\u00b0");
        infoLore.add("");
        infoLore.add(String.valueOf(ChatColor.YELLOW) + "Placement Location:");
        infoLore.add(String.valueOf(ChatColor.WHITE) + "  X: " + location.getBlockX());
        infoLore.add(String.valueOf(ChatColor.WHITE) + "  Y: " + location.getBlockY());
        infoLore.add(String.valueOf(ChatColor.WHITE) + "  Z: " + location.getBlockZ());
        infoLore.add(String.valueOf(ChatColor.WHITE) + "  World: " + location.getWorld().getName());
        infoLore.add("");
        String complexity = SchematicConfirmationGUI.getComplexityLevel(nonAirBlocks);
        if (nonAirBlocks > 200) {
            infoLore.add(String.valueOf(ChatColor.RED) + "\u26a0 Large Structure Warning:");
            infoLore.add(String.valueOf(ChatColor.YELLOW) + "This is a " + complexity + " structure");
            infoLore.add(String.valueOf(ChatColor.YELLOW) + "Placement may take several seconds");
            infoLore.add(String.valueOf(ChatColor.YELLOW) + "and could cause temporary lag");
        } else {
            infoLore.add(String.valueOf(ChatColor.GREEN) + "\u2713 Structure Complexity: " + complexity);
        }
        infoMeta.setLore(infoLore);
        info.setItemMeta(infoMeta);
        inv.setItem(13, info);
    }

    private static void createConfirmationButtons(Inventory inv, String schematicName, Location location, int rotation) {
        ItemStack confirm = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta confirmMeta = confirm.getItemMeta();
        confirmMeta.setDisplayName(String.valueOf(ChatColor.GREEN) + "\u2713 Confirm Placement");
        ArrayList<Object> confirmLore = new ArrayList<Object>();
        confirmLore.add(String.valueOf(ChatColor.GRAY) + "Place the schematic at the");
        confirmLore.add(String.valueOf(ChatColor.GRAY) + "specified location with current");
        confirmLore.add(String.valueOf(ChatColor.GRAY) + "rotation settings");
        confirmLore.add("");
        confirmLore.add(String.valueOf(ChatColor.GREEN) + "Click to confirm!");
        confirmMeta.setLore(confirmLore);
        confirm.setItemMeta(confirmMeta);
        inv.setItem(10, confirm);
        ItemStack cancel = new ItemStack(Material.REDSTONE_BLOCK);
        ItemMeta cancelMeta = cancel.getItemMeta();
        cancelMeta.setDisplayName(String.valueOf(ChatColor.RED) + "\u2717 Cancel Placement");
        ArrayList<Object> cancelLore = new ArrayList<Object>();
        cancelLore.add(String.valueOf(ChatColor.GRAY) + "Cancel the schematic placement");
        cancelLore.add(String.valueOf(ChatColor.GRAY) + "and return to the previous menu");
        cancelLore.add("");
        cancelLore.add(String.valueOf(ChatColor.RED) + "Click to cancel!");
        cancelMeta.setLore(cancelLore);
        cancel.setItemMeta(cancelMeta);
        inv.setItem(16, cancel);
        ItemStack preview = new ItemStack(Material.ENDER_EYE);
        ItemMeta previewMeta = preview.getItemMeta();
        previewMeta.setDisplayName(String.valueOf(ChatColor.AQUA) + "\ud83d\udc41 Show Preview Again");
        ArrayList<Object> previewLore = new ArrayList<Object>();
        previewLore.add(String.valueOf(ChatColor.GRAY) + "Return to the 3D preview mode");
        previewLore.add(String.valueOf(ChatColor.GRAY) + "to adjust position or rotation");
        previewLore.add("");
        previewLore.add(String.valueOf(ChatColor.AQUA) + "Click to preview!");
        previewMeta.setLore(previewLore);
        preview.setItemMeta(previewMeta);
        inv.setItem(14, preview);
    }

    private static void registerEventListener(final ApexDungeons plugin, final String schematicName, final Location location, final int rotation) {
        ApexDungeons pl = plugin;
        Bukkit.getPluginManager().registerEvents(new Listener(){

            @EventHandler
            public void onClick(InventoryClickEvent e) {
                if (e.getView().getTitle().equals(GUI_NAME) && e.getWhoClicked() instanceof Player) {
                    e.setCancelled(true);
                    int slot = e.getRawSlot();
                    Player clicker = (Player)e.getWhoClicked();
                    switch (slot) {
                        case 10: {
                            clicker.closeInventory();
                            SchematicConfirmationGUI.confirmPlacement(clicker, plugin, schematicName, location, rotation);
                            break;
                        }
                        case 14: {
                            clicker.closeInventory();
                            SchematicConfirmationGUI.returnToPreview(clicker, plugin, schematicName, location, rotation);
                            break;
                        }
                        case 16: {
                            clicker.closeInventory();
                            clicker.sendMessage(String.valueOf(ChatColor.YELLOW) + "Schematic placement cancelled.");
                        }
                    }
                }
            }
        }, (Plugin)pl);
    }

    private static void confirmPlacement(Player player, ApexDungeons plugin, String schematicName, Location location, int rotation) {
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "Placing schematic: " + String.valueOf(ChatColor.YELLOW) + schematicName);
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Rotation: " + rotation + "\u00b0 | Location: " + location.getBlockX() + ", " + location.getBlockY() + ", " + location.getBlockZ());
        plugin.getSchematicManager().placeSchematic(schematicName, location).thenAccept(success -> {
            if (success.booleanValue()) {
                player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Schematic placed successfully!");
            } else {
                player.sendMessage(String.valueOf(ChatColor.RED) + "\u2717 Failed to place schematic!");
            }
        });
    }

    private static void returnToPreview(Player player, ApexDungeons plugin, String schematicName, Location location, int rotation) {
        SchematicData schematic = plugin.getSchematicManager().getSchematic(schematicName);
        if (schematic != null) {
            SchematicPreview preview = new SchematicPreview(plugin, player, schematic, location);
            for (int i = 0; i < rotation / 90; ++i) {
                preview.rotate();
            }
            plugin.getPreviewInputHandler().registerPreview(player, preview);
            preview.startPreview();
            player.sendMessage(String.valueOf(ChatColor.GREEN) + "Returned to preview mode");
        } else {
            player.sendMessage(String.valueOf(ChatColor.RED) + "Schematic not found: " + schematicName);
        }
    }

    private static int countNonAirBlocks(SchematicData schematic) {
        Material[][][] blocks;
        int count = 0;
        Material[][][] materialArray = blocks = schematic.getBlocks();
        int n = materialArray.length;
        for (int i = 0; i < n; ++i) {
            Material[][] layer;
            Material[][] materialArray2 = layer = materialArray[i];
            int n2 = materialArray2.length;
            for (int j = 0; j < n2; ++j) {
                Material[] row;
                for (Material block : row = materialArray2[j]) {
                    if (block == Material.AIR) continue;
                    ++count;
                }
            }
        }
        return count;
    }

    private static String getComplexityLevel(int blockCount) {
        if (blockCount < 50) {
            return String.valueOf(ChatColor.GREEN) + "Simple";
        }
        if (blockCount < 200) {
            return String.valueOf(ChatColor.YELLOW) + "Medium";
        }
        if (blockCount < 500) {
            return String.valueOf(ChatColor.GOLD) + "Complex";
        }
        return String.valueOf(ChatColor.RED) + "Very Complex";
    }
}

