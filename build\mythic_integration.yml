# Enhanced MythicMobs Integration for ApexDungeons
# This file configures deep integration with MythicMobs system

# Core integration settings
integration:
  enabled: true
  use_mythic_api: true
  use_mythic_commands: true
  sync_with_server: true
  override_vanilla: true

# Command integration
commands:
  # Use MythicMobs commands directly
  spawn_command: "mm mobs spawn {mob} {location}"
  spawn_with_level: "mm mobs spawn {mob} {location} {level}"
  kill_command: "mm mobs kill {mob}"
  list_command: "mm mobs list"
  
  # Custom dungeon commands that integrate with MM
  dungeon_spawn: "dgn spawn {mob} {location} {level}"
  dungeon_clear: "dgn clearmobs {dungeon}"

# Mob spawning integration
spawning:
  # Use MythicMobs spawning system
  use_mythic_spawner: true
  respect_mythic_conditions: true
  use_mythic_levels: true
  use_mythic_skills: true
  
  # Spawn parameters
  default_level_range: "1-5"
  boss_level_range: "5-10"
  elite_level_range: "3-7"

# Mob pools that reference MythicMobs directly
mythic_pools:
  dungeon_basic:
    - "SkeletonKnight:30:1-3"
    - "ZombieBrute:25:1-2" 
    - "SpiderQueen:20:2-4"
    - "OrcWarrior:15:1-3"
    - "GoblinRogue:10:1-2"
    
  dungeon_elite:
    - "EliteKnight:40:3-5"
    - "ChampionOrc:30:3-6"
    - "ShadowAssassin:20:4-7"
    - "FireElemental:10:5-8"
    
  dungeon_boss:
    - "DungeonLord:50:8-12"
    - "AncientLich:30:10-15"
    - "DragonKing:20:12-20"

# Skills and abilities integration
skills:
  enabled: true
  use_mythic_skills: true
  trigger_conditions: true
  custom_mechanics: true

# Rewards integration
rewards:
  use_mythic_drops: true
  use_mythic_experience: true
  custom_dungeon_rewards: true

# Debug and logging
debug:
  log_spawns: true
  log_commands: true
  log_integration: true
