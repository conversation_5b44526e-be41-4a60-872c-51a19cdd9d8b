/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Material
 *  org.bukkit.configuration.file.YamlConfiguration
 *  org.bukkit.inventory.ItemStack
 */
package com.apexdungeons.chests;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.chests.ChestLootTable;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bukkit.Material;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;

public class ChestLootManager {
    private final ApexDungeons plugin;
    private final Map<String, ChestLootTable> lootTables = new HashMap<String, ChestLootTable>();

    public ChestLootManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.loadLootTables();
        this.createDefaultLootTables();
    }

    public ChestLootTable getLootTable(String name) {
        return this.lootTables.get(name);
    }

    public ChestLootTable getDefaultLootTable() {
        return this.lootTables.get("common");
    }

    public void setLootTable(String name, ChestLootTable lootTable) {
        this.lootTables.put(name, lootTable);
        this.saveLootTables();
    }

    public Set<String> getLootTableNames() {
        return new HashSet<String>(this.lootTables.keySet());
    }

    public boolean addItemToLootTable(String tableName, Material material, int amount, int weight) {
        ChestLootTable table = this.lootTables.get(tableName);
        if (table == null) {
            table = new ChestLootTable(tableName);
            this.lootTables.put(tableName, table);
        }
        table.addItem(new ItemStack(material, amount), weight);
        this.saveLootTables();
        return true;
    }

    public boolean removeItemFromLootTable(String tableName, Material material) {
        ChestLootTable table = this.lootTables.get(tableName);
        if (table == null) {
            return false;
        }
        boolean removed = table.removeItem(material);
        if (removed) {
            this.saveLootTables();
        }
        return removed;
    }

    private void createDefaultLootTables() {
        if (this.lootTables.isEmpty()) {
            ChestLootTable common = new ChestLootTable("common");
            common.addItem(new ItemStack(Material.BREAD, 3), 50);
            common.addItem(new ItemStack(Material.APPLE, 2), 40);
            common.addItem(new ItemStack(Material.ARROW, 16), 35);
            common.addItem(new ItemStack(Material.COAL, 8), 30);
            common.addItem(new ItemStack(Material.IRON_INGOT, 2), 20);
            common.addItem(new ItemStack(Material.GOLD_INGOT, 1), 10);
            this.lootTables.put("common", common);
            ChestLootTable rare = new ChestLootTable("rare");
            rare.addItem(new ItemStack(Material.IRON_SWORD), 25);
            rare.addItem(new ItemStack(Material.IRON_HELMET), 20);
            rare.addItem(new ItemStack(Material.IRON_CHESTPLATE), 15);
            rare.addItem(new ItemStack(Material.IRON_LEGGINGS), 15);
            rare.addItem(new ItemStack(Material.IRON_BOOTS), 20);
            rare.addItem(new ItemStack(Material.BOW), 25);
            rare.addItem(new ItemStack(Material.DIAMOND, 2), 10);
            rare.addItem(new ItemStack(Material.EMERALD, 3), 15);
            rare.addItem(new ItemStack(Material.ENCHANTED_BOOK), 8);
            this.lootTables.put("rare", rare);
            ChestLootTable epic = new ChestLootTable("epic");
            epic.addItem(new ItemStack(Material.DIAMOND_SWORD), 20);
            epic.addItem(new ItemStack(Material.DIAMOND_HELMET), 15);
            epic.addItem(new ItemStack(Material.DIAMOND_CHESTPLATE), 10);
            epic.addItem(new ItemStack(Material.DIAMOND_LEGGINGS), 10);
            epic.addItem(new ItemStack(Material.DIAMOND_BOOTS), 15);
            epic.addItem(new ItemStack(Material.DIAMOND, 5), 25);
            epic.addItem(new ItemStack(Material.EMERALD, 8), 20);
            epic.addItem(new ItemStack(Material.ENCHANTED_BOOK), 15);
            epic.addItem(new ItemStack(Material.GOLDEN_APPLE), 12);
            epic.addItem(new ItemStack(Material.EXPERIENCE_BOTTLE, 5), 18);
            this.lootTables.put("epic", epic);
            ChestLootTable bossRewards = new ChestLootTable("boss_rewards");
            bossRewards.addItem(new ItemStack(Material.NETHERITE_SCRAP), 15);
            bossRewards.addItem(new ItemStack(Material.DIAMOND, 10), 25);
            bossRewards.addItem(new ItemStack(Material.EMERALD, 15), 20);
            bossRewards.addItem(new ItemStack(Material.ENCHANTED_GOLDEN_APPLE), 8);
            bossRewards.addItem(new ItemStack(Material.ENCHANTED_BOOK), 20);
            bossRewards.addItem(new ItemStack(Material.EXPERIENCE_BOTTLE, 10), 15);
            bossRewards.addItem(new ItemStack(Material.TOTEM_OF_UNDYING), 5);
            bossRewards.addItem(new ItemStack(Material.ELYTRA), 3);
            bossRewards.addItem(new ItemStack(Material.BEACON), 2);
            this.lootTables.put("boss_rewards", bossRewards);
            this.saveLootTables();
            this.plugin.getLogger().info("Created default loot tables: common, rare, epic, boss_rewards");
        }
    }

    private void loadLootTables() {
        File file = new File(this.plugin.getDataFolder(), "chest_loot_tables.yml");
        if (!file.exists()) {
            return;
        }
        YamlConfiguration config = YamlConfiguration.loadConfiguration((File)file);
        for (String tableName : config.getKeys(false)) {
            try {
                ChestLootTable table = new ChestLootTable(tableName);
                if (config.contains(tableName + ".items")) {
                    List items = config.getMapList(tableName + ".items");
                    for (Map itemMap : items) {
                        String materialName = (String)itemMap.get("material");
                        int amount = (Integer)itemMap.get("amount");
                        int weight = (Integer)itemMap.get("weight");
                        Material material = Material.valueOf((String)materialName);
                        table.addItem(new ItemStack(material, amount), weight);
                    }
                }
                this.lootTables.put(tableName, table);
            }
            catch (Exception e) {
                this.plugin.getLogger().warning("Failed to load loot table '" + tableName + "': " + e.getMessage());
            }
        }
        this.plugin.getLogger().info("Loaded " + this.lootTables.size() + " chest loot tables");
    }

    private void saveLootTables() {
        File file = new File(this.plugin.getDataFolder(), "chest_loot_tables.yml");
        YamlConfiguration config = new YamlConfiguration();
        for (Map.Entry<String, ChestLootTable> entry : this.lootTables.entrySet()) {
            String tableName = entry.getKey();
            ChestLootTable table = entry.getValue();
            ArrayList items = new ArrayList();
            for (ChestLootTable.WeightedItem weightedItem : table.getItems()) {
                HashMap<String, Object> itemMap = new HashMap<String, Object>();
                itemMap.put("material", weightedItem.getItem().getType().name());
                itemMap.put("amount", weightedItem.getItem().getAmount());
                itemMap.put("weight", weightedItem.getWeight());
                items.add(itemMap);
            }
            config.set(tableName + ".items", items);
        }
        try {
            config.save(file);
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save chest loot tables: " + e.getMessage());
        }
    }

    public void shutdown() {
        this.saveLootTables();
        this.lootTables.clear();
    }
}

