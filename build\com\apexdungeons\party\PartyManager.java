/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.party;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.party.Party;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

public class PartyManager {
    private final ApexDungeons plugin;
    private final Map<UUID, Party> parties = new HashMap<UUID, Party>();
    private final Map<UUID, Party> playerParties = new HashMap<UUID, Party>();
    private final Map<UUID, Set<UUID>> pendingInvites = new HashMap<UUID, Set<UUID>>();

    public PartyManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    public Party createParty(Player leader) {
        if (this.isInParty(leader)) {
            return null;
        }
        Party party = new Party(leader);
        this.parties.put(party.getPartyId(), party);
        this.playerParties.put(leader.getUniqueId(), party);
        this.plugin.getLogger().info("Created party " + String.valueOf(party.getPartyId()) + " with leader " + leader.getName());
        return party;
    }

    public boolean invitePlayer(Party party, Player inviter, Player target) {
        if (!party.isLeader(inviter)) {
            return false;
        }
        if (party.isFull()) {
            return false;
        }
        if (this.isInParty(target)) {
            return false;
        }
        Set targetInvites = this.pendingInvites.computeIfAbsent(target.getUniqueId(), k -> new HashSet());
        if (targetInvites.contains(party.getPartyId())) {
            return false;
        }
        targetInvites.add(party.getPartyId());
        target.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udf89 Party Invitation!");
        target.sendMessage(String.valueOf(ChatColor.YELLOW) + "You've been invited to join " + inviter.getName() + "'s party!");
        target.sendMessage(String.valueOf(ChatColor.AQUA) + "Type " + String.valueOf(ChatColor.WHITE) + "/dgn party accept " + inviter.getName() + String.valueOf(ChatColor.AQUA) + " to accept");
        target.sendMessage(String.valueOf(ChatColor.AQUA) + "Type " + String.valueOf(ChatColor.WHITE) + "/dgn party decline " + inviter.getName() + String.valueOf(ChatColor.AQUA) + " to decline");
        for (Player member : party.getMembers()) {
            member.sendMessage(String.valueOf(ChatColor.GREEN) + "Invited " + target.getName() + " to the party!");
        }
        this.plugin.getLogger().info("Player " + inviter.getName() + " invited " + target.getName() + " to party " + String.valueOf(party.getPartyId()));
        return true;
    }

    public boolean acceptInvite(Player player, String leaderName) {
        Party targetParty = null;
        for (Party party : this.parties.values()) {
            Set<UUID> playerInvites;
            if (!party.getLeader().getName().equalsIgnoreCase(leaderName) || (playerInvites = this.pendingInvites.get(player.getUniqueId())) == null || !playerInvites.contains(party.getPartyId())) continue;
            targetParty = party;
            break;
        }
        if (targetParty == null) {
            return false;
        }
        if (this.isInParty(player)) {
            return false;
        }
        if (targetParty.isFull()) {
            return false;
        }
        if (targetParty.addMember(player)) {
            this.playerParties.put(player.getUniqueId(), targetParty);
            Set<UUID> playerInvites = this.pendingInvites.get(player.getUniqueId());
            if (playerInvites != null) {
                playerInvites.remove(targetParty.getPartyId());
                if (playerInvites.isEmpty()) {
                    this.pendingInvites.remove(player.getUniqueId());
                }
            }
            for (Player member : targetParty.getMembers()) {
                member.sendMessage(String.valueOf(ChatColor.GREEN) + "\ud83c\udf89 " + player.getName() + " joined the party!");
            }
            this.plugin.getLogger().info("Player " + player.getName() + " joined party " + String.valueOf(targetParty.getPartyId()));
            return true;
        }
        return false;
    }

    public boolean declineInvite(Player player, String leaderName) {
        Party targetParty = null;
        for (Party party : this.parties.values()) {
            Set<UUID> playerInvites;
            if (!party.getLeader().getName().equalsIgnoreCase(leaderName) || (playerInvites = this.pendingInvites.get(player.getUniqueId())) == null || !playerInvites.contains(party.getPartyId())) continue;
            targetParty = party;
            break;
        }
        if (targetParty == null) {
            return false;
        }
        Set<UUID> playerInvites = this.pendingInvites.get(player.getUniqueId());
        if (playerInvites != null) {
            playerInvites.remove(targetParty.getPartyId());
            if (playerInvites.isEmpty()) {
                this.pendingInvites.remove(player.getUniqueId());
            }
        }
        targetParty.getLeader().sendMessage(String.valueOf(ChatColor.YELLOW) + player.getName() + " declined the party invitation.");
        player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You declined the party invitation from " + targetParty.getLeader().getName());
        return true;
    }

    public boolean leaveParty(Player player) {
        Party party = this.getPlayerParty(player);
        if (party == null) {
            return false;
        }
        if (party.isLeader(player)) {
            this.disbandParty(party);
        } else {
            party.removeMember(player);
            this.playerParties.remove(player.getUniqueId());
            for (Player member : party.getMembers()) {
                member.sendMessage(String.valueOf(ChatColor.YELLOW) + player.getName() + " left the party.");
            }
            player.sendMessage(String.valueOf(ChatColor.YELLOW) + "You left the party.");
        }
        return true;
    }

    public void disbandParty(Party party) {
        for (Player member : party.getMembers()) {
            member.sendMessage(String.valueOf(ChatColor.RED) + "Party has been disbanded.");
            this.playerParties.remove(member.getUniqueId());
        }
        this.parties.remove(party.getPartyId());
        this.pendingInvites.values().forEach(invites -> invites.remove(party.getPartyId()));
        this.plugin.getLogger().info("Disbanded party " + String.valueOf(party.getPartyId()));
    }

    public boolean kickPlayer(Player kicker, Player target) {
        Party party = this.getPlayerParty(kicker);
        if (party == null || !party.isLeader(kicker)) {
            return false;
        }
        if (!party.hasMember(target) || party.isLeader(target)) {
            return false;
        }
        party.removeMember(target);
        this.playerParties.remove(target.getUniqueId());
        for (Player member : party.getMembers()) {
            member.sendMessage(String.valueOf(ChatColor.YELLOW) + target.getName() + " was kicked from the party.");
        }
        target.sendMessage(String.valueOf(ChatColor.RED) + "You were kicked from the party.");
        return true;
    }

    public Party getPlayerParty(Player player) {
        return this.playerParties.get(player.getUniqueId());
    }

    public boolean isInParty(Player player) {
        return this.playerParties.containsKey(player.getUniqueId());
    }

    public Collection<Party> getAllParties() {
        return this.parties.values();
    }

    public Set<UUID> getPendingInvites(Player player) {
        return this.pendingInvites.getOrDefault(player.getUniqueId(), new HashSet());
    }

    public void cleanup() {
        this.parties.entrySet().removeIf(entry -> {
            Party party = (Party)entry.getValue();
            if (party.isEmpty()) {
                this.plugin.getLogger().info("Removing empty party: " + String.valueOf(party.getPartyId()));
                return true;
            }
            return false;
        });
        this.pendingInvites.entrySet().removeIf(entry -> {
            UUID playerId = (UUID)entry.getKey();
            Player player = this.plugin.getServer().getPlayer(playerId);
            return player == null || !player.isOnline();
        });
    }

    public void shutdown() {
        for (Party party : new ArrayList<Party>(this.parties.values())) {
            this.disbandParty(party);
        }
        this.parties.clear();
        this.playerParties.clear();
        this.pendingInvites.clear();
    }
}

