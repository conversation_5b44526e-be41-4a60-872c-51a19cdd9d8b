/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.entity.EntityType
 *  org.bukkit.entity.LivingEntity
 *  org.bukkit.inventory.ItemStack
 */
package com.apexdungeons.integration;

import com.apexdungeons.integration.MobAdapter;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.inventory.ItemStack;

public class VanillaAdapter
implements MobAdapter {
    @Override
    public LivingEntity spawnMob(String mobName, Location location) {
        if (mobName == null || location == null) {
            System.out.println("[ApexDungeons] VanillaAdapter: Null mobName or location");
            return null;
        }
        System.out.println("[ApexDungeons] VanillaAdapter: Attempting to spawn mob '" + mobName + "' at " + this.formatLocation(location));
        try {
            if (!location.getChunk().isLoaded()) {
                location.getChunk().load();
                System.out.println("[ApexDungeons] VanillaAdapter: Loaded chunk for spawn location");
            }
            String entityName = this.cleanAndMapMobName(mobName);
            System.out.println("[ApexDungeons] VanillaAdapter: Mapped '" + mobName + "' to '" + entityName + "'");
            LivingEntity entity = this.attemptSpawn(entityName, location);
            if (entity != null) {
                this.enhanceMob(entity, mobName);
                System.out.println("[ApexDungeons] VanillaAdapter: \u2713 Successfully spawned and enhanced " + entityName);
                return entity;
            }
            System.out.println("[ApexDungeons] VanillaAdapter: Primary spawn failed, trying fallback");
            return this.tryFallbackSpawning(mobName, location);
        }
        catch (Exception e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Exception during spawn: " + e.getMessage());
            e.printStackTrace();
            return this.tryFallbackSpawning(mobName, location);
        }
    }

    private String cleanAndMapMobName(String mobName) {
        String[] parts;
        String cleaned = mobName;
        if (mobName.contains(":") && (parts = mobName.split(":")).length == 2) {
            cleaned = parts[1];
        }
        return this.mapMobName(cleaned.toLowerCase());
    }

    private String mapMobName(String mobName) {
        switch (mobName) {
            case "zombie": {
                return "ZOMBIE";
            }
            case "skeleton": {
                return "SKELETON";
            }
            case "spider": {
                return "SPIDER";
            }
            case "creeper": {
                return "CREEPER";
            }
            case "enderman": {
                return "ENDERMAN";
            }
            case "witch": {
                return "WITCH";
            }
            case "slime": {
                return "SLIME";
            }
            case "magma_cube": 
            case "magmacube": 
            case "magma": {
                return "MAGMA_CUBE";
            }
            case "blaze": {
                return "BLAZE";
            }
            case "ghast": {
                return "GHAST";
            }
            case "piglin": {
                return "PIGLIN";
            }
            case "piglin_brute": 
            case "brute": {
                return "PIGLIN_BRUTE";
            }
            case "hoglin": {
                return "HOGLIN";
            }
            case "zoglin": {
                return "ZOGLIN";
            }
            case "zombified_piglin": 
            case "zombie_pigman": 
            case "pigman": {
                return "ZOMBIFIED_PIGLIN";
            }
            case "wither_skeleton": 
            case "wither_skele": {
                return "WITHER_SKELETON";
            }
            case "stray": {
                return "STRAY";
            }
            case "husk": {
                return "HUSK";
            }
            case "drowned": {
                return "DROWNED";
            }
            case "phantom": {
                return "PHANTOM";
            }
            case "vindicator": {
                return "VINDICATOR";
            }
            case "evoker": {
                return "EVOKER";
            }
            case "pillager": {
                return "PILLAGER";
            }
            case "ravager": {
                return "RAVAGER";
            }
            case "vex": {
                return "VEX";
            }
            case "guardian": {
                return "GUARDIAN";
            }
            case "elder_guardian": {
                return "ELDER_GUARDIAN";
            }
            case "shulker": {
                return "SHULKER";
            }
            case "silverfish": {
                return "SILVERFISH";
            }
            case "endermite": {
                return "ENDERMITE";
            }
            case "cave_spider": {
                return "CAVE_SPIDER";
            }
            case "warden": {
                return "WARDEN";
            }
            case "iron_golem": 
            case "golem": {
                return "IRON_GOLEM";
            }
            case "snow_golem": 
            case "snowman": {
                return "SNOW_GOLEM";
            }
            case "wolf": {
                return "WOLF";
            }
            case "polar_bear": {
                return "POLAR_BEAR";
            }
            case "panda": {
                return "PANDA";
            }
            case "bee": {
                return "BEE";
            }
            case "llama": {
                return "LLAMA";
            }
            case "trader_llama": {
                return "TRADER_LLAMA";
            }
            case "dolphin": {
                return "DOLPHIN";
            }
            case "cow": {
                return "COW";
            }
            case "pig": {
                return "PIG";
            }
            case "sheep": {
                return "SHEEP";
            }
            case "chicken": {
                return "CHICKEN";
            }
            case "horse": {
                return "HORSE";
            }
            case "donkey": {
                return "DONKEY";
            }
            case "mule": {
                return "MULE";
            }
            case "cat": {
                return "CAT";
            }
            case "ocelot": {
                return "OCELOT";
            }
            case "rabbit": {
                return "RABBIT";
            }
            case "bat": {
                return "BAT";
            }
            case "squid": {
                return "SQUID";
            }
            case "glow_squid": {
                return "GLOW_SQUID";
            }
            case "villager": {
                return "VILLAGER";
            }
            case "wandering_trader": 
            case "trader": {
                return "WANDERING_TRADER";
            }
            case "wither": 
            case "wither_boss": {
                return "WITHER";
            }
            case "ender_dragon": 
            case "dragon": {
                return "ENDER_DRAGON";
            }
            case "giant": {
                return "GIANT";
            }
        }
        return mobName.toUpperCase();
    }

    private LivingEntity attemptSpawn(String entityName, Location location) {
        try {
            EntityType entityType = EntityType.valueOf((String)entityName);
            if (!entityType.isAlive()) {
                System.out.println("[ApexDungeons] VanillaAdapter: EntityType " + entityName + " is not a living entity");
                return null;
            }
            Location safeLocation = this.findSafeSpawnLocation(location);
            LivingEntity entity = (LivingEntity)safeLocation.getWorld().spawnEntity(safeLocation, entityType);
            return entity;
        }
        catch (IllegalArgumentException e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Invalid EntityType: " + entityName);
            return null;
        }
        catch (Exception e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Spawn failed for " + entityName + ": " + e.getMessage());
            return null;
        }
    }

    private Location findSafeSpawnLocation(Location original) {
        Location safe = original.clone();
        if (this.isSafeSpawnLocation(safe)) {
            return safe;
        }
        for (int y = -2; y <= 5; ++y) {
            for (int x = -1; x <= 1; ++x) {
                for (int z = -1; z <= 1; ++z) {
                    Location test = original.clone().add((double)x, (double)y, (double)z);
                    if (!this.isSafeSpawnLocation(test)) continue;
                    return test;
                }
            }
        }
        return original;
    }

    private boolean isSafeSpawnLocation(Location loc) {
        return loc.getBlock().getType().isAir() && loc.clone().add(0.0, 1.0, 0.0).getBlock().getType().isAir() && !loc.clone().add(0.0, -1.0, 0.0).getBlock().getType().isAir();
    }

    private LivingEntity tryFallbackSpawning(String originalMobName, Location location) {
        String[] fallbackMobs;
        System.out.println("[ApexDungeons] VanillaAdapter: Trying fallback spawning for " + originalMobName);
        for (String fallbackMob : fallbackMobs = new String[]{"ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "SLIME"}) {
            try {
                LivingEntity entity = this.attemptSpawn(fallbackMob, location);
                if (entity == null) continue;
                entity.setCustomName("\u00a7e" + originalMobName + " \u00a77(Fallback: " + fallbackMob + ")");
                entity.setCustomNameVisible(true);
                System.out.println("[ApexDungeons] VanillaAdapter: \u2713 Fallback spawned " + fallbackMob + " for " + originalMobName);
                return entity;
            }
            catch (Exception e) {
                System.out.println("[ApexDungeons] VanillaAdapter: Fallback " + fallbackMob + " failed: " + e.getMessage());
            }
        }
        System.out.println("[ApexDungeons] VanillaAdapter: \u2717 All fallback attempts failed for " + originalMobName);
        return null;
    }

    private void enhanceMob(LivingEntity entity, String originalName) {
        try {
            if (entity.getCustomName() == null) {
                String displayName = this.formatMobName(originalName);
                entity.setCustomName("\u00a7f" + displayName);
                entity.setCustomNameVisible(true);
            }
            try {
                double currentMaxHealth = entity.getMaxHealth();
                entity.setMaxHealth(currentMaxHealth * 1.2);
                entity.setHealth(entity.getMaxHealth());
            }
            catch (Exception currentMaxHealth) {}
        }
        catch (Exception e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Failed to enhance mob: " + e.getMessage());
        }
    }

    @Override
    public LivingEntity spawnBoss(String bossName, Location location) {
        if (bossName == null || location == null) {
            System.out.println("[ApexDungeons] VanillaAdapter: Null bossName or location for boss spawn");
            return null;
        }
        System.out.println("[ApexDungeons] VanillaAdapter: Attempting to spawn boss '" + bossName + "' at " + this.formatLocation(location));
        LivingEntity boss = this.spawnSpecificBoss(bossName, location);
        if (boss == null) {
            boss = this.spawnMob(bossName, location);
        }
        if (boss != null) {
            this.enhanceBoss(boss, bossName);
            System.out.println("[ApexDungeons] VanillaAdapter: \u2713 Successfully spawned and enhanced boss " + bossName);
        } else {
            System.out.println("[ApexDungeons] VanillaAdapter: \u2717 Failed to spawn boss " + bossName);
        }
        return boss;
    }

    private LivingEntity spawnSpecificBoss(String bossName, Location location) {
        String lowerName;
        switch (lowerName = bossName.toLowerCase()) {
            case "wither": 
            case "wither_boss": {
                return this.attemptSpawn("WITHER", location);
            }
            case "ender_dragon": 
            case "dragon": {
                return this.attemptSpawn("ENDER_DRAGON", location);
            }
            case "elder_guardian": {
                return this.attemptSpawn("ELDER_GUARDIAN", location);
            }
            case "warden": {
                return this.attemptSpawn("WARDEN", location);
            }
            case "giant": {
                return this.attemptSpawn("GIANT", location);
            }
        }
        return null;
    }

    private void enhanceBoss(LivingEntity boss, String bossName) {
        try {
            String displayName = this.formatMobName(bossName);
            boss.setCustomName("\u00a74\u00a7l\u2694 BOSS: " + displayName + " \u2694");
            boss.setCustomNameVisible(true);
            try {
                double currentMaxHealth = boss.getMaxHealth();
                boss.setMaxHealth(currentMaxHealth * 3.0);
                boss.setHealth(boss.getMaxHealth());
            }
            catch (Exception exception) {
                // empty catch block
            }
            if (boss.getEquipment() != null) {
                boss.getEquipment().setHelmet(new ItemStack(Material.NETHERITE_HELMET));
                boss.getEquipment().setChestplate(new ItemStack(Material.NETHERITE_CHESTPLATE));
                boss.getEquipment().setLeggings(new ItemStack(Material.NETHERITE_LEGGINGS));
                boss.getEquipment().setBoots(new ItemStack(Material.NETHERITE_BOOTS));
                boss.getEquipment().setItemInMainHand(new ItemStack(Material.NETHERITE_SWORD));
                boss.getEquipment().setHelmetDropChance(0.0f);
                boss.getEquipment().setChestplateDropChance(0.0f);
                boss.getEquipment().setLeggingsDropChance(0.0f);
                boss.getEquipment().setBootsDropChance(0.0f);
                boss.getEquipment().setItemInMainHandDropChance(0.0f);
            }
        }
        catch (Exception e) {
            System.out.println("[ApexDungeons] VanillaAdapter: Failed to enhance boss: " + e.getMessage());
        }
    }

    private String formatMobName(String mobName) {
        String formatted = mobName.replace("_", " ").replace(":", " ").toLowerCase();
        String[] words = formatted.split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() <= 0) continue;
            result.append(Character.toUpperCase(word.charAt(0))).append(word.substring(1)).append(" ");
        }
        return result.toString().trim();
    }

    private String formatLocation(Location loc) {
        return String.format("%s(%.1f,%.1f,%.1f)", loc.getWorld().getName(), loc.getX(), loc.getY(), loc.getZ());
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public String getAdapterName() {
        return "Enhanced Vanilla Adapter";
    }

    @Override
    public String[] getAvailableMobs() {
        return new String[]{"ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "ENDERMAN", "WITCH", "SLIME", "MAGMA_CUBE", "BLAZE", "GHAST", "PIGLIN", "HOGLIN", "VINDICATOR", "EVOKER", "PILLAGER", "RAVAGER", "VEX", "WITHER_SKELETON", "STRAY", "HUSK", "DROWNED", "PHANTOM", "GUARDIAN", "SHULKER", "SILVERFISH", "ENDERMITE", "CAVE_SPIDER", "IRON_GOLEM", "SNOW_GOLEM", "WOLF", "POLAR_BEAR", "BEE", "COW", "PIG", "SHEEP", "CHICKEN", "VILLAGER"};
    }

    @Override
    public String[] getAvailableBosses() {
        return new String[]{"WITHER", "ENDER_DRAGON", "ELDER_GUARDIAN", "WARDEN", "GIANT", "RAVAGER", "EVOKER", "VINDICATOR", "PIGLIN_BRUTE"};
    }
}

