/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.World
 *  org.bukkit.configuration.file.YamlConfiguration
 *  org.bukkit.entity.Player
 *  org.bukkit.plugin.Plugin
 */
package com.apexdungeons.saved;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.saved.SavedDungeon;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

public class SavedDungeonManager {
    private final ApexDungeons plugin;
    private final File savedDungeonsFile;
    private final Map<String, SavedDungeon> savedDungeons = new HashMap<String, SavedDungeon>();

    public SavedDungeonManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.savedDungeonsFile = new File(plugin.getDataFolder(), "saved_dungeons.yml");
        this.loadSavedDungeons();
    }

    public boolean saveDungeon(String dungeonName, String savedName, Player creator) {
        DungeonInstance dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            this.plugin.getLogger().warning("Cannot save dungeon: '" + dungeonName + "' not found");
            return false;
        }
        World dungeonWorld = dungeon.getOrigin().getWorld();
        if (dungeonWorld == null) {
            this.plugin.getLogger().warning("Cannot save dungeon: world is null");
            return false;
        }
        SavedDungeon savedDungeon = new SavedDungeon();
        savedDungeon.setName(savedName);
        savedDungeon.setOriginalName(dungeonName);
        savedDungeon.setCreator(creator.getName());
        savedDungeon.setCreatedTime(System.currentTimeMillis());
        savedDungeon.setDescription("Saved from dungeon: " + dungeonName);
        this.scanDungeonWorld(dungeonWorld, savedDungeon);
        Location spawnLoc = this.plugin.getWorldManager().getDungeonSpawnLocation(dungeonName);
        if (spawnLoc != null) {
            savedDungeon.setSpawnLocation(spawnLoc);
        }
        HashMap<String, Object> settings = new HashMap<String, Object>();
        settings.put("max_players", 4);
        settings.put("difficulty", "normal");
        settings.put("time_limit", 0);
        savedDungeon.setSettings(settings);
        this.savedDungeons.put(savedName, savedDungeon);
        return this.saveToDisk();
    }

    private void scanDungeonWorld(World world, SavedDungeon savedDungeon) {
        ArrayList<Location> startBlocks = new ArrayList<Location>();
        ArrayList<Location> endBlocks = new ArrayList<Location>();
        ArrayList<Location> mobSpawns = new ArrayList<Location>();
        ArrayList<Location> bossSpawns = new ArrayList<Location>();
        ArrayList<Location> chestSpawns = new ArrayList<Location>();
        Location center = new Location(world, 0.0, 64.0, 0.0);
        int radius = 100;
        for (int x = -radius; x <= radius; x += 5) {
            for (int y = 0; y <= 128; y += 5) {
                block8: for (int z = -radius; z <= radius; z += 5) {
                    Location loc = center.clone().add((double)x, (double)y, (double)z);
                    switch (loc.getBlock().getType()) {
                        case EMERALD_BLOCK: {
                            startBlocks.add(loc.clone());
                            continue block8;
                        }
                        case DIAMOND_BLOCK: {
                            endBlocks.add(loc.clone());
                            continue block8;
                        }
                        case SPAWNER: {
                            mobSpawns.add(loc.clone());
                            continue block8;
                        }
                        case CHEST: {
                            chestSpawns.add(loc.clone());
                            continue block8;
                        }
                    }
                }
            }
        }
        savedDungeon.setStartBlocks(startBlocks);
        savedDungeon.setEndBlocks(endBlocks);
        savedDungeon.setMobSpawns(mobSpawns);
        savedDungeon.setBossSpawns(bossSpawns);
        savedDungeon.setChestSpawns(chestSpawns);
        this.plugin.getLogger().info("Scanned dungeon: " + startBlocks.size() + " start blocks, " + endBlocks.size() + " end blocks, " + mobSpawns.size() + " mob spawns, " + chestSpawns.size() + " chest spawns");
    }

    public boolean loadSavedDungeon(String savedName, String newDungeonName, Player player) {
        SavedDungeon savedDungeon = this.savedDungeons.get(savedName);
        if (savedDungeon == null) {
            return false;
        }
        if (this.plugin.getDungeonManager().getDungeon(newDungeonName) != null) {
            return false;
        }
        this.plugin.getWorldManager().createDungeonWorld(newDungeonName).thenAccept(world -> {
            if (world != null) {
                boolean success = this.applySavedDungeon(savedDungeon, newDungeonName, (World)world);
                this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> {
                    if (success) {
                        player.sendMessage("\u00a7aSuccessfully loaded saved dungeon '" + savedName + "' as '" + newDungeonName + "'!");
                    } else {
                        player.sendMessage("\u00a7cFailed to apply saved dungeon.");
                    }
                });
            } else {
                this.plugin.getServer().getScheduler().runTask((Plugin)this.plugin, () -> player.sendMessage("\u00a7cFailed to create dungeon world."));
            }
        });
        return true;
    }

    private boolean applySavedDungeon(SavedDungeon savedDungeon, String newDungeonName, World targetWorld) {
        try {
            Location newLoc;
            Location origin = new Location(targetWorld, 0.0, 64.0, 0.0);
            DungeonInstance newDungeon = new DungeonInstance(this.plugin, newDungeonName, targetWorld, origin, 1);
            for (Location startLoc : savedDungeon.getStartBlocks()) {
                newLoc = new Location(targetWorld, startLoc.getX(), startLoc.getY(), startLoc.getZ());
                newLoc.getBlock().setType(Material.EMERALD_BLOCK);
            }
            for (Location endLoc : savedDungeon.getEndBlocks()) {
                newLoc = new Location(targetWorld, endLoc.getX(), endLoc.getY(), endLoc.getZ());
                newLoc.getBlock().setType(Material.DIAMOND_BLOCK);
            }
            this.plugin.getDungeonManager().addDungeon(newDungeon);
            this.plugin.getWorldManager().registerDungeonWorld(newDungeonName, targetWorld);
            this.plugin.getLogger().info("Applied saved dungeon '" + savedDungeon.getName() + "' to create '" + newDungeonName + "'");
            return true;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("Failed to apply saved dungeon: " + e.getMessage());
            return false;
        }
    }

    public Map<String, SavedDungeon> getSavedDungeons() {
        return new HashMap<String, SavedDungeon>(this.savedDungeons);
    }

    public SavedDungeon getSavedDungeon(String name) {
        return this.savedDungeons.get(name);
    }

    public boolean deleteSavedDungeon(String name) {
        if (this.savedDungeons.remove(name) != null) {
            return this.saveToDisk();
        }
        return false;
    }

    public boolean exists(String name) {
        return this.savedDungeons.containsKey(name);
    }

    private void loadSavedDungeons() {
        if (!this.savedDungeonsFile.exists()) {
            return;
        }
        YamlConfiguration config = YamlConfiguration.loadConfiguration((File)this.savedDungeonsFile);
        for (String key : config.getKeys(false)) {
            try {
                SavedDungeon savedDungeon = SavedDungeon.fromConfig(config.getConfigurationSection(key));
                this.savedDungeons.put(key, savedDungeon);
            }
            catch (Exception e) {
                this.plugin.getLogger().warning("Failed to load saved dungeon '" + key + "': " + e.getMessage());
            }
        }
        this.plugin.getLogger().info("Loaded " + this.savedDungeons.size() + " saved dungeons");
    }

    private boolean saveToDisk() {
        try {
            YamlConfiguration config = new YamlConfiguration();
            for (Map.Entry<String, SavedDungeon> entry : this.savedDungeons.entrySet()) {
                entry.getValue().saveToConfig(config.createSection(entry.getKey()));
            }
            config.save(this.savedDungeonsFile);
            return true;
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save dungeons to disk: " + e.getMessage());
            return false;
        }
    }

    public void shutdown() {
        this.saveToDisk();
        this.savedDungeons.clear();
    }
}

