/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.World
 *  org.bukkit.configuration.file.YamlConfiguration
 *  org.bukkit.entity.ArmorStand
 *  org.bukkit.entity.EntityType
 *  org.bukkit.entity.LivingEntity
 *  org.bukkit.entity.Player
 *  org.bukkit.event.EventHandler
 *  org.bukkit.event.Listener
 *  org.bukkit.event.entity.EntityDeathEvent
 *  org.bukkit.event.player.PlayerMoveEvent
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.scheduler.BukkitRunnable
 *  org.bukkit.util.Vector
 */
package com.apexdungeons.mobs;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.mobs.CustomMobConfig;
import com.apexdungeons.mobs.CustomMobSpawnPoint;
import com.apexdungeons.mobs.MobSpawnPoint;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.Vector;

public class MobSpawnManager
implements Listener {
    private final ApexDungeons plugin;
    private final Map<String, MobSpawnPoint> spawnPoints = new ConcurrentHashMap<String, MobSpawnPoint>();
    private final Map<String, Long> lastSpawnTimes = new ConcurrentHashMap<String, Long>();
    private final Set<String> activeSpawns = ConcurrentHashMap.newKeySet();
    private double defaultRadius = 6.0;
    private long defaultCooldown = 30000L;
    private int defaultMaxMobs = 3;

    public MobSpawnManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.loadSpawnPoints();
        Bukkit.getPluginManager().registerEvents((Listener)this, (Plugin)plugin);
        this.startCleanupTask();
        this.startVisualFeedbackTask();
    }

    public void addMobSpawnPoint(Location location, String mobName, double radius) {
        String key = this.locationToKey(location);
        MobSpawnPoint spawnPoint = new MobSpawnPoint(location, mobName, radius, false);
        this.spawnPoints.put(key, spawnPoint);
        this.saveSpawnPoints();
        this.plugin.getLogger().info("Added mob spawn point: " + mobName + " at " + this.locationToString(location));
    }

    public void addBossSpawnPoint(Location location, String bossName, double radius) {
        String key = this.locationToKey(location);
        MobSpawnPoint spawnPoint = new MobSpawnPoint(location, bossName, radius, true);
        this.spawnPoints.put(key, spawnPoint);
        this.saveSpawnPoints();
        this.plugin.getLogger().info("Added boss spawn point: " + bossName + " at " + this.locationToString(location));
    }

    public boolean removeSpawnPoint(Location location) {
        String key = this.locationToKey(location);
        MobSpawnPoint removed = this.spawnPoints.remove(key);
        if (removed != null) {
            this.saveSpawnPoints();
            this.plugin.getLogger().info("Removed spawn point at " + this.locationToString(location));
            return true;
        }
        return false;
    }

    public MobSpawnPoint getSpawnPoint(Location location) {
        String key = this.locationToKey(location);
        return this.spawnPoints.get(key);
    }

    public Collection<MobSpawnPoint> getAllSpawnPoints() {
        return new ArrayList<MobSpawnPoint>(this.spawnPoints.values());
    }

    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        Location to = event.getTo();
        if (to == null) {
            return;
        }
        Location from = event.getFrom();
        if (from.getBlockX() == to.getBlockX() && from.getBlockY() == to.getBlockY() && from.getBlockZ() == to.getBlockZ()) {
            return;
        }
        if (!this.plugin.getWorldManager().isDungeonWorld(to.getWorld())) {
            return;
        }
        for (MobSpawnPoint spawnPoint : this.spawnPoints.values()) {
            double distance;
            if (!spawnPoint.getLocation().getWorld().equals((Object)to.getWorld()) || !((distance = spawnPoint.getLocation().distance(to)) <= spawnPoint.getRadius())) continue;
            this.plugin.getLogger().info("[MobSpawnManager] Player " + player.getName() + " triggered spawn point '" + spawnPoint.getMobName() + "' at distance " + String.format("%.1f", distance));
            this.triggerSpawn(spawnPoint, player);
        }
    }

    private void triggerSpawn(MobSpawnPoint spawnPoint, Player triggeringPlayer) {
        this.plugin.getLogger().info("Triggering spawn for " + spawnPoint.getMobName() + " at " + this.locationToString(spawnPoint.getLocation()));
        if (!spawnPoint.isActive()) {
            this.plugin.getLogger().info("Spawn point is inactive, skipping");
            return;
        }
        if (spawnPoint.isOnCooldown()) {
            this.plugin.getLogger().info("Spawn point on cooldown (" + spawnPoint.getRemainingCooldownSeconds() + "s remaining), skipping");
            return;
        }
        if (spawnPoint.isAtMaxCapacity()) {
            this.plugin.getLogger().info("Spawn point at max capacity (" + spawnPoint.getSpawnedMobs().size() + "/" + spawnPoint.getMaxConcurrentMobs() + "), skipping");
            return;
        }
        spawnPoint.setLastSpawnTime(System.currentTimeMillis());
        this.plugin.getLogger().info("Starting enhanced mob spawn process for " + spawnPoint.getMobName());
        this.spawnMobsEnhanced(spawnPoint, triggeringPlayer);
    }

    private void spawnMobsEnhanced(MobSpawnPoint spawnPoint, Player triggeringPlayer) {
        Location spawnLoc = spawnPoint.getLocation().clone();
        String mobName = spawnPoint.getMobName();
        boolean isBoss = spawnPoint.isBoss();
        this.plugin.getLogger().info("[MobSpawnManager] SpawnMobs called for " + mobName + " (boss: " + isBoss + ") at " + this.locationToString(spawnLoc));
        if (this.plugin.getMobAdapter() == null) {
            this.plugin.getLogger().severe("[MobSpawnManager] MobAdapter is null! Cannot spawn mobs.");
            return;
        }
        this.plugin.getLogger().info("[MobSpawnManager] Using mob adapter: " + this.plugin.getMobAdapter().getAdapterName());
        int availableSlots = spawnPoint.getMaxConcurrentMobs() - spawnPoint.getSpawnedMobs().size();
        int mobCount = isBoss ? 1 : Math.min(availableSlots, 1 + new Random().nextInt(Math.min(3, availableSlots)));
        this.plugin.getLogger().info("[MobSpawnManager] Attempting to spawn " + mobCount + " mobs (available slots: " + availableSlots + ")");
        int successfulSpawns = 0;
        for (int i = 0; i < mobCount; ++i) {
            Location randomLoc = spawnLoc.clone().add((Math.random() - 0.5) * 4.0, 0.0, (Math.random() - 0.5) * 4.0);
            randomLoc = this.findSafeSpawnLocation(randomLoc);
            this.plugin.getLogger().info("[MobSpawnManager] Attempting to spawn " + mobName + " at " + this.locationToString(randomLoc));
            if (!randomLoc.getChunk().isLoaded()) {
                randomLoc.getChunk().load();
                this.plugin.getLogger().info("[MobSpawnManager] Loaded chunk for spawn location");
            }
            LivingEntity spawned = null;
            try {
                if (isBoss) {
                    this.plugin.getLogger().info("[MobSpawnManager] Spawning boss: " + mobName);
                    spawned = this.plugin.getMobAdapter().spawnBoss(mobName, randomLoc);
                } else {
                    this.plugin.getLogger().info("[MobSpawnManager] Spawning mob: " + mobName);
                    spawned = this.plugin.getMobAdapter().spawnMob(mobName, randomLoc);
                }
                if (spawned != null) {
                    ++successfulSpawns;
                    spawnPoint.addSpawnedMob(spawned.getUniqueId());
                    this.plugin.getLogger().info("[MobSpawnManager] Successfully spawned " + (isBoss ? "boss " : "mob ") + mobName + " (" + String.valueOf(spawned.getType()) + ") at " + this.locationToString(randomLoc) + " (triggered by " + triggeringPlayer.getName() + ")");
                    randomLoc.getWorld().spawnParticle(Particle.EXPLOSION, randomLoc, 1);
                    randomLoc.getWorld().playSound(randomLoc, Sound.ENTITY_ZOMBIE_VILLAGER_CONVERTED, 1.0f, 0.8f);
                    triggeringPlayer.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2713 Spawned " + (isBoss ? "boss " : "mob ") + String.valueOf(ChatColor.YELLOW) + mobName + String.valueOf(ChatColor.GREEN) + "!");
                    continue;
                }
                this.plugin.getLogger().warning("[MobSpawnManager] Failed to spawn " + mobName + " - adapter returned null");
                this.plugin.getLogger().info("[MobSpawnManager] Trying alternative spawn methods...");
                spawned = this.tryAlternativeSpawn(mobName, randomLoc, isBoss);
                if (spawned == null) continue;
                ++successfulSpawns;
                spawnPoint.addSpawnedMob(spawned.getUniqueId());
                this.plugin.getLogger().info("[MobSpawnManager] Alternative spawn successful: " + String.valueOf(spawned.getType()));
                triggeringPlayer.sendMessage(String.valueOf(ChatColor.YELLOW) + "\u26a0 Spawned alternative mob: " + String.valueOf(spawned.getType()));
                continue;
            }
            catch (Exception e) {
                this.plugin.getLogger().severe("[MobSpawnManager] Exception while spawning " + mobName + ": " + e.getMessage());
                e.printStackTrace();
                try {
                    spawned = this.tryEmergencyFallback(randomLoc, triggeringPlayer);
                    if (spawned == null) continue;
                    ++successfulSpawns;
                    spawnPoint.addSpawnedMob(spawned.getUniqueId());
                    triggeringPlayer.sendMessage(String.valueOf(ChatColor.RED) + "\u26a0 Emergency fallback spawn: " + String.valueOf(spawned.getType()));
                    continue;
                }
                catch (Exception fallbackException) {
                    this.plugin.getLogger().severe("[MobSpawnManager] Emergency fallback also failed: " + fallbackException.getMessage());
                }
            }
        }
        this.plugin.getLogger().info("[MobSpawnManager] Spawn process completed: " + successfulSpawns + "/" + mobCount + " mobs spawned successfully");
        if (successfulSpawns == 0) {
            this.plugin.getLogger().warning("[MobSpawnManager] No mobs spawned successfully, trying final fallback spawn");
            this.tryFallbackSpawn(spawnLoc, triggeringPlayer);
        }
    }

    private LivingEntity tryAlternativeSpawn(String mobName, Location location, boolean isBoss) {
        block7: {
            try {
                EntityType entityType = null;
                try {
                    entityType = EntityType.valueOf((String)mobName.toUpperCase());
                }
                catch (IllegalArgumentException e) {
                    this.plugin.getLogger().info("[MobSpawnManager] " + mobName + " is not a vanilla entity type");
                }
                if (entityType == null || !entityType.isSpawnable()) break block7;
                this.plugin.getLogger().info("[MobSpawnManager] Trying vanilla spawn for " + String.valueOf(entityType));
                LivingEntity entity = (LivingEntity)location.getWorld().spawnEntity(location, entityType);
                if (isBoss && entity != null) {
                    entity.setCustomName(String.valueOf(ChatColor.DARK_RED) + "Boss " + mobName);
                    entity.setCustomNameVisible(true);
                    try {
                        double maxHealth = entity.getMaxHealth();
                        double newMaxHealth = Math.max(maxHealth * 2.0, 100.0);
                        entity.setMaxHealth(newMaxHealth);
                        entity.setHealth(newMaxHealth);
                        this.plugin.getLogger().info("[MobSpawnManager] Enhanced boss health: " + newMaxHealth);
                    }
                    catch (Exception attrException) {
                        this.plugin.getLogger().info("[MobSpawnManager] Could not set boss health: " + attrException.getMessage());
                    }
                }
                return entity;
            }
            catch (Exception e) {
                this.plugin.getLogger().warning("[MobSpawnManager] Alternative spawn failed: " + e.getMessage());
            }
        }
        return null;
    }

    private LivingEntity tryEmergencyFallback(Location location, Player triggeringPlayer) {
        try {
            this.plugin.getLogger().info("[MobSpawnManager] Attempting emergency fallback spawn (ZOMBIE)");
            LivingEntity zombie = (LivingEntity)location.getWorld().spawnEntity(location, EntityType.ZOMBIE);
            zombie.setCustomName(String.valueOf(ChatColor.RED) + "Dungeon Mob");
            zombie.setCustomNameVisible(true);
            return zombie;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("[MobSpawnManager] Emergency fallback spawn failed: " + e.getMessage());
            return null;
        }
    }

    private void tryFallbackSpawn(Location location, Player triggeringPlayer) {
        try {
            this.plugin.getLogger().info("Attempting fallback spawn of ZOMBIE");
            LivingEntity fallback = this.plugin.getMobAdapter().spawnMob("ZOMBIE", location);
            if (fallback != null) {
                this.plugin.getLogger().info("Fallback spawn successful - spawned ZOMBIE");
            } else {
                this.plugin.getLogger().severe("Even fallback spawn failed!");
            }
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("Fallback spawn failed with exception: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private Location findSafeSpawnLocation(Location location) {
        Location safe = location.clone();
        if (safe.getBlock().getType().isAir() && safe.clone().add(0.0, 1.0, 0.0).getBlock().getType().isAir()) {
            return safe;
        }
        for (int y = -2; y <= 5; ++y) {
            Location test = safe.clone().add(0.0, (double)y, 0.0);
            if (!test.getBlock().getType().isAir() || !test.clone().add(0.0, 1.0, 0.0).getBlock().getType().isAir() || test.clone().add(0.0, -1.0, 0.0).getBlock().getType().isAir()) continue;
            return test;
        }
        return safe;
    }

    private void startCleanupTask() {
        new BukkitRunnable(){

            public void run() {
                long now = System.currentTimeMillis();
                MobSpawnManager.this.lastSpawnTimes.entrySet().removeIf(entry -> now - (Long)entry.getValue() > MobSpawnManager.this.defaultCooldown * 10L);
            }
        }.runTaskTimerAsynchronously((Plugin)this.plugin, 6000L, 6000L);
    }

    private String locationToKey(Location location) {
        return location.getWorld().getName() + ":" + location.getBlockX() + ":" + location.getBlockY() + ":" + location.getBlockZ();
    }

    private String locationToString(Location location) {
        return String.format("%s(%d,%d,%d)", location.getWorld().getName(), location.getBlockX(), location.getBlockY(), location.getBlockZ());
    }

    private void loadSpawnPoints() {
        File file = new File(this.plugin.getDataFolder(), "mob_spawns.yml");
        if (!file.exists()) {
            return;
        }
        YamlConfiguration config = YamlConfiguration.loadConfiguration((File)file);
        for (String key : config.getKeys(false)) {
            try {
                String worldName = config.getString(key + ".world");
                int x = config.getInt(key + ".x");
                int y = config.getInt(key + ".y");
                int z = config.getInt(key + ".z");
                String mobName = config.getString(key + ".mob");
                double radius = config.getDouble(key + ".radius", this.defaultRadius);
                boolean isBoss = config.getBoolean(key + ".boss", false);
                Location location = new Location(Bukkit.getWorld((String)worldName), (double)x, (double)y, (double)z);
                MobSpawnPoint spawnPoint = new MobSpawnPoint(location, mobName, radius, isBoss);
                long cooldownMs = config.getLong(key + ".cooldownMs", this.defaultCooldown);
                int maxConcurrentMobs = config.getInt(key + ".maxConcurrentMobs", this.defaultMaxMobs);
                boolean active = config.getBoolean(key + ".active", true);
                String displayName = config.getString(key + ".displayName", spawnPoint.getDisplayName());
                spawnPoint.setCooldownMs(cooldownMs);
                spawnPoint.setMaxConcurrentMobs(maxConcurrentMobs);
                spawnPoint.setActive(active);
                spawnPoint.setDisplayName(displayName);
                this.spawnPoints.put(key, spawnPoint);
            }
            catch (Exception e) {
                this.plugin.getLogger().warning("Failed to load spawn point " + key + ": " + e.getMessage());
            }
        }
        this.plugin.getLogger().info("Loaded " + this.spawnPoints.size() + " mob spawn points");
    }

    private void saveSpawnPoints() {
        File file = new File(this.plugin.getDataFolder(), "mob_spawns.yml");
        YamlConfiguration config = new YamlConfiguration();
        for (Map.Entry<String, MobSpawnPoint> entry : this.spawnPoints.entrySet()) {
            String key = entry.getKey();
            MobSpawnPoint point = entry.getValue();
            Location loc = point.getLocation();
            config.set(key + ".world", (Object)loc.getWorld().getName());
            config.set(key + ".x", (Object)loc.getBlockX());
            config.set(key + ".y", (Object)loc.getBlockY());
            config.set(key + ".z", (Object)loc.getBlockZ());
            config.set(key + ".mob", (Object)point.getMobName());
            config.set(key + ".radius", (Object)point.getRadius());
            config.set(key + ".boss", (Object)point.isBoss());
            config.set(key + ".cooldownMs", (Object)point.getCooldownMs());
            config.set(key + ".maxConcurrentMobs", (Object)point.getMaxConcurrentMobs());
            config.set(key + ".active", (Object)point.isActive());
            config.set(key + ".displayName", (Object)point.getDisplayName());
        }
        try {
            config.save(file);
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save mob spawn points: " + e.getMessage());
        }
    }

    public boolean testMobSpawn(Location location, String mobName, boolean isBoss) {
        this.plugin.getLogger().info("Testing mob spawn: " + mobName + " at " + this.locationToString(location));
        try {
            LivingEntity spawned = isBoss ? this.plugin.getMobAdapter().spawnBoss(mobName, location) : this.plugin.getMobAdapter().spawnMob(mobName, location);
            if (spawned != null) {
                this.plugin.getLogger().info("Test spawn successful: " + String.valueOf(spawned.getType()));
                return true;
            }
            this.plugin.getLogger().warning("Test spawn failed: adapter returned null");
            return false;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("Test spawn failed with exception: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    private void startVisualFeedbackTask() {
        new BukkitRunnable(){

            public void run() {
                for (MobSpawnPoint spawnPoint : MobSpawnManager.this.spawnPoints.values()) {
                    MobSpawnManager.this.showVisualFeedback(spawnPoint);
                }
            }
        }.runTaskTimer((Plugin)this.plugin, 0L, 20L);
    }

    private void showVisualFeedback(MobSpawnPoint spawnPoint) {
        Location loc = spawnPoint.getLocation();
        World world = loc.getWorld();
        if (world == null) {
            return;
        }
        boolean playersNearby = world.getPlayers().stream().anyMatch(p -> p.getLocation().distance(loc) <= 20.0);
        if (!playersNearby) {
            return;
        }
        Location textLoc = loc.clone().add(0.0, 2.5, 0.0);
        String[] lines = new String[]{String.valueOf(ChatColor.YELLOW) + spawnPoint.getDisplayName(), String.valueOf(ChatColor.GRAY) + "Range: " + String.valueOf(ChatColor.WHITE) + (int)spawnPoint.getRadius() + " blocks", String.valueOf(this.getStatusColor(spawnPoint)) + spawnPoint.getStatus()};
        for (Player player : world.getPlayers()) {
            if (!(player.getLocation().distance(loc) <= 15.0) || !this.isPlayerLookingAt(player, loc)) continue;
            for (String line : lines) {
                player.sendMessage(line);
            }
        }
        this.showDetectionRadiusParticles(spawnPoint);
        this.showStatusParticles(spawnPoint);
    }

    private ChatColor getStatusColor(MobSpawnPoint spawnPoint) {
        if (!spawnPoint.isActive()) {
            return ChatColor.DARK_GRAY;
        }
        if (spawnPoint.isOnCooldown()) {
            return ChatColor.RED;
        }
        if (spawnPoint.isAtMaxCapacity()) {
            return ChatColor.YELLOW;
        }
        return ChatColor.GREEN;
    }

    private void showDetectionRadiusParticles(MobSpawnPoint spawnPoint) {
        Location center = spawnPoint.getLocation();
        World world = center.getWorld();
        if (world == null) {
            return;
        }
        double radius = spawnPoint.getRadius();
        Particle particle = spawnPoint.isBoss() ? Particle.FLAME : Particle.ENCHANT;
        for (int i = 0; i < 16; ++i) {
            double angle = Math.PI * 2 * (double)i / 16.0;
            double x = center.getX() + radius * Math.cos(angle);
            double z = center.getZ() + radius * Math.sin(angle);
            Location particleLoc = new Location(world, x, center.getY() + 0.1, z);
            world.spawnParticle(particle, particleLoc, 1, 0.0, 0.0, 0.0, 0.0);
        }
    }

    private void showStatusParticles(MobSpawnPoint spawnPoint) {
        Location loc = spawnPoint.getLocation().add(0.0, 1.0, 0.0);
        World world = loc.getWorld();
        if (world == null) {
            return;
        }
        if (!spawnPoint.isActive()) {
            world.spawnParticle(Particle.SMOKE, loc, 3, 0.2, 0.2, 0.2, 0.0);
        } else if (spawnPoint.isOnCooldown()) {
            world.spawnParticle(Particle.FLAME, loc, 5, 0.3, 0.3, 0.3, 0.0);
        } else if (spawnPoint.isAtMaxCapacity()) {
            world.spawnParticle(Particle.CRIT, loc, 3, 0.2, 0.2, 0.2, 0.0);
        } else {
            world.spawnParticle(Particle.HAPPY_VILLAGER, loc, 2, 0.1, 0.1, 0.1, 0.0);
        }
    }

    private boolean isPlayerLookingAt(Player player, Location target) {
        Vector playerDirection;
        Location playerLoc = player.getEyeLocation();
        double distance = playerLoc.distance(target);
        if (distance > 15.0) {
            return false;
        }
        Vector toTarget = target.toVector().subtract(playerLoc.toVector()).normalize();
        double dot = toTarget.dot(playerDirection = playerLoc.getDirection().normalize());
        double angle = Math.acos(dot);
        return angle < 0.52;
    }

    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        UUID entityId = entity.getUniqueId();
        for (MobSpawnPoint spawnPoint : this.spawnPoints.values()) {
            spawnPoint.removeSpawnedMob(entityId);
        }
    }

    public void addCustomMobSpawnPoint(CustomMobSpawnPoint customSpawnPoint) {
        String key = this.locationToKey(customSpawnPoint.getLocation());
        CustomMobConfig config = customSpawnPoint.getMobConfig();
        MobSpawnPoint spawnPoint = new MobSpawnPoint(customSpawnPoint.getLocation(), "custom:" + config.getFileName(), config.getSpawnRadius(), config.isBoss());
        spawnPoint.setCooldownMs((long)((config.getMinCooldown() + config.getMaxCooldown()) / 2) * 1000L);
        spawnPoint.setMaxConcurrentMobs(config.getMaxConcurrent());
        spawnPoint.setDisplayName(config.getDisplayName());
        this.spawnPoints.put(key, spawnPoint);
        this.saveSpawnPoints();
        this.plugin.getLogger().info("Added custom mob spawn point: " + config.getDisplayName() + " at " + this.locationToString(customSpawnPoint.getLocation()));
    }

    public void shutdown() {
        this.saveSpawnPoints();
        this.spawnPoints.clear();
        this.lastSpawnTimes.clear();
        this.activeSpawns.clear();
        for (MobSpawnPoint spawnPoint : this.spawnPoints.values()) {
            ArmorStand detectionEntity = spawnPoint.getDetectionEntity();
            if (detectionEntity == null || detectionEntity.isDead()) continue;
            detectionEntity.remove();
        }
    }
}

