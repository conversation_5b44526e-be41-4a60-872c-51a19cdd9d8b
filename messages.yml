# ------------------------------------------------------------------------------
# Messages for ApexDungeons
# All user facing strings are defined here for ease of localisation. Placeholders
# such as {name} or {count} will be replaced at runtime.
# ------------------------------------------------------------------------------

prefix: "§8[§6ApexDungeons§8] §7"

errors:
  notPlayer: "{prefix}You must be a player to use this command."
  noPermission: "{prefix}You don't have permission to do that."
  unknownCommand: "{prefix}Unknown command. Use /dgn for help."
  noDungeon: "{prefix}There is no dungeon called {name}."
  maxActive: "{prefix}Maximum active dungeons reached. Please remove one before creating another."

gui:
  mainTitle: "Apex Dungeons"
  adminTitle: "Apex Dungeons Admin"
  wandTitle: "Dungeon Architect Wand"
  createButton: "§aCreate Dungeon"
  manageButton: "§bActive Dungeons"
  helpButton: "§eHelp"
  closeButton: "§cClose"

wand:
  name: "§6Dungeon Architect Wand"
  lore:
    - "§7Right click: §fPreview & place room"
    - "§7Left click: §fRotate 90°"
    - "§7Sneak + scroll: §fChange height"
    - "§7Sneak + right click: §fRoom selector"