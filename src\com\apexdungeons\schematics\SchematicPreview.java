/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.World
 *  org.bukkit.entity.Player
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.scheduler.BukkitRunnable
 *  org.bukkit.scheduler.BukkitTask
 */
package com.apexdungeons.schematics;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.schematics.SchematicData;
import java.util.HashMap;
import java.util.Map;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

public class SchematicPreview {
    private final ApexDungeons plugin;
    private final Player player;
    private final SchematicData schematic;
    private Location baseLocation;
    private int rotation = 0;
    private BukkitTask previewTask;
    private boolean isActive = false;
    private static final Particle OUTLINE_PARTICLE = Particle.END_ROD;
    private static final Particle CORNER_PARTICLE = Particle.FLAME;
    private static final Particle CENTER_PARTICLE = Particle.ENCHANT;
    private static final Particle VALID_PARTICLE = Particle.HAPPY_VILLAGER;
    private static final Particle INVALID_PARTICLE = Particle.SMOKE;
    private static final int PREVIEW_DURATION = 300;
    private final Map<String, Location> movementOffsets = new HashMap<String, Location>();

    public SchematicPreview(ApexDungeons plugin, Player player, SchematicData schematic, Location location) {
        this.plugin = plugin;
        this.player = player;
        this.schematic = schematic;
        this.baseLocation = location.clone();
        this.initializeMovementOffsets();
    }

    private void initializeMovementOffsets() {
        this.movementOffsets.put("north", new Location(null, 0.0, 0.0, -1.0));
        this.movementOffsets.put("south", new Location(null, 0.0, 0.0, 1.0));
        this.movementOffsets.put("east", new Location(null, 1.0, 0.0, 0.0));
        this.movementOffsets.put("west", new Location(null, -1.0, 0.0, 0.0));
        this.movementOffsets.put("up", new Location(null, 0.0, 1.0, 0.0));
        this.movementOffsets.put("down", new Location(null, 0.0, -1.0, 0.0));
    }

    public void startPreview() {
        if (this.isActive) {
            this.stopPreview();
        }
        this.isActive = true;
        this.showPreviewInstructions();
        this.previewTask = new BukkitRunnable(){
            private int ticks = 0;

            public void run() {
                if (!SchematicPreview.this.isActive || !SchematicPreview.this.player.isOnline()) {
                    this.cancel();
                    return;
                }
                if (this.ticks % 4 == 0) {
                    SchematicPreview.this.displayPreview();
                }
                ++this.ticks;
                if (this.ticks >= 300) {
                    SchematicPreview.this.stopPreview();
                    SchematicPreview.this.player.sendMessage(String.valueOf(ChatColor.YELLOW) + "Preview timed out. Use the tool again to restart.");
                }
            }
        }.runTaskTimer((Plugin)this.plugin, 0L, 1L);
    }

    public void stopPreview() {
        this.isActive = false;
        if (this.previewTask != null) {
            this.previewTask.cancel();
            this.previewTask = null;
        }
    }

    private void displayPreview() {
        World world = this.baseLocation.getWorld();
        if (world == null) {
            return;
        }
        int[] dimensions = this.getRotatedDimensions();
        int width = dimensions[0];
        int height = dimensions[1];
        int depth = dimensions[2];
        this.showCornerMarkers(world, width, height, depth);
        this.showOutlineEdges(world, width, height, depth);
        this.showCenterPoint(world);
        this.showKeyBlocks(world);
        this.showInfoDisplay();
    }

    private void showCornerMarkers(World world, int width, int height, int depth) {
        Location[] corners;
        for (Location corner : corners = new Location[]{this.baseLocation.clone(), this.baseLocation.clone().add((double)(width - 1), 0.0, 0.0), this.baseLocation.clone().add(0.0, 0.0, (double)(depth - 1)), this.baseLocation.clone().add((double)(width - 1), 0.0, (double)(depth - 1)), this.baseLocation.clone().add(0.0, (double)(height - 1), 0.0), this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), 0.0), this.baseLocation.clone().add(0.0, (double)(height - 1), (double)(depth - 1)), this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), (double)(depth - 1))}) {
            world.spawnParticle(CORNER_PARTICLE, corner.add(0.5, 0.5, 0.5), 3, 0.1, 0.1, 0.1, 0.0);
        }
    }

    private void showOutlineEdges(World world, int width, int height, int depth) {
        this.showEdgeLine(world, this.baseLocation.clone(), this.baseLocation.clone().add((double)(width - 1), 0.0, 0.0));
        this.showEdgeLine(world, this.baseLocation.clone(), this.baseLocation.clone().add(0.0, 0.0, (double)(depth - 1)));
        this.showEdgeLine(world, this.baseLocation.clone().add((double)(width - 1), 0.0, 0.0), this.baseLocation.clone().add((double)(width - 1), 0.0, (double)(depth - 1)));
        this.showEdgeLine(world, this.baseLocation.clone().add(0.0, 0.0, (double)(depth - 1)), this.baseLocation.clone().add((double)(width - 1), 0.0, (double)(depth - 1)));
        this.showEdgeLine(world, this.baseLocation.clone().add(0.0, (double)(height - 1), 0.0), this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), 0.0));
        this.showEdgeLine(world, this.baseLocation.clone().add(0.0, (double)(height - 1), 0.0), this.baseLocation.clone().add(0.0, (double)(height - 1), (double)(depth - 1)));
        this.showEdgeLine(world, this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), 0.0), this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), (double)(depth - 1)));
        this.showEdgeLine(world, this.baseLocation.clone().add(0.0, (double)(height - 1), (double)(depth - 1)), this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), (double)(depth - 1)));
        this.showEdgeLine(world, this.baseLocation.clone(), this.baseLocation.clone().add(0.0, (double)(height - 1), 0.0));
        this.showEdgeLine(world, this.baseLocation.clone().add((double)(width - 1), 0.0, 0.0), this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), 0.0));
        this.showEdgeLine(world, this.baseLocation.clone().add(0.0, 0.0, (double)(depth - 1)), this.baseLocation.clone().add(0.0, (double)(height - 1), (double)(depth - 1)));
        this.showEdgeLine(world, this.baseLocation.clone().add((double)(width - 1), 0.0, (double)(depth - 1)), this.baseLocation.clone().add((double)(width - 1), (double)(height - 1), (double)(depth - 1)));
    }

    private void showEdgeLine(World world, Location start, Location end) {
        double distance = start.distance(end);
        int particles = Math.max(2, (int)(distance * 2.0));
        for (int i = 0; i <= particles; ++i) {
            double ratio = (double)i / (double)particles;
            Location point = start.clone().add((end.getX() - start.getX()) * ratio, (end.getY() - start.getY()) * ratio, (end.getZ() - start.getZ()) * ratio);
            world.spawnParticle(OUTLINE_PARTICLE, point.add(0.5, 0.5, 0.5), 1, 0.0, 0.0, 0.0, 0.0);
        }
    }

    private void showCenterPoint(World world) {
        int[] dimensions = this.getRotatedDimensions();
        Location center = this.baseLocation.clone().add((double)dimensions[0] / 2.0, (double)dimensions[1] / 2.0, (double)dimensions[2] / 2.0);
        world.spawnParticle(CENTER_PARTICLE, center, 5, 0.2, 0.2, 0.2, 0.0);
    }

    private void showKeyBlocks(World world) {
        Material[][][] blocks = this.getRotatedBlocks();
        int sampleRate = Math.max(1, Math.max(blocks.length, Math.max(blocks[0].length, blocks[0][0].length)) / 20);
        for (int y = 0; y < blocks.length; y += sampleRate) {
            for (int z = 0; z < blocks[y].length; z += sampleRate) {
                for (int x = 0; x < blocks[y][z].length; x += sampleRate) {
                    if (blocks[y][z][x] == Material.AIR) continue;
                    Location blockLoc = this.baseLocation.clone().add((double)x + 0.5, (double)y + 0.5, (double)z + 0.5);
                    world.spawnParticle(Particle.HAPPY_VILLAGER, blockLoc, 1, 0.0, 0.0, 0.0, 0.0);
                }
            }
        }
    }

    private void showInfoDisplay() {
    }

    private void showPreviewInstructions() {
        this.player.sendMessage(String.valueOf(ChatColor.GOLD) + "=== Schematic Preview Controls ===");
        this.player.sendMessage(String.valueOf(ChatColor.GREEN) + "R" + String.valueOf(ChatColor.WHITE) + " - Rotate 90\u00b0 clockwise");
        this.player.sendMessage(String.valueOf(ChatColor.GREEN) + "WASD" + String.valueOf(ChatColor.WHITE) + " - Move preview horizontally");
        this.player.sendMessage(String.valueOf(ChatColor.GREEN) + "Space/Shift" + String.valueOf(ChatColor.WHITE) + " - Move preview up/down");
        this.player.sendMessage(String.valueOf(ChatColor.GREEN) + "Left-click" + String.valueOf(ChatColor.WHITE) + " - Confirm placement");
        this.player.sendMessage(String.valueOf(ChatColor.GREEN) + "Right-click" + String.valueOf(ChatColor.WHITE) + " - Cancel preview");
        this.player.sendMessage(String.valueOf(ChatColor.GRAY) + "Preview will auto-cancel in 15 seconds");
    }

    public void rotate() {
        this.rotation = (this.rotation + 90) % 360;
        this.player.sendMessage(String.valueOf(ChatColor.GREEN) + "Rotated to " + this.rotation + "\u00b0");
        this.player.playSound(this.baseLocation, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);
    }

    public void move(String direction) {
        Location offset = this.movementOffsets.get(direction.toLowerCase());
        if (offset != null) {
            this.baseLocation.add(offset);
            this.player.sendMessage(String.valueOf(ChatColor.GREEN) + "Moved " + direction);
            this.player.playSound(this.baseLocation, Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.0f);
        }
    }

    private int[] getRotatedDimensions() {
        int width = this.schematic.getWidth();
        int height = this.schematic.getHeight();
        int depth = this.schematic.getDepth();
        if (this.rotation == 90 || this.rotation == 270) {
            return new int[]{depth, height, width};
        }
        return new int[]{width, height, depth};
    }

    private Material[][][] getRotatedBlocks() {
        Material[][][] original = this.schematic.getBlocks();
        switch (this.rotation) {
            case 90: {
                return this.rotateBlocks90(original);
            }
            case 180: {
                return this.rotateBlocks180(original);
            }
            case 270: {
                return this.rotateBlocks270(original);
            }
        }
        return original;
    }

    private Material[][][] rotateBlocks90(Material[][][] blocks) {
        int height = blocks.length;
        int depth = blocks[0].length;
        int width = blocks[0][0].length;
        Material[][][] rotated = new Material[height][width][depth];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    rotated[y][x][depth - 1 - z] = blocks[y][z][x];
                }
            }
        }
        return rotated;
    }

    private Material[][][] rotateBlocks180(Material[][][] blocks) {
        int height = blocks.length;
        int depth = blocks[0].length;
        int width = blocks[0][0].length;
        Material[][][] rotated = new Material[height][depth][width];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    rotated[y][depth - 1 - z][width - 1 - x] = blocks[y][z][x];
                }
            }
        }
        return rotated;
    }

    private Material[][][] rotateBlocks270(Material[][][] blocks) {
        int height = blocks.length;
        int depth = blocks[0].length;
        int width = blocks[0][0].length;
        Material[][][] rotated = new Material[height][width][depth];
        for (int y = 0; y < height; ++y) {
            for (int z = 0; z < depth; ++z) {
                for (int x = 0; x < width; ++x) {
                    rotated[y][width - 1 - x][z] = blocks[y][z][x];
                }
            }
        }
        return rotated;
    }

    private int countNonAirBlocks() {
        Material[][][] blocks;
        int count = 0;
        Material[][][] materialArray = blocks = this.schematic.getBlocks();
        int n = materialArray.length;
        for (int i = 0; i < n; ++i) {
            Material[][] layer;
            Material[][] materialArray2 = layer = materialArray[i];
            int n2 = materialArray2.length;
            for (int j = 0; j < n2; ++j) {
                Material[] row;
                for (Material block : row = materialArray2[j]) {
                    if (block == Material.AIR) continue;
                    ++count;
                }
            }
        }
        return count;
    }

    public boolean isActive() {
        return this.isActive;
    }

    public Location getBaseLocation() {
        return this.baseLocation.clone();
    }

    public int getRotation() {
        return this.rotation;
    }

    public SchematicData getSchematic() {
        return this.schematic;
    }
}

