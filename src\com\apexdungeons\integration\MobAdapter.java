/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.entity.LivingEntity
 */
package com.apexdungeons.integration;

import org.bukkit.Location;
import org.bukkit.entity.LivingEntity;

public interface MobAdapter {
    public LivingEntity spawnMob(String var1, Location var2);

    public LivingEntity spawnBoss(String var1, Location var2);

    public boolean isAvailable();

    public String getAdapterName();

    public String[] getAvailableMobs();

    public String[] getAvailableBosses();
}

