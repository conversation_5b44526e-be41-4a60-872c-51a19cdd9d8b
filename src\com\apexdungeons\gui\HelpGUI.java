/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.ChatColor
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.gui;

import com.apexdungeons.ApexDungeons;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

public class HelpGUI {
    public static void open(Player player) {
        player.sendMessage(String.valueOf(ChatColor.AQUA) + "=== Soaps Dungeons Help ===");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Use the main GUI to create new dungeons.");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Use the Architect Wand to preview and place rooms.");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "<PERSON><PERSON> can access advanced controls via /dgn admin.");
    }

    public static void open(Player player, ApexDungeons plugin) {
        HelpGUI.open(player);
    }
}

