# ApexDungeons Configuration Testing Guide

## Changes Made

### 1. **Theme Removal**
- ✅ Removed theme restrictions from `presets/procedural.yml`
- ✅ Created new `presets/building.yml` focused on custom building
- ✅ No more forced theme selection

### 2. **Instance System Enhancement**
- ✅ Added instancing configuration to `config.yml`
- ✅ Created detailed `instances.yml` for instance management
- ✅ Party-shared instances configured
- ✅ Per-player instance limits set

### 3. **MythicMobs Integration**
- ✅ Enhanced `config.yml` for forced MythicMobs usage
- ✅ Updated `mobs.yml` with MythicMobs format and level ranges
- ✅ Enhanced `bosses.yml` with MythicMobs spawn commands
- ✅ Created `mythic_integration.yml` for deep integration

## Testing Steps

### 1. **Test Basic Functionality**
```
/dgn help
/dgn create
```

### 2. **Test Instance Creation**
```
/dgn create building
# Should create separate instance for each player/party
```

### 3. **Test MythicMobs Integration**
```
# Check if MythicMobs mobs spawn correctly
# Verify level ranges work
# Test boss spawning with MM commands
```

### 4. **Test Party System**
```
# Create party
# Both players join same dungeon
# Verify they see each other
# Test solo players get separate instances
```

## Configuration Files Modified

1. **config.yml** - Enhanced MythicMobs integration and instancing
2. **presets/procedural.yml** - Removed themes, added instancing
3. **mobs.yml** - MythicMobs format with levels
4. **bosses.yml** - MythicMobs integration with spawn commands
5. **presets/building.yml** - NEW: Building-focused preset
6. **mythic_integration.yml** - NEW: Deep MythicMobs integration
7. **instances.yml** - NEW: Instance management configuration

## Next Steps

To complete the implementation, you would need to:

1. **Modify GUI Classes** (requires source code):
   - Remove theme selection from MainGUI
   - Simplify DungeonCreationGUI
   - Update PresetGUI to hide themes

2. **Enhance Instance Manager** (requires source code):
   - Implement party-shared instances
   - Add per-player instance tracking
   - Integrate with new configuration

3. **Improve MythicMobs Adapter** (requires source code):
   - Add direct command execution
   - Implement level range support
   - Add skill/ability integration

## Source Code Needed

To make the complete changes you requested, I need access to the Java source files (.java) to modify:
- GUI classes for theme removal
- DungeonInstanceManager for enhanced instancing
- MythicMobsAdapter for command integration
