/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.Material
 *  org.bukkit.World
 *  org.bukkit.block.Block
 *  org.bukkit.configuration.file.FileConfiguration
 *  org.bukkit.configuration.file.YamlConfiguration
 *  org.bukkit.entity.Player
 */
package com.apexdungeons.templates;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import com.apexdungeons.templates.DungeonTemplate;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

public class DungeonTemplateManager {
    private final ApexDungeons plugin;
    private final File templatesDir;
    private final Map<String, DungeonTemplate> loadedTemplates = new HashMap<String, DungeonTemplate>();

    public DungeonTemplateManager(ApexDungeons plugin) {
        this.plugin = plugin;
        this.templatesDir = new File(plugin.getDataFolder(), "templates");
        if (!this.templatesDir.exists()) {
            this.templatesDir.mkdirs();
        }
        this.loadAllTemplates();
    }

    public boolean saveDungeonAsTemplate(String dungeonName, String templateName, Player creator) {
        Location customExit;
        DungeonInstance dungeon = this.plugin.getDungeonManager().getDungeon(dungeonName);
        if (dungeon == null) {
            this.plugin.getLogger().warning("Cannot save template: Dungeon '" + dungeonName + "' not found");
            return false;
        }
        World dungeonWorld = dungeon.getOrigin().getWorld();
        if (dungeonWorld == null) {
            this.plugin.getLogger().warning("Cannot save template: Dungeon world is null");
            return false;
        }
        DungeonTemplate template = new DungeonTemplate(templateName);
        template.setCreator(creator.getName());
        template.setWorldName(dungeonWorld.getName());
        template.setDescription("Template created from dungeon: " + dungeonName);
        this.scanDungeonWorld(dungeonWorld, template);
        Location customSpawn = this.plugin.getDungeonConfig().getCustomSpawnLocation(dungeonName);
        if (customSpawn != null) {
            template.setSpawnLocation(customSpawn);
        }
        if ((customExit = this.plugin.getDungeonConfig().getCustomExitLocation(dungeonName)) != null) {
            template.setExitLocation(customExit);
        }
        FileConfiguration dungeonConfig = this.plugin.getDungeonConfig().getDungeonConfig(dungeonName);
        HashMap<String, Object> settings = new HashMap<String, Object>();
        settings.put("max_players", dungeonConfig.getInt("settings.max_players", 4));
        settings.put("time_limit", dungeonConfig.getInt("settings.time_limit", 0));
        settings.put("difficulty", dungeonConfig.getString("settings.difficulty", "normal"));
        template.setSettings(settings);
        return this.saveTemplate(template);
    }

    private void scanDungeonWorld(World world, DungeonTemplate template) {
        ArrayList<Location> startBlocks = new ArrayList<Location>();
        ArrayList<Location> endBlocks = new ArrayList<Location>();
        Location worldSpawn = world.getSpawnLocation();
        int centerX = worldSpawn.getBlockX();
        int centerZ = worldSpawn.getBlockZ();
        int radius = 500;
        this.plugin.getLogger().info("Scanning world " + world.getName() + " for dungeon blocks...");
        for (int x = centerX - radius; x <= centerX + radius; x += 16) {
            for (int z = centerZ - radius; z <= centerZ + radius; z += 16) {
                for (int y = 0; y <= world.getMaxHeight(); y += 8) {
                    Block block = world.getBlockAt(x, y, z);
                    if (block.getType() == Material.EMERALD_BLOCK) {
                        startBlocks.add(block.getLocation());
                        this.plugin.getLogger().info("Found start block at: " + x + "," + y + "," + z);
                        continue;
                    }
                    if (block.getType() != Material.DIAMOND_BLOCK) continue;
                    endBlocks.add(block.getLocation());
                    this.plugin.getLogger().info("Found end block at: " + x + "," + y + "," + z);
                }
            }
        }
        template.setStartBlocks(startBlocks);
        template.setEndBlocks(endBlocks);
        this.plugin.getLogger().info("Template scan complete: " + startBlocks.size() + " start blocks, " + endBlocks.size() + " end blocks");
    }

    public boolean saveTemplate(DungeonTemplate template) {
        try {
            File templateFile = new File(this.templatesDir, template.getName() + ".yml");
            YamlConfiguration config = new YamlConfiguration();
            template.saveToConfig((FileConfiguration)config);
            config.save(templateFile);
            this.loadedTemplates.put(template.getName(), template);
            this.plugin.getLogger().info("Saved dungeon template: " + template.getName());
            return true;
        }
        catch (IOException e) {
            this.plugin.getLogger().severe("Failed to save template " + template.getName() + ": " + e.getMessage());
            return false;
        }
    }

    public DungeonTemplate loadTemplate(String templateName) {
        if (this.loadedTemplates.containsKey(templateName)) {
            return this.loadedTemplates.get(templateName);
        }
        File templateFile = new File(this.templatesDir, templateName + ".yml");
        if (!templateFile.exists()) {
            return null;
        }
        try {
            YamlConfiguration config = YamlConfiguration.loadConfiguration((File)templateFile);
            DungeonTemplate template = DungeonTemplate.fromConfig((FileConfiguration)config);
            this.loadedTemplates.put(templateName, template);
            return template;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("Failed to load template " + templateName + ": " + e.getMessage());
            return null;
        }
    }

    public void loadAllTemplates() {
        this.loadedTemplates.clear();
        File[] files = this.templatesDir.listFiles((dir, name) -> name.endsWith(".yml"));
        if (files != null) {
            for (File file : files) {
                String templateName = file.getName().replace(".yml", "");
                this.loadTemplate(templateName);
            }
        }
        this.plugin.getLogger().info("Loaded " + this.loadedTemplates.size() + " dungeon templates");
    }

    public boolean applyTemplate(String templateName, String newDungeonName, World targetWorld) {
        DungeonTemplate template = this.loadTemplate(templateName);
        if (template == null) {
            this.plugin.getLogger().warning("Template not found: " + templateName);
            return false;
        }
        try {
            Location worldLoc;
            Location origin = new Location(targetWorld, 0.0, 64.0, 0.0);
            DungeonInstance newDungeon = new DungeonInstance(this.plugin, newDungeonName, targetWorld, origin, 1);
            FileConfiguration dungeonConfig = this.plugin.getDungeonConfig().getDungeonConfig(newDungeonName);
            for (Map.Entry<String, Object> setting : template.getSettings().entrySet()) {
                dungeonConfig.set("settings." + setting.getKey(), setting.getValue());
            }
            this.plugin.getDungeonConfig().saveDungeonConfig(newDungeonName, dungeonConfig);
            if (template.getSpawnLocation() != null) {
                Location spawnLoc = template.getSpawnLocation().clone();
                spawnLoc.setWorld(targetWorld);
                this.plugin.getDungeonConfig().setCustomSpawnLocation(newDungeonName, spawnLoc);
            }
            if (template.getExitLocation() != null) {
                Location exitLoc = template.getExitLocation().clone();
                exitLoc.setWorld(targetWorld);
                this.plugin.getDungeonConfig().setCustomExitLocation(newDungeonName, exitLoc);
            }
            for (Location startBlockLoc : template.getStartBlocks()) {
                worldLoc = startBlockLoc.clone();
                worldLoc.setWorld(targetWorld);
                worldLoc.getBlock().setType(Material.EMERALD_BLOCK);
            }
            for (Location endBlockLoc : template.getEndBlocks()) {
                worldLoc = endBlockLoc.clone();
                worldLoc.setWorld(targetWorld);
                worldLoc.getBlock().setType(Material.DIAMOND_BLOCK);
            }
            this.plugin.getDungeonManager().addDungeon(newDungeon);
            this.plugin.getWorldManager().registerDungeonWorld(newDungeonName, targetWorld);
            this.plugin.getLogger().info("Applied template '" + templateName + "' to create dungeon '" + newDungeonName + "'");
            return true;
        }
        catch (Exception e) {
            this.plugin.getLogger().severe("Failed to apply template " + templateName + ": " + e.getMessage());
            return false;
        }
    }

    public List<String> getTemplateNames() {
        return new ArrayList<String>(this.loadedTemplates.keySet());
    }

    public DungeonTemplate getTemplate(String name) {
        return this.loadedTemplates.get(name);
    }

    public boolean deleteTemplate(String templateName) {
        File templateFile = new File(this.templatesDir, templateName + ".yml");
        if (templateFile.exists()) {
            boolean deleted = templateFile.delete();
            if (deleted) {
                this.loadedTemplates.remove(templateName);
                this.plugin.getLogger().info("Deleted template: " + templateName);
            }
            return deleted;
        }
        return false;
    }

    public boolean templateExists(String templateName) {
        return this.loadedTemplates.containsKey(templateName) || new File(this.templatesDir, templateName + ".yml").exists();
    }
}

