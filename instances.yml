# Dungeon Instance Management Configuration
# Controls how dungeon instances are created and managed

# Core instance settings
instances:
  enabled: true
  max_total_instances: 20
  max_per_player: 2
  max_per_party: 1
  
# Instance creation
creation:
  # World naming pattern
  world_prefix: "dungeon_"
  world_suffix: "_{player}_{timestamp}"
  
  # Instance types
  types:
    solo:
      max_players: 1
      party_shared: false
    party:
      max_players: 8
      party_shared: true
    building:
      max_players: 4
      party_shared: true
      creative_mode: true
      
# Instance lifecycle
lifecycle:
  # Cleanup settings
  cleanup_delay_minutes: 10
  auto_cleanup: true
  save_on_cleanup: false
  
  # Persistence
  persistent: false
  save_progress: true
  
# Party integration
party:
  enabled: true
  share_instances: true
  leader_controls: true
  invite_to_instance: true
  
  # Party permissions
  permissions:
    invite: true
    kick: true
    transfer_leadership: true
    
# World management
world:
  # World settings for instances
  difficulty: "NORMAL"
  pvp: false
  monsters: true
  animals: false
  
  # Performance settings
  view_distance: 8
  simulation_distance: 6
  
  # Building mode settings
  building_mode:
    flight: true
    creative: true
    worldedit: true
    unlimited_blocks: true

# Instance monitoring
monitoring:
  track_performance: true
  log_player_actions: true
  auto_restart_on_error: true
  
# Integration with other systems
integration:
  worldguard: true
  multiverse: true
  plotsquared: false
