# ------------------------------------------------------------------------------
# ApexDungeons Main Configuration
# This file is generated on first run with sane defaults. You can tweak these
# values to customise how dungeons generate, their performance budgets and
# behaviour. See the documentation in docs/ for more details.
# ------------------------------------------------------------------------------

# List of worlds where dungeon generation is permitted. If empty, all worlds are
# allowed. The world names must exactly match those found in server.properties.
worlds:
  allowed:
    - world
    - world_nether
    - world_the_end
    - dungeons

generation:
  # Maximum number of rooms a dungeon may contain. Larger values produce longer
  # winding dungeons but take more time to generate.
  maxRooms: 25
  # Chance (0.0‑1.0) for a branch to be created at each step. Higher values
  # result in more labyrinthine structures.
  branchChance: 0.3
  # Maximum number of active dungeons at once. When reached, new dungeons will
  # refuse to start until existing ones are removed.
  maxActive: 4

performance:
  # Maximum number of blocks placed per server tick. Keeping this low prevents
  # lag spikes but slows generation. Adjust based on your server's CPU.
  blocksPerTick: 8000
  # How far in chunks around a dungeon area should be preloaded before any
  # generation begins. Preloading reduces chunk load hitches when exploring.
  chunkPreloadRadius: 2

loot:
  # Default loot tables applied to all rooms which do not specify their own.
  tables:
    default:
      - basic
      - rare

mobs:
  # Which adapter should be used for spawning mobs. AUTO picks MythicMobs if
  # present, otherwise falls back to vanilla.
  adapter: AUTO

boss:
  # Default boss definition to spawn at the end of a dungeon if none is
  # explicitly defined. You can create your own bosses in bosses.yml.
  default: wither_basic

integration:
  mythicMobs:
    # Whether MythicMobs integration is enabled. When AUTO (default) the
    # integration is automatically enabled if MythicMobs is installed.
    enabled: AUTO