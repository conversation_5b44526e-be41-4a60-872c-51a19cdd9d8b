/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Location
 *  org.bukkit.configuration.file.FileConfiguration
 */
package com.apexdungeons.templates;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;

public class DungeonTemplate
implements Serializable {
    private String name;
    private String description;
    private String creator;
    private long createdTime;
    private String worldName;
    private Location spawnLocation;
    private Location exitLocation;
    private List<Location> startBlocks;
    private List<Location> endBlocks;
    private Map<String, Object> settings;
    private List<PortalData> portals;
    private String schematicFile;

    public DungeonTemplate(String name) {
        this.name = name;
        this.description = "";
        this.creator = "";
        this.createdTime = System.currentTimeMillis();
        this.startBlocks = new ArrayList<Location>();
        this.endBlocks = new ArrayList<Location>();
        this.settings = new HashMap<String, Object>();
        this.portals = new ArrayList<PortalData>();
    }

    public static DungeonTemplate fromConfig(FileConfiguration config) {
        Location loc;
        String name = config.getString("template.name", "Unknown");
        DungeonTemplate template = new DungeonTemplate(name);
        template.description = config.getString("template.description", "");
        template.creator = config.getString("template.creator", "");
        template.createdTime = config.getLong("template.created", System.currentTimeMillis());
        template.worldName = config.getString("template.world", "");
        template.schematicFile = config.getString("template.schematic", "");
        if (config.contains("spawn")) {
            template.spawnLocation = DungeonTemplate.loadLocationFromConfig(config, "spawn");
        }
        if (config.contains("exit")) {
            template.exitLocation = DungeonTemplate.loadLocationFromConfig(config, "exit");
        }
        if (config.contains("start_blocks")) {
            List startBlocksList = config.getMapList("start_blocks");
            for (Map blockData : startBlocksList) {
                loc = DungeonTemplate.loadLocationFromMap(blockData);
                if (loc == null) continue;
                template.startBlocks.add(loc);
            }
        }
        if (config.contains("end_blocks")) {
            List endBlocksList = config.getMapList("end_blocks");
            for (Map blockData : endBlocksList) {
                loc = DungeonTemplate.loadLocationFromMap(blockData);
                if (loc == null) continue;
                template.endBlocks.add(loc);
            }
        }
        if (config.contains("settings")) {
            template.settings = config.getConfigurationSection("settings").getValues(false);
        }
        if (config.contains("portals")) {
            List portalsList = config.getMapList("portals");
            for (Map portalData : portalsList) {
                PortalData portal = PortalData.fromMap(portalData);
                if (portal == null) continue;
                template.portals.add(portal);
            }
        }
        return template;
    }

    public void saveToConfig(FileConfiguration config) {
        config.set("template.name", (Object)this.name);
        config.set("template.description", (Object)this.description);
        config.set("template.creator", (Object)this.creator);
        config.set("template.created", (Object)this.createdTime);
        config.set("template.world", (Object)this.worldName);
        config.set("template.schematic", (Object)this.schematicFile);
        if (this.spawnLocation != null) {
            this.saveLocationToConfig(config, "spawn", this.spawnLocation);
        }
        if (this.exitLocation != null) {
            this.saveLocationToConfig(config, "exit", this.exitLocation);
        }
        ArrayList<Map<String, Object>> startBlocksList = new ArrayList<Map<String, Object>>();
        for (Location location : this.startBlocks) {
            startBlocksList.add(this.locationToMap(location));
        }
        config.set("start_blocks", startBlocksList);
        ArrayList<Map<String, Object>> endBlocksList = new ArrayList<Map<String, Object>>();
        for (Location location : this.endBlocks) {
            endBlocksList.add(this.locationToMap(location));
        }
        config.set("end_blocks", endBlocksList);
        if (!this.settings.isEmpty()) {
            for (Map.Entry<String, Object> entry : this.settings.entrySet()) {
                config.set("settings." + entry.getKey(), entry.getValue());
            }
        }
        ArrayList<Map<String, Object>> arrayList = new ArrayList<Map<String, Object>>();
        for (PortalData portal : this.portals) {
            arrayList.add(portal.toMap());
        }
        config.set("portals", arrayList);
    }

    private static Location loadLocationFromConfig(FileConfiguration config, String path) {
        if (!config.contains(path + ".world")) {
            return null;
        }
        double x = config.getDouble(path + ".x", 0.0);
        double y = config.getDouble(path + ".y", 64.0);
        double z = config.getDouble(path + ".z", 0.0);
        float yaw = (float)config.getDouble(path + ".yaw", 0.0);
        float pitch = (float)config.getDouble(path + ".pitch", 0.0);
        return new Location(null, x, y, z, yaw, pitch);
    }

    private static Location loadLocationFromMap(Map<?, ?> map) {
        if (!(map.containsKey("x") && map.containsKey("y") && map.containsKey("z"))) {
            return null;
        }
        double x = ((Number)map.get("x")).doubleValue();
        double y = ((Number)map.get("y")).doubleValue();
        double z = ((Number)map.get("z")).doubleValue();
        float yaw = map.containsKey("yaw") ? ((Number)map.get("yaw")).floatValue() : 0.0f;
        float pitch = map.containsKey("pitch") ? ((Number)map.get("pitch")).floatValue() : 0.0f;
        return new Location(null, x, y, z, yaw, pitch);
    }

    private void saveLocationToConfig(FileConfiguration config, String path, Location location) {
        config.set(path + ".x", (Object)location.getX());
        config.set(path + ".y", (Object)location.getY());
        config.set(path + ".z", (Object)location.getZ());
        config.set(path + ".yaw", (Object)Float.valueOf(location.getYaw()));
        config.set(path + ".pitch", (Object)Float.valueOf(location.getPitch()));
    }

    private Map<String, Object> locationToMap(Location location) {
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("x", location.getX());
        map.put("y", location.getY());
        map.put("z", location.getZ());
        map.put("yaw", Float.valueOf(location.getYaw()));
        map.put("pitch", Float.valueOf(location.getPitch()));
        return map;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreator() {
        return this.creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public long getCreatedTime() {
        return this.createdTime;
    }

    public void setCreatedTime(long createdTime) {
        this.createdTime = createdTime;
    }

    public String getWorldName() {
        return this.worldName;
    }

    public void setWorldName(String worldName) {
        this.worldName = worldName;
    }

    public Location getSpawnLocation() {
        return this.spawnLocation;
    }

    public void setSpawnLocation(Location spawnLocation) {
        this.spawnLocation = spawnLocation;
    }

    public Location getExitLocation() {
        return this.exitLocation;
    }

    public void setExitLocation(Location exitLocation) {
        this.exitLocation = exitLocation;
    }

    public List<Location> getStartBlocks() {
        return this.startBlocks;
    }

    public void setStartBlocks(List<Location> startBlocks) {
        this.startBlocks = startBlocks;
    }

    public List<Location> getEndBlocks() {
        return this.endBlocks;
    }

    public void setEndBlocks(List<Location> endBlocks) {
        this.endBlocks = endBlocks;
    }

    public Map<String, Object> getSettings() {
        return this.settings;
    }

    public void setSettings(Map<String, Object> settings) {
        this.settings = settings;
    }

    public List<PortalData> getPortals() {
        return this.portals;
    }

    public void setPortals(List<PortalData> portals) {
        this.portals = portals;
    }

    public String getSchematicFile() {
        return this.schematicFile;
    }

    public void setSchematicFile(String schematicFile) {
        this.schematicFile = schematicFile;
    }

    public static class PortalData {
        private Location corner1;
        private Location corner2;
        private String destination;
        private String destinationType;

        public PortalData(Location corner1, Location corner2, String destination, String destinationType) {
            this.corner1 = corner1;
            this.corner2 = corner2;
            this.destination = destination;
            this.destinationType = destinationType;
        }

        public static PortalData fromMap(Map<?, ?> map) {
            if (!map.containsKey("corner1") || !map.containsKey("corner2")) {
                return null;
            }
            Location corner1 = PortalData.loadLocationFromMap((Map)map.get("corner1"));
            Location corner2 = PortalData.loadLocationFromMap((Map)map.get("corner2"));
            String destination = (String)map.get("destination");
            String destinationType = (String)map.get("destination_type");
            return new PortalData(corner1, corner2, destination, destinationType);
        }

        public Map<String, Object> toMap() {
            HashMap<String, Object> map = new HashMap<String, Object>();
            map.put("corner1", this.locationToMap(this.corner1));
            map.put("corner2", this.locationToMap(this.corner2));
            map.put("destination", this.destination);
            map.put("destination_type", this.destinationType);
            return map;
        }

        private static Location loadLocationFromMap(Map<?, ?> map) {
            double x = ((Number)map.get("x")).doubleValue();
            double y = ((Number)map.get("y")).doubleValue();
            double z = ((Number)map.get("z")).doubleValue();
            return new Location(null, x, y, z);
        }

        private Map<String, Object> locationToMap(Location location) {
            HashMap<String, Object> map = new HashMap<String, Object>();
            map.put("x", location.getX());
            map.put("y", location.getY());
            map.put("z", location.getZ());
            return map;
        }

        public Location getCorner1() {
            return this.corner1;
        }

        public Location getCorner2() {
            return this.corner2;
        }

        public String getDestination() {
            return this.destination;
        }

        public String getDestinationType() {
            return this.destinationType;
        }
    }
}

