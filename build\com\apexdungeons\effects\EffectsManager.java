/*
 * Decompiled with CFR 0.152.
 * 
 * Could not load the following classes:
 *  org.bukkit.Bukkit
 *  org.bukkit.ChatColor
 *  org.bukkit.Location
 *  org.bukkit.Particle
 *  org.bukkit.Sound
 *  org.bukkit.World
 *  org.bukkit.entity.Player
 *  org.bukkit.plugin.Plugin
 *  org.bukkit.scheduler.BukkitRunnable
 */
package com.apexdungeons.effects;

import com.apexdungeons.ApexDungeons;
import com.apexdungeons.gen.DungeonInstance;
import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

public class EffectsManager {
    private final ApexDungeons plugin;

    public EffectsManager(ApexDungeons plugin) {
        this.plugin = plugin;
    }

    public void playDungeonCreationEffects(final Player player, String dungeonName) {
        final Location location = player.getLocation();
        player.playSound(location, Sound.BLOCK_BEACON_ACTIVATE, 1.0f, 1.2f);
        player.playSound(location, Sound.ENTITY_EXPERIENCE_ORB_PICKUP, 1.0f, 0.8f);
        this.spawnParticleCircle(location, Particle.ENCHANT, 30, 3.0, 0.5);
        this.spawnParticleCircle(location, Particle.END_ROD, 20, 2.0, 1.0);
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2726 " + String.valueOf(ChatColor.GREEN) + "Dungeon Creation Started!");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Creating: " + String.valueOf(ChatColor.AQUA) + dungeonName);
        new BukkitRunnable(){

            public void run() {
                player.playSound(location, Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
                EffectsManager.this.spawnParticleExplosion(location, Particle.FIREWORK, 50, 2.0);
            }
        }.runTaskLater((Plugin)this.plugin, 40L);
    }

    public void playDungeonCompletionEffects(final Player player, DungeonInstance dungeon) {
        final Location location = player.getLocation();
        player.playSound(location, Sound.UI_TOAST_CHALLENGE_COMPLETE, 1.0f, 1.0f);
        player.playSound(location, Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.2f);
        this.spawnParticleExplosion(location, Particle.FIREWORK, 100, 3.0);
        this.spawnParticleExplosion(location, Particle.TOTEM_OF_UNDYING, 50, 2.0);
        player.sendMessage("");
        player.sendMessage(String.valueOf(ChatColor.GOLD) + "\u2605 \u2605 \u2605 " + String.valueOf(ChatColor.GREEN) + "DUNGEON COMPLETED!" + String.valueOf(ChatColor.GOLD) + " \u2605 \u2605 \u2605");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Dungeon: " + String.valueOf(ChatColor.AQUA) + dungeon.getDisplayName());
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Rooms: " + String.valueOf(ChatColor.YELLOW) + dungeon.getRoomCount());
        player.sendMessage("");
        new BukkitRunnable(){
            int count = 0;

            public void run() {
                if (this.count >= 5) {
                    this.cancel();
                    return;
                }
                player.playSound(location, Sound.ENTITY_FIREWORK_ROCKET_BLAST, 0.5f, 1.0f + (float)this.count * 0.2f);
                EffectsManager.this.spawnParticleCircle(location.clone().add(0.0, (double)this.count, 0.0), Particle.FIREWORK, 20, 1.0 + (double)this.count, 0.1);
                ++this.count;
            }
        }.runTaskTimer((Plugin)this.plugin, 0L, 10L);
    }

    public void playDungeonEntryEffects(Player player, DungeonInstance dungeon) {
        Location location = player.getLocation();
        player.playSound(location, Sound.BLOCK_PORTAL_TRAVEL, 1.0f, 0.8f);
        player.playSound(location, Sound.AMBIENT_CAVE, 0.5f, 1.2f);
        this.spawnParticleSpiral(location, Particle.PORTAL, 30, 2.0, 3.0);
        this.spawnParticleCircle(location, Particle.LARGE_SMOKE, 15, 1.5, 0.1);
        player.sendMessage(String.valueOf(ChatColor.DARK_PURPLE) + "\u26a1 " + String.valueOf(ChatColor.LIGHT_PURPLE) + "Entering Dungeon...");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Welcome to: " + String.valueOf(ChatColor.AQUA) + dungeon.getDisplayName());
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Creator: " + String.valueOf(ChatColor.YELLOW) + dungeon.getCreator());
        player.sendTitle(String.valueOf(ChatColor.DARK_PURPLE) + "Entering Dungeon", String.valueOf(ChatColor.AQUA) + dungeon.getDisplayName(), 10, 40, 10);
    }

    public void playDungeonExitEffects(Player player, String dungeonName) {
        Location location = player.getLocation();
        player.playSound(location, Sound.BLOCK_PORTAL_TRAVEL, 1.0f, 1.2f);
        player.playSound(location, Sound.ENTITY_ENDERMAN_TELEPORT, 0.8f, 1.0f);
        this.spawnParticleSpiral(location, Particle.END_ROD, 25, 1.5, 2.5);
        this.spawnParticleCircle(location, Particle.CLOUD, 20, 2.0, 0.2);
        player.sendMessage(String.valueOf(ChatColor.GREEN) + "\u2b05 " + String.valueOf(ChatColor.YELLOW) + "Returning to Main World");
        player.sendMessage(String.valueOf(ChatColor.GRAY) + "Left dungeon: " + String.valueOf(ChatColor.AQUA) + dungeonName);
        player.sendTitle(String.valueOf(ChatColor.GREEN) + "Returning", String.valueOf(ChatColor.YELLOW) + "Main World", 10, 30, 10);
    }

    public void playRoomGenerationEffects(Location location, String roomName) {
        World world = location.getWorld();
        if (world == null) {
            return;
        }
        this.spawnParticleCircle(location, Particle.BLOCK, 40, 2.0, 0.3);
        this.spawnParticleExplosion(location, Particle.SMOKE, 30, 1.5);
        List<Player> nearbyPlayers = world.getNearbyEntities(location, 20.0, 20.0, 20.0).stream().filter(entity -> entity instanceof Player).map(entity -> (Player)entity).toList();
        for (Player player : nearbyPlayers) {
            player.playSound(location, Sound.BLOCK_STONE_BREAK, 0.8f, 0.8f);
            player.playSound(location, Sound.BLOCK_GRAVEL_BREAK, 0.6f, 1.2f);
        }
    }

    private void spawnParticleCircle(Location center, Particle particle, int count, double radius, double height) {
        World world = center.getWorld();
        if (world == null) {
            return;
        }
        for (int i = 0; i < count; ++i) {
            double angle = Math.PI * 2 * (double)i / (double)count;
            double x = center.getX() + radius * Math.cos(angle);
            double z = center.getZ() + radius * Math.sin(angle);
            double y = center.getY() + height;
            Location particleLocation = new Location(world, x, y, z);
            world.spawnParticle(particle, particleLocation, 1, 0.0, 0.0, 0.0, 0.0);
        }
    }

    private void spawnParticleExplosion(Location center, Particle particle, int count, double spread) {
        World world = center.getWorld();
        if (world == null) {
            return;
        }
        world.spawnParticle(particle, center, count, spread, spread, spread, 0.1);
    }

    private void spawnParticleSpiral(final Location center, final Particle particle, final int count, final double radius, final double height) {
        final World world = center.getWorld();
        if (world == null) {
            return;
        }
        new BukkitRunnable(this){
            int step = 0;

            public void run() {
                if (this.step >= count) {
                    this.cancel();
                    return;
                }
                double angle = Math.PI * 2 * (double)this.step / 10.0;
                double currentRadius = radius * (1.0 - (double)this.step / (double)count);
                double currentHeight = height * (double)this.step / (double)count;
                double x = center.getX() + currentRadius * Math.cos(angle);
                double z = center.getZ() + currentRadius * Math.sin(angle);
                double y = center.getY() + currentHeight;
                Location particleLocation = new Location(world, x, y, z);
                world.spawnParticle(particle, particleLocation, 1, 0.0, 0.0, 0.0, 0.0);
                ++this.step;
            }
        }.runTaskTimer((Plugin)this.plugin, 0L, 1L);
    }

    public void broadcastDungeonEvent(String message, DungeonInstance dungeon) {
        String formattedMessage = String.valueOf(ChatColor.GOLD) + "[Dungeons] " + String.valueOf(ChatColor.RESET) + message;
        for (Player player : Bukkit.getOnlinePlayers()) {
            player.sendMessage(formattedMessage);
        }
        this.plugin.getLogger().info("Dungeon Event: " + message);
    }

    public void playAmbientDungeonSounds(final Player player) {
        final Location location = player.getLocation();
        new BukkitRunnable(){

            public void run() {
                if (!EffectsManager.this.plugin.getWorldManager().isDungeonWorld(player.getWorld())) {
                    this.cancel();
                    return;
                }
                Sound[] ambientSounds = new Sound[]{Sound.AMBIENT_CAVE, Sound.BLOCK_FIRE_AMBIENT, Sound.ENTITY_BAT_AMBIENT};
                Sound randomSound = ambientSounds[(int)(Math.random() * (double)ambientSounds.length)];
                player.playSound(location, randomSound, 0.3f, 0.8f + (float)(Math.random() * (double)0.4f));
            }
        }.runTaskTimer((Plugin)this.plugin, 200L, 400L + (long)(Math.random() * 400.0));
    }
}

